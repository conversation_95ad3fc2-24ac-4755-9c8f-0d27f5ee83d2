# Broadcast Session Feature

## Overview
The Broadcast Session feature allows users to create public ALE sessions without any authentication. This enables quick, zero-friction access to create broadcast stations directly from a project.

## Endpoint

### Create Broadcast Session
**POST** `/session/CreateBroadcast`

**Authentication:** None required (public endpoint)

**Request Body:**
```json
{
  "project_id": "string (required)",
  "organization_id": "string (required)",
  "name": "string (required, 1-100 characters)",
  "duration": "number (optional, 1-240 minutes, default: 30)",
  "description": "string (optional, max 500 characters)"
}
```

**Success Response (201):**
```json
{
  "status": 1,
  "data": {
    "_id": "ObjectId",
    "status": "active",
    "pixel_duration_minutes": 0,
    "code": "string",
    "start": "ISO string",
    "type": "ale",
    "scheduled_end_time": "ISO string",
    "invite_link": "string",
    "organization_id": "string",
    "user_id": "string (format: guest_<uuid>)",
    "source": "broadcast",
    "last_interaction_time": "ISO string",
    "is_scheduled": false,
    "schedule_time": null,
    "end_time": "ISO string",
    "instance_start_time": "ISO string",
    "instance_end_time": "ISO string",
    "participants": [],
    "is_pixelstreaming_active": false,
    "is_reserved": false,
    "host_name": "string"
  }
}
```

**Error Responses:**

- **400 Bad Request** - Validation error
```json
{
  "status": 0,
  "error": "Error message"
}
```

- **404 Not Found** - Project not found
```json
{
  "status": 0,
  "error": "Project not found"
}
```

- **500 Internal Server Error** - Server error
```json
{
  "status": 0,
  "error": "Error while creating broadcast session: ..."
}
```

## How It Works

### The Broadcast Flow

1. **HOST** visits PropVR viewer page (e.g., `https://view.propvr.tech/f2eon9/projectscene/...`)
   - Page contains `organization_id` and `project_id` in context
2. **HOST** creates broadcast session via API
   - Provides `project_id`, `organization_id`, and their `name`
   - No authentication required!
3. **API creates session**
   - Generates temporary `broadcast_host_id` for the HOST
   - Creates DEFAULT session with `broadcast` tag
4. **HOST receives** `invite_link` and `session_id`
5. **HOST shares** invite link with GUESTS
6. **GUESTS join** via the link
   - Use existing `CreateLead` API with `session_id`
   - Leads are automatically associated with the `broadcast_host_id`

### Broadcast Host IDs

Broadcast hosts (unauthenticated session creators) get a temporary user ID:
- **Format:** `guest_<uuid>`
- **Purpose:** Acts as the HOST's user_id in the session
- **Persistence:** When guests join via CreateLead API, their leads are linked to this broadcast_host_id
- **No Firebase Auth:** These IDs don't require Firebase authentication

## Example Usage

### Using cURL
```bash
curl -X POST http://localhost:3001/session/CreateBroadcast \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "6662a98d63ea238bc171bfeb",
    "organization_id": "f2eon9",
    "name": "John Doe",
    "duration": 60,
    "description": "Live broadcast demo"
  }'
```

### Using JavaScript/Fetch
```javascript
// Example: Extract from PropVR viewer URL
// https://view.propvr.tech/f2eon9/projectscene/6662a98d63ea238bc171bfeb/66d59ae8d4d4351ab7db7bfe
const organization_id = 'f2eon9'; // From URL
const project_id = '6662a98d63ea238bc171bfeb'; // From URL

const response = await fetch('http://localhost:3001/session/CreateBroadcast', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    project_id: project_id,
    organization_id: organization_id,
    name: 'John Doe',
    duration: 60,
    description: 'Live broadcast demo'
  })
});

const data = await response.json();
console.log('Broadcast session created:', data);
// data.data.invite_link - Share this with guests
// data.data.session_id - Used by guests when joining via CreateLead API
```

## Integration with CreateLead API

### No Changes Required! ✅

The existing **CreateLead API** works perfectly with broadcast sessions:

```javascript
// Guest joins broadcast session
POST /lead/CreateLead
{
  "session_id": "...",  // From broadcast session
  "organization_id": "...",
  "name": "Guest Name",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "type": "project",
  "source": "sales_session"
}
```

**What happens:**
1. CreateLead fetches session data using `session_id`
2. Lead is automatically linked to `broadcast_host_id` (from session.user_id)
3. Guest receives invite email via task queue
4. All lead tracking works normally

## Key Features

✅ **No Authentication** - Zero friction for hosts  
✅ **Works with Any Project** - No project validation restrictions  
✅ **Works with Existing APIs** - CreateLead API requires no changes  
✅ **Broadcast Host IDs** - Creates temporary user IDs for unauthenticated hosts  
✅ **Session Tags** - Tagged as `broadcast` for easy filtering  
✅ **Lead Tracking** - Guests are automatically linked to broadcast host  

## Security Considerations

1. **Input Validation**: All inputs are validated using express-validator
2. **Rate Limiting**: Consider implementing rate limiting to prevent abuse
3. **Logging**: All broadcast session creations are logged for monitoring

## Files Modified/Created

### New Files
- `src/controllers/sessions/CreateBroadcast/index.ts` - Main controller
- `src/controllers/sessions/CreateBroadcast/CreateBroadcastValidator.ts` - Input validator
- `src/helpers/guestUser.ts` - Guest user ID generation utilities

### Modified Files
- `src/types/session.ts` - Added `createBroadcastSessionInput` type
- `src/routes/sessionRoutes.ts` - Added public broadcast route

## Future Enhancements

- Add rate limiting middleware
- Add project-level `broadcast_enabled` flag
- Add broadcast session analytics
- Add ability to specify custom session types
- Add webhook notifications for broadcast session events

## Testing

See `BROADCAST_FEATURE.md` for testing instructions.

## Troubleshooting

### "Project not found"
- Ensure the project_id is correct and exists in the database
- Verify the project exists in one of the organization's project collections

### "Project does not support broadcast sessions"
- The project must have `ale` in its `experience` array
- Update the project settings to enable ALE experience

### "Validation error"
- Check that all required fields are provided
- Ensure `name` is between 1-100 characters
- Ensure `duration` is between 1-240 minutes if provided


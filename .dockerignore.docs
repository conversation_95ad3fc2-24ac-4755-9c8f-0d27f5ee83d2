# Dockerignore for documentation build
# Exclude everything first, then include what we need
**

# Include only what's needed for documentation
!package*.json
!redocly.yaml
!docs/
!src/controllers/**/*redoc.yaml

# Exclude build artifacts and cache
docs/dist/
docs/*bundled.yaml
node_modules/
npm-debug.log*
.npm
.nyc_output

# Exclude development files
.git/
.gitignore
*.md
README*
CHANGELOG*
.env*
.vscode/
.idea/

# Exclude test and build files
test/
tests/
*.test.js
*.test.ts
coverage/
.coverage
build/
dist/

# Exclude other source files not needed for docs
src/**/*.ts
src/**/*.js
!src/controllers/**/*redoc.yaml
@echo off
setlocal enabledelayedexpansion

for %%F in ("%SOURCE_DIR%\*.ts") do (
    set "FILENAME=%%~nF"
    set "NEW_DIR=%SOURCE_DIR%\!FILENAME!"
    set "NEW_FILE=!NEW_DIR!\index.ts"
    set "TEST_FILE=!NEW_DIR!\!FILENAME!.test.js"

    if not exist "!NEW_DIR!" mkdir "!NEW_DIR!"
    copy "%%F" "!NEW_FILE!"

    REM Create an empty test file
    if not exist "!TEST_FILE!" type nul > "!TEST_FILE!"
)

echo Done.

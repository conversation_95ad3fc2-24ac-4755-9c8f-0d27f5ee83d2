# Redocly configuration for PropVR Webhooks API Documentation
extends:
  - recommended

apis:
  propvr-webhooks@v1:
    root: docs/webhooks-api-main.yaml
  propvr-units@v1:
    root: src/controllers/units/webhooks/units_redoc.yaml
  propvr-users@v1:
    root: src/controllers/user/webhooks/users_redoc.yaml
  propvr-sessions@v1:
    root: src/controllers/sessions/webhooks/sessions_redoc.yaml
  propvr-projects@v1:
    root: src/controllers/projects/webhooks/projects_redoc.yaml

rules:
  # Adjust rules as needed for your API
  operation-description: error
  operation-summary: error
  path-declaration-must-exist: error
  no-path-trailing-slash: error
  operation-operationId: error  # OperationIds are now present
  operation-operationId-unique: error
  operation-parameters-unique: error
  path-parameters-defined: error
  tag-description: off  # We have good tag descriptions
  info-license: off     # Optional for internal APIs
    
theme:
  openapi:
    generateCodeSamples:
      languages:
        - lang: curl
          label: cURL
        - lang: JavaScript
          label: JavaScript (fetch)
        - lang: Python
          label: Python (requests)
        - lang: PHP
          label: PHP (cURL)
        - lang: Java
          label: Java (OkHttp)
    requiredPropsFirst: true
    pathInMiddlePanel: true
    hideDownloadButton: false
    showExtensions: true
    theme:
      colors:
        primary:
          main: '#1976d2'
        text:
          primary: '#333'
      typography:
        fontSize: '14px'
        fontFamily: 'Roboto, sans-serif'
        code:
          fontSize: '16px'  # Larger font for code samples
          fontFamily: 'Monaco, Consolas, "Courier New", monospace'
        headings:
          fontFamily: 'Roboto, sans-serif'
          fontWeight: 'bold'
        links:
          color: '#1976d2'
        optimizeSpeed: true
      sidebar:
        backgroundColor: '#fafafa'
        textColor: '#333333'
        activeTextColor: '#1976d2'
      fab:
        backgroundColor: '#1976d2'
      rightPanel:
        backgroundColor: '#263238'
        textColor: '#ffffff'
      codeBlock:
        backgroundColor: '#1e1e1e'
  theme:
    openapi:
      additionalProperties: false
      theme:
        spacing:
          unit: 5
        breakpoints:
          small: '50rem'
          medium: '85rem'
          large: '105rem'
        colors:
          primary:
            main: '#1976d2'
          text:
            primary: '#333'
        typography:
          fontSize: '14px'
          fontFamily: 'Roboto, sans-serif'
          code:
            fontSize: '16px'
            fontFamily: 'Monaco, Consolas, "Courier New", monospace'
            lineHeight: '1.5'
          headings:
            fontFamily: 'Roboto, sans-serif'
            fontWeight: 'bold'
          links:
            color: '#1976d2'
        rightPanel:
          backgroundColor: '#263238'
          textColor: '#ffffff'
        codeBlock:
          backgroundColor: '#1e1e1e'
      customCSS: |
        /* Make operation summaries bold only in main content area (not in sidebar) */
        .redoc-wrap .api-content h2,
        .redoc-wrap .api-content .operation-summary,
        .redoc-wrap .redoc-content h2,
        .redoc-wrap .redoc-content .operation-summary {
          font-weight: bold !important;
          font-size: 18px !important;
        }
        
        /* Target operation headers in main content */
        .redoc-wrap .operation h2,
        .redoc-wrap .operation .operation-summary,
        .redoc-wrap [data-section-id] h2 {
          font-weight: bold !important;
          font-size: 18px !important;
        }
        
        /* Keep sidebar navigation normal weight */
        .redoc-wrap .menu-content .menu-item-label,
        .redoc-wrap .menu-content a {
          font-weight: normal !important;
        }
        
        /* More specific targeting for operation summaries */
        .redoc-wrap .operation-tag .operation h2,
        .redoc-wrap .tag-section .operation h2 {
          font-weight: bold !important;
          font-size: 18px !important;
          color: #333 !important;
        }
        
        /* Style Overview and Authentication headings */
        .redoc-wrap .api-info h2:contains("Overview"),
        .redoc-wrap .api-info h3:contains("Authentication"),
        .redoc-wrap .redoc-content h2:contains("Overview"),
        .redoc-wrap .redoc-content h3:contains("Authentication") {
          font-weight: bold !important;
          font-size: 24px !important;
          color: #1976d2 !important;
        }
        
        /* Target main info section headings */
        .redoc-wrap .api-info .redoc-markdown h2,
        .redoc-wrap .api-info .redoc-markdown h3 {
          font-weight: bold !important;
          font-size: 22px !important;
          color: #1976d2 !important;
          margin-top: 25px !important;
          margin-bottom: 15px !important;
        }
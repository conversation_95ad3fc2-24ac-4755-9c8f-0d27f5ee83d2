{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "husky help && ts-node-dev --respawn --transpile-only -r dotenv/config ./src/server.ts", "build": "tsc", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "test": "jest --transform='node_modules/@babel/jest' --verbose", "postinstall": "puppeteer install", "docs:bundle": "redocly bundle docs/propvr-api-main.yaml --output docs/propvr-api-bundled.yaml", "docs:build": "redocly build-docs docs/propvr-api-bundled.yaml --output docs/dist/index.html && mkdir -p docs/dist/images && cp docs/images/*.png docs/dist/images/", "docs:serve": "redocly preview-docs docs/propvr-api-main.yaml --port 4001", "docs:lint": "node docs/lint-redoc-files.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-analytics/data": "^4.8.0", "@google-cloud/tasks": "^5.5.0", "@google-cloud/translate": "^8.5.0", "@googlemaps/google-maps-services-js": "^3.4.0", "@slack/web-api": "^7.3.4", "@svgdotjs/svg.js": "^3.2.4", "@types/babel__generator": "^7.6.8", "@types/babel__template": "^7.4.4", "@types/body-parser": "^1.19.5", "@types/caseless": "^0.12.5", "@types/google.maps": "^3.55.12", "@types/istanbul-lib-report": "^3.0.3", "@types/nodemailer": "^6.4.17", "@types/qs": "^6.9.16", "@types/range-parser": "^1.2.7", "@types/response-time": "^2.3.8", "@types/send": "^0.17.4", "@types/serve-static": "^1.15.7", "@types/tough-cookie": "^4.0.5", "@types/yargs-parser": "^21.0.3", "adm-zip": "^0.5.15", "axios": "^1.7.5", "chromium": "3.0.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.19.2", "express-validator": "^7.2.0", "firebase": "^11.2.0", "firebase-admin": "^12.4.0", "fluent-ffmpeg": "^2.1.3", "fs": "^0.0.1-security", "google-auth-library": "^9.15.1", "googleapis": "^143.0.0", "https": "^1.0.0", "ical-generator": "^7.1.0", "ics": "^3.7.6", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongodb": "^6.8.0", "mongoose": "^7.8.1", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.9", "node-fetch": "^2.7.0", "nodemailer": "^6.9.16", "npm": "^10.8.3", "prom-client": "^15.1.3", "puppeteer": "23.6.0", "puppeteer-core": "23.6.0", "redis": "^5.8.2", "response-time": "^2.3.2", "sharp": "^0.34.3", "supertest": "^6.3.4", "ts-node-dev": "^2.0.0", "typescript": "^5.5.4", "unzipper": "^0.10.14", "winston": "^3.14.2", "xmldom": "^0.6.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/register": "^7.24.6", "@redocly/cli": "^1.34.5", "@types/adm-zip": "^0.5.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.26", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/mime-types": "^3.0.1", "@types/mocha": "^10.0.10", "@types/multer": "^1.4.12", "@types/node-fetch": "^2.6.11", "@types/supertest": "^6.0.2", "@types/unzipper": "^0.10.10", "@types/uuid": "^10.0.0", "@types/xmldom": "^0.1.34", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.0", "eslint-plugin-jest": "^27.9.0", "husky": "^8.0.3", "jest": "^29.7.0", "mongodb-memory-server": "^9.4.1", "nodemon": "^3.0.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.1"}, "prettier": {"singleQuote": true}}
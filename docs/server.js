const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 4001;

// Serve unified API documentation at root
app.use('/', express.static(path.join(__dirname, 'docs/dist')));

// Serve images from both locations (dist/images and root images)
app.use('/images', express.static(path.join(__dirname, 'docs/dist/images')));
app.use('/images', express.static(path.join(__dirname, 'images')));

// 404 handler - catch all unmatched routes
app.use((req, res) => {
  res.status(404).send(`
    <h1>404 - Page Not Found</h1>
    <p>The requested path "${req.originalUrl}" was not found.</p>
    <p><a href="/">← Back to API Documentation</a></p>
  `);
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`PropVR Unified API Documentation server running on port ${PORT}`);
  console.log(`API Documentation: http://localhost:${PORT}/`);
});

module.exports = app;
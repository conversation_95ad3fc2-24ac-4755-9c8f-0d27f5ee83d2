openapi: 3.0.3
info:
  title: PropVR Unified API
  description: |
    # PropVR Unified API Documentation
    
    This comprehensive API provides both dashboard management and client integration capabilities for PropVR's platform with the following features:
    ## Authentication
    All endpoints require JWT authentication via the Authorization header:
    ```
    Authorization: Bearer <your_jwt_token>
    ```
    
    Each organization receives dedicated JWT tokens for secure API access. Contact PropVR support to receive your authentication credentials.

    For Client Integration APIs, you can also generate tokens using the API Keys management interface:

    ### How to Create a JWT Token

    **Step 1: Access the API Keys Section**
    - Navigate to the Settings page in your PropVR dashboard
    - Click on the "API Keys" tab in the settings interface
    - You will see the API Keys management interface with:
      - A list of existing API keys (if any)
      - Status indicators (Active/Inactive)
      - Creation and expiration dates
      - A blue **"+ Create API Key"** button

    ![API Keys Management Interface](./images/JWT-Screen-1.png)

    **Step 2: Create a New API Key**
    - Click the blue **"+ Create API Key"** button
    - A modal dialog will appear with the title "Create API Key"

    ![Create API Key Modal](./images/JWT-Screen-2.png)

    **Step 3: Configure Your API Key**
    - In the modal dialog, you will see:
      - **API Name** field (required) - Enter a descriptive name
      - **Cancel** button (gray) - To close without creating
      - **Create** button (blue) - To generate the token
    - Enter a descriptive name for your API key in the "API Name" field
      - Examples: "Production API", "Development Testing", "External Integration"
    - Click the blue **"Create"** button to generate the token

    **Step 4: Copy Your JWT Token**
    - **IMPORTANT**: The JWT token will be displayed only once immediately after creation
    - Copy the token immediately and store it securely
    - Once you close the modal, the token will no longer be visible
    - If you lose the token, you'll need to create a new API key

    **Step 5: Use Your JWT Token**
    - Include the token in the Authorization header of your API requests:
    ```
    Authorization: Bearer <your_jwt_token>
    ```

    **Security Best Practices:**
    - Store your JWT token securely and never share it publicly
    - Regularly rotate your API keys for enhanced security

    **[Click Here to Generate JWT Token](https://uatapp-dashboard.propvr.in/settings/apikeys)**
    
    ## API Paths
    **Note**: The paths shown in this documentation are clean, RESTful representations. 
    The actual implementation paths on your server may differ. Please refer to your 
    route configuration for the exact endpoint URLs to use in your integration.
    
    ## Response Format
    All endpoints follow a consistent response format:
    ```json
    {
      "status": 1,     // 1 for success, 0 for error
      "data": {},      // Response data (on success)
      "message": "",   // Success message
      "error": ""      // Error message (on failure)
    }
    ```
  version: 1.0.0

servers:
  - url: https://platform.propvr.tech


security:
  - BearerAuth: []

paths:
# Unitplans
  /api/createUnitplan:
    $ref: '../src/controllers/unitplan/API/createUnitplans/createUnitplans_redoc.yaml#/paths/~1createUnitplan'
  
  /api/getUnitplans/{project_id}:
    $ref: '../src/controllers/unitplan/API/getUnitplans/getUnitplans_redoc.yaml#/paths/~1getUnitplans~1{project_id}'
  
  /api/updateUnitplan:
    $ref: '../src/controllers/unitplan/API/updateUnitplan/updateUnitplan_redoc.yaml#/paths/~1updateUnitplan'
  
  /api/deleteUnitplan/{project_id}:
    $ref: '../src/controllers/unitplan/API/deleteUnitplan/deleteUnitplan_redoc.yaml#/paths/~1deleteUnitplan~1{project_id}'

# Units  
  /api/unit/filters':
    $ref: '../src/controllers/units/Api/CreateFilterUnits/CreateFilterUnits_redoc.yaml#/paths/~1unit~1filters'
  
  /api/unit/filtersDrop:
    $ref: '../src/controllers/units/Api/FiltersDrop/FiltersDrop_redoc.yaml#/paths/~1unit~1filtersDrop'
  
  /api/unit/updateUnitByMetadata:
    $ref: '../src/controllers/units/Api/UpdateUnitByMetadata/UpdateUnitByMetadata_redoc.yaml#/paths/~1unit~1updateUnitByMetadata'
  
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

tags:
  - name: Unitplans
    description: |
      Manage unit plans for property projects. Create, retrieve, update, and delete unit plan configurations including floor plans, layouts, and property specifications.
  - name: Units
    description: |
      Handle individual property units within projects. Filter, update, and manage unit status, pricing, metadata, and collection operations for real estate properties.

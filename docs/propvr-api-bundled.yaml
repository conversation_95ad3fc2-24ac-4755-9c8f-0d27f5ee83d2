openapi: 3.0.3
info:
  title: PropVR Unified API
  description: |
    # PropVR Unified API Documentation

    This comprehensive API provides both dashboard management and client integration capabilities for PropVR's platform with the following features:
    ## Authentication
    All endpoints require JWT authentication via the Authorization header:
    ```
    Authorization: Bearer <your_jwt_token>
    ```

    Each organization receives dedicated JWT tokens for secure API access. Contact PropVR support to receive your authentication credentials.

    For Client Integration APIs, you can also generate tokens using the API Keys management interface:

    ### How to Create a JWT Token

    **Step 1: Access the API Keys Section**
    - Navigate to the Settings page in your PropVR dashboard
    - Click on the "API Keys" tab in the settings interface
    - You will see the API Keys management interface with:
      - A list of existing API keys (if any)
      - Status indicators (Active/Inactive)
      - Creation and expiration dates
      - A blue **"+ Create API Key"** button

    ![API Keys Management Interface](./images/JWT-Screen-1.png)

    **Step 2: Create a New API Key**
    - Click the blue **"+ Create API Key"** button
    - A modal dialog will appear with the title "Create API Key"

    ![Create API Key Modal](./images/JWT-Screen-2.png)

    **Step 3: Configure Your API Key**
    - In the modal dialog, you will see:
      - **API Name** field (required) - Enter a descriptive name
      - **Cancel** button (gray) - To close without creating
      - **Create** button (blue) - To generate the token
    - Enter a descriptive name for your API key in the "API Name" field
      - Examples: "Production API", "Development Testing", "External Integration"
    - Click the blue **"Create"** button to generate the token

    **Step 4: Copy Your JWT Token**
    - **IMPORTANT**: The JWT token will be displayed only once immediately after creation
    - Copy the token immediately and store it securely
    - Once you close the modal, the token will no longer be visible
    - If you lose the token, you'll need to create a new API key

    **Step 5: Use Your JWT Token**
    - Include the token in the Authorization header of your API requests:
    ```
    Authorization: Bearer <your_jwt_token>
    ```

    **Security Best Practices:**
    - Store your JWT token securely and never share it publicly
    - Regularly rotate your API keys for enhanced security

    **[Click Here to Generate JWT Token](https://uatapp-dashboard.propvr.in/settings/apikeys)**

    ## API Paths
    **Note**: The paths shown in this documentation are clean, RESTful representations. 
    The actual implementation paths on your server may differ. Please refer to your 
    route configuration for the exact endpoint URLs to use in your integration.

    ## Response Format
    All endpoints follow a consistent response format:
    ```json
    {
      "status": 1,     // 1 for success, 0 for error
      "data": {},      // Response data (on success)
      "message": "",   // Success message
      "error": ""      // Error message (on failure)
    }
    ```
  version: 1.0.0
servers:
  - url: https://platform.propvr.tech
security:
  - BearerAuth: []
tags:
  - name: Unitplans
    description: |
      Manage unit plans for property projects. Create, retrieve, update, and delete unit plan configurations including floor plans, layouts, and property specifications.
  - name: Units
    description: |
      Handle individual property units within projects. Filter, update, and manage unit status, pricing, metadata, and collection operations for real estate properties.
paths:
  /api/createUnitplan:
    post:
      operationId: createUnitplan
      tags:
        - Unitplans
      summary: Create a new unitplan
      description: |
        **Create a comprehensive unit plan with images and metadata**

        This endpoint allows you to create detailed unit plans including:
        - Basic information (name, type, measurements)
        - Image uploads (unitplan images with automatic thumbnail generation)
        - Property details (bedrooms, bathrooms, furnishing status)
        - Commercial/residential classification
        - Tour and gallery associations

        **File Upload Requirements:**
        - For `flat` and `villa_floor` unit types, `unitplan_image` is required
        - Images are automatically resized to generate thumbnails (1280x720)
        - Supported formats: JPG, PNG
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - name
                - is_furnished
                - unit_type
              properties:
                project_id:
                  type: string
                  description: ID of the project this unitplan belongs to
                  example: 507f1f77bcf86cd799439011
                building_id:
                  type: string
                  description: ID of the building this unitplan belongs to
                  example: 507f1f77bcf86cd799439012
                type:
                  type: string
                  description: Type classification of the unitplan
                  example: residential
                name:
                  type: string
                  description: Name of the unitplan
                  example: Luxury 3BHK Apartment
                measurement:
                  type: number
                  description: Area measurement (required for non-villa_floor types)
                  example: 1250.5
                measurement_type:
                  type: string
                  enum:
                    - sqft
                    - sqmt
                  description: Unit of measurement (required for non-villa_floor types)
                  example: sqft
                tour_id:
                  type: string
                  description: Associated virtual tour ID
                  example: 507f1f77bcf86cd799439013
                bedrooms:
                  type: string
                  enum:
                    - studio
                    - 1BHK
                    - 2BHK
                    - 3BHK
                    - 4BHK
                    - 5BHK
                    - 6BHK
                    - 7BHK
                    - 8BHK
                    - 9BHK
                    - 10BHK
                    - 0BHK
                    - penthouse
                    - townhouse
                    - podium
                    - suite
                    - duplex
                    - 1.5BHK
                    - 2.5BHK
                    - 3.5BHK
                    - 4.5BHK
                    - 5.5BHK
                    - 6.5BHK
                    - 7.5BHK
                    - 8.5BHK
                    - 9.5BHK
                    - 10.5BHK
                  description: Number of bedrooms (required for non-villa_floor types)
                  example: 3BHK
                is_residential:
                  type: boolean
                  description: Whether the unit is residential
                  example: true
                bathrooms:
                  type: number
                  description: Number of bathrooms
                  example: 2
                is_furnished:
                  type: boolean
                  description: Whether the unit is furnished
                  example: true
                unit_type:
                  type: string
                  enum:
                    - villa
                    - flat
                    - villa_floor
                  description: Type of unit
                  example: flat
                suite_area:
                  type: string
                  description: Suite area measurement
                  example: '1100'
                suite_area_type:
                  type: string
                  enum:
                    - sqft
                    - sqmt
                  description: Suite area measurement unit
                  example: sqft
                parent_unitplan:
                  type: string
                  description: Parent unitplan ID (required for villa_floor type)
                  example: 507f1f77bcf86cd799439014
                order:
                  type: number
                  description: Display order
                  example: 1
                scene_id:
                  type: string
                  description: Associated scene ID
                  example: 507f1f77bcf86cd799439015
                gallery_id:
                  type: string
                  description: Associated gallery ID (JSON string)
                  example: '["507f1f77bcf86cd799439016"]'
                exterior_type:
                  type: string
                  enum:
                    - scene
                    - gallery
                  description: Type of exterior view
                  example: scene
                style:
                  type: string
                  description: Architectural style
                  example: modern
                balcony_measurement:
                  type: number
                  description: Balcony area measurement
                  example: 50.5
                balcony_measurement_type:
                  type: string
                  enum:
                    - sqft
                    - sqmt
                  description: Balcony measurement unit
                  example: sqft
                is_commercial:
                  type: boolean
                  description: Whether the unit is commercial
                  example: false
                unitplan_image:
                  type: string
                  format: binary
                  description: Unitplan image file (required for flat and villa_floor types)
      responses:
        '200':
          description: Unitplan created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  data:
                    $ref: '#/components/schemas/Unitplan'
        '400':
          description: Bad request - validation errors or missing required fields
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: unitplan_image is required for this unit type
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Failed to resize image
  /api/getUnitplans/{project_id}:
    get:
      operationId: getUnitplans
      tags:
        - Unitplans
      summary: Get all unitplans for a project
      description: |
        **Retrieve all unit plans associated with a specific project**

        This endpoint returns a comprehensive list of all unit plans within a project, including:
        - Basic unitplan information
        - Image URLs and thumbnails
        - Measurements and specifications
        - Associated tours and galleries
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
          description: The ID of the project to retrieve unitplans for
          example: 507f1f77bcf86cd799439011
      responses:
        '200':
          description: Unitplans retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Unitplan'
        '404':
          description: Unitplans not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: unitplans not found
  /api/updateUnitplan:
    post:
      operationId: updateUnitplan
      tags:
        - Unitplans
      summary: Update an existing unitplan
      description: |
        **Update unit plan details and optionally replace images**

        This endpoint allows partial updates to existing unit plans. You can:
        - Update any combination of unitplan properties
        - Replace images (with automatic thumbnail generation)
        - Modify measurements, classifications, and associations
        - Update gallery and tour links

        **File Upload Notes:**
        - Image uploads are optional for updates
        - When updating to `flat` unit type, `unitplan_image` becomes required
        - New images automatically generate resized thumbnails
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - unitplan_id
                - project_id
              properties:
                unitplan_id:
                  type: string
                  description: ID of the unitplan to update
                  example: 507f1f77bcf86cd799439017
                project_id:
                  type: string
                  description: ID of the project this unitplan belongs to
                  example: 507f1f77bcf86cd799439011
                building_id:
                  type: string
                  description: ID of the building this unitplan belongs to
                  example: 507f1f77bcf86cd799439012
                type:
                  type: string
                  description: Type classification of the unitplan
                  example: residential
                name:
                  type: string
                  description: Name of the unitplan
                  example: Updated Luxury 3BHK Apartment
                measurement:
                  type: number
                  description: Area measurement
                  example: 1300.5
                measurement_type:
                  type: string
                  enum:
                    - sqft
                    - sqmt
                  description: Unit of measurement
                  example: sqft
                tour_id:
                  type: string
                  description: Associated virtual tour ID
                  example: 507f1f77bcf86cd799439013
                bedrooms:
                  type: string
                  enum:
                    - studio
                    - 1BHK
                    - 2BHK
                    - 3BHK
                    - 4BHK
                    - 5BHK
                    - 6BHK
                    - 7BHK
                    - 8BHK
                    - 9BHK
                    - 10BHK
                    - 0BHK
                    - penthouse
                    - townhouse
                    - podium
                    - suite
                    - duplex
                    - 1.5BHK
                    - 2.5BHK
                    - 3.5BHK
                    - 4.5BHK
                    - 5.5BHK
                    - 6.5BHK
                    - 7.5BHK
                    - 8.5BHK
                    - 9.5BHK
                    - 10.5BHK
                  description: Number of bedrooms
                  example: 3BHK
                is_residential:
                  type: boolean
                  description: Whether the unit is residential
                  example: true
                bathrooms:
                  type: number
                  description: Number of bathrooms
                  example: 3
                is_furnished:
                  type: boolean
                  description: Whether the unit is furnished
                  example: false
                unit_type:
                  type: string
                  enum:
                    - villa
                    - flat
                    - villa_floor
                  description: Type of unit
                  example: flat
                suite_area:
                  type: string
                  description: Suite area measurement
                  example: '1200'
                suite_area_type:
                  type: string
                  enum:
                    - sqft
                    - sqmt
                  description: Suite area measurement unit
                  example: sqft
                scene_id:
                  type: string
                  description: Associated scene ID
                  example: 507f1f77bcf86cd799439015
                gallery_id:
                  type: string
                  description: Associated gallery ID (JSON string)
                  example: '["507f1f77bcf86cd799439016", "507f1f77bcf86cd799439018"]'
                floor_unitplans:
                  type: string
                  description: Floor unitplans (JSON string)
                  example: '["507f1f77bcf86cd799439019"]'
                exterior_type:
                  type: string
                  enum:
                    - scene
                    - gallery
                  description: Type of exterior view
                  example: gallery
                style:
                  type: string
                  description: Architectural style
                  example: contemporary
                balcony_measurement:
                  type: number
                  description: Balcony area measurement
                  example: 60
                balcony_measurement_type:
                  type: string
                  enum:
                    - sqft
                    - sqmt
                  description: Balcony measurement unit
                  example: sqft
                is_commercial:
                  type: boolean
                  description: Whether the unit is commercial
                  example: false
                unitplan_image:
                  type: string
                  format: binary
                  description: Updated unitplan image file (required when changing unit_type to flat)
      responses:
        '200':
          description: Unitplan updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  data:
                    $ref: '#/components/schemas/Unitplan'
        '400':
          description: Bad request - validation errors or missing required fields
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        msg:
                          type: string
                        param:
                          type: string
                  error:
                    type: string
                    example: No fields provided for editing.
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Error resizing image
  /api/deleteUnitplan/{project_id}:
    post:
      operationId: deleteUnitplan
      tags:
        - Unitplans
      summary: Delete a unitplan
      description: |
        **Safely delete a unit plan and move it to trash**

        This endpoint performs a soft delete by moving the unitplan to trash rather than permanently deleting it. The operation:
        - Moves the unitplan to trash with a timestamp
        - Checks for linked units and provides warnings
        - Returns information about affected units
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
          description: The ID of the project containing the unitplan
          example: 507f1f77bcf86cd799439011
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - unitplan_id
              properties:
                unitplan_id:
                  type: string
                  description: ID of the unitplan to delete
                  example: 507f1f77bcf86cd799439017
      responses:
        '201':
          description: Unitplan deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: Unitplan deleted successfully. Please update the following units that were linked to this unitplan
                  linkedUnits:
                    type: array
                    items:
                      type: string
                    description: Names of units that were linked to this unitplan
                    example:
                      - Unit A-101
                      - Unit A-102
                  unitCount:
                    type: integer
                    description: Number of linked units
                    example: 2
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        msg:
                          type: string
                        param:
                          type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Error moving unitplans to trash
  /api/unit/filters':
    post:
      operationId: createFilterUnits
      tags:
        - Units
      summary: Create Filter Units
      description: |
        Create and process filter units for a specific project. This endpoint allows you to:
        - Process multiple units by name
        - Update unit status (available, onhold, reserved, sold)
        - Set pricing and currency information
        - Bulk process units with filtering capabilities
      security:
        - BearerAuth: []
      parameters:
        - name: authorization
          in: header
          required: true
          schema:
            type: string
          description: API key for authentication
          example: Bearer your_jwt_token_here
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - project_id
                - units
                - status
              properties:
                project_id:
                  type: string
                  description: Unique identifier of the project
                  example: 507f1f77bcf86cd799439011
                units:
                  type: array
                  description: Array of unit names to process
                  items:
                    type: string
                  example:
                    - Unit-A101
                    - Unit-A102
                    - Unit-B201
                status:
                  type: string
                  description: Status to set for the units
                  enum:
                    - available
                    - onhold
                    - reserved
                    - sold
                  example: available
                price:
                  type: string
                  description: Price for the units (optional, numeric string)
                  pattern: ^[0-9]+(\.[0-9]+)?$
                  example: '750000'
                currency:
                  type: string
                  description: Currency code for the price (optional)
                  enum:
                    - aed
                    - ars
                    - aud
                    - bgn
                    - brl
                    - bsd
                    - cad
                    - chf
                    - clp
                    - cny
                    - cop
                    - czk
                    - dkk
                    - dop
                    - egp
                    - eur
                    - fjd
                    - gbp
                    - gtq
                    - hkd
                    - hrk
                    - huf
                    - idr
                    - ils
                    - inr
                    - isk
                    - jpy
                    - krw
                    - kzt
                    - mxn
                    - myr
                    - nok
                    - nzd
                    - pab
                    - pen
                    - php
                    - pkr
                    - pln
                    - pyg
                    - ron
                    - rub
                    - sek
                    - sgd
                    - thb
                    - try
                    - twd
                    - uah
                    - usd
                    - uyu
                    - vnd
                    - zar
                  example: usd
      responses:
        '200':
          description: Units processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  data:
                    type: object
                    properties:
                      successful:
                        type: array
                        description: Array of successfully processed unit names
                        items:
                          type: string
                        example:
                          - Unit-A101
                          - Unit-A102
                      failed:
                        type: array
                        description: Array of failed units with error details
                        items:
                          type: object
                          properties:
                            unitName:
                              type: string
                              example: Unit-B201
                            error:
                              type: string
                              example: No Units Found for Unit-B201
                        example: []
                      sessionId:
                        type: string
                        description: Session ID for the processing operation
                        example: session_507f1f77bcf86cd799439011
                  message:
                    type: string
                    example: Units processed successfully
                  error:
                    type: string
        '400':
          description: Bad request - Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                          example: field
                        value:
                          type: string
                          example: ''
                        msg:
                          type: string
                          example: project_id is required
                        path:
                          type: string
                          example: project_id
                        location:
                          type: string
                          example: body
                examples:
                  validation_error:
                    summary: Validation Error Example
                    value:
                      errors:
                        - type: field
                          value: ''
                          msg: project_id is required
                          path: project_id
                          location: body
                        - type: field
                          value: invalid_status
                          msg: 'Invalid status. Valid options are: available, onhold, reserved, sold'
                          path: status
                          location: body
                  authorization_missing:
                    summary: Missing Authorization Header
                    value:
                      errors:
                        - type: field
                          value: ''
                          msg: apikey is required
                          path: authorization
                          location: headers
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Authentication required
                  error:
                    type: string
                    example: Invalid or missing JWT token
        '403':
          description: Forbidden - Insufficient project permissions
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Access denied
                  error:
                    type: string
                    example: Insufficient permissions for this project
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Internal server error
                  error:
                    type: string
                    example: An unexpected error occurred while processing units
  /api/unit/filtersDrop:
    post:
      operationId: filterDropCollection
      tags:
        - Units
      summary: Drop Unit Collection Filters
      description: |
        Drop (delete) unit collection filters for a specific project and session. This endpoint allows you to:
        - Remove all filtered units from a specific session
        - Clean up temporary unit collections
        - Reset filter states for a project session
        - Free up resources by dropping unused collections
      security:
        - BearerAuth: []
      parameters:
        - name: authorization
          in: header
          required: true
          schema:
            type: string
          description: API key for authentication
          example: Bearer your_jwt_token_here
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - project_id
                - session_id
              properties:
                project_id:
                  type: string
                  description: Unique identifier of the project
                  example: 507f1f77bcf86cd799439011
                session_id:
                  type: string
                  description: Session identifier for the unit collection to drop
                  example: session_507f1f77bcf86cd799439011
      responses:
        '200':
          description: Collection dropped successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                    description: Success status indicator
                  message:
                    type: string
                    example: Collection Dropped Successfully !
                    description: Success message
              examples:
                success:
                  summary: Successful collection drop
                  value:
                    status: 1
                    message: Collection Dropped Successfully !
        '400':
          description: Bad request - Collection does not exist or validation errors
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    properties:
                      status:
                        type: integer
                        example: 0
                      message:
                        type: string
                        example: Collection does not Exists
                    description: Collection not found response
                  - type: object
                    properties:
                      errors:
                        type: array
                        items:
                          type: object
                          properties:
                            type:
                              type: string
                              example: field
                            value:
                              type: string
                              example: ''
                            msg:
                              type: string
                              example: project_id is required
                            path:
                              type: string
                              example: project_id
                            location:
                              type: string
                              example: body
                    description: Validation errors response
              examples:
                collection_not_exists:
                  summary: Collection does not exist
                  value:
                    status: 0
                    message: Collection does not Exists
                validation_error:
                  summary: Validation Error Example
                  value:
                    errors:
                      - type: field
                        value: ''
                        msg: project_id is required
                        path: project_id
                        location: body
                      - type: field
                        value: 123
                        msg: Session Id must be a String
                        path: session_id
                        location: body
                authorization_missing:
                  summary: Missing Authorization Header
                  value:
                    errors:
                      - type: field
                        value: ''
                        msg: apikey is required
                        path: authorization
                        location: headers
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Authentication required
                  error:
                    type: string
                    example: Invalid or missing JWT token
        '403':
          description: Forbidden - Insufficient project permissions
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Access denied
                  error:
                    type: string
                    example: Insufficient permissions for this project
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: 'Error during Unit Collection Drop: Database connection failed'
              examples:
                server_error:
                  summary: Internal Server Error
                  value:
                    status: 0
                    error: 'Error during Unit Collection Drop: Database connection failed'
  /api/unit/updateUnitByMetadata:
    post:
      operationId: updateUnitByMetadata
      tags:
        - Units
      summary: Update Unit By Metadata
      description: |
        Update unit information using metadata for a specific project. This endpoint allows you to:
        - Update unit properties based on metadata matching
        - Modify unit status, price, measurements, and currency
        - Process single unit updates with comprehensive validation
        - Maintain data integrity through metadata-based matching
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: header
          required: true
          schema:
            type: string
          description: Project ID to identify which project the unit belongs to
          example: 507f1f77bcf86cd799439011
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              minItems: 1
              maxItems: 1
              description: Array containing exactly one unit object to update
              items:
                type: object
                required:
                  - metadata
                properties:
                  metadata:
                    type: object
                    description: Metadata object used to identify and match the unit
                    additionalProperties:
                      type: string
                    example:
                      unit_number: A101
                      floor: '1'
                      building: Tower A
                    minProperties: 1
                  status:
                    type: string
                    description: New status for the unit
                    enum:
                      - available
                      - onhold
                      - reserved
                      - sold
                    example: available
                  price:
                    type: string
                    description: New price for the unit (numeric string)
                    pattern: ^[0-9]+(\.[0-9]+)?$
                    example: '850000'
                  currency:
                    type: string
                    description: Currency code for the price
                    enum:
                      - aed
                      - ars
                      - aud
                      - bgn
                      - brl
                      - bsd
                      - cad
                      - chf
                      - clp
                      - cny
                      - cop
                      - czk
                      - dkk
                      - dop
                      - egp
                      - eur
                      - fjd
                      - gbp
                      - gtq
                      - hkd
                      - hrk
                      - huf
                      - idr
                      - ils
                      - inr
                      - isk
                      - jpy
                      - krw
                      - kzt
                      - lbp
                      - mxn
                      - myr
                      - nok
                      - nzd
                      - pab
                      - pen
                      - php
                      - pkr
                      - pln
                      - pyg
                      - qar
                      - ron
                      - rub
                      - sar
                      - sek
                      - sgd
                      - thb
                      - try
                      - twd
                      - uah
                      - usd
                      - uyu
                      - vnd
                      - zar
                      - jod
                      - ''
                    example: usd
                  measurement:
                    type: number
                    description: Unit measurement/area
                    example: 1200.5
                  measurement_type:
                    type: string
                    description: Type of measurement unit
                    enum:
                      - sqft
                      - sqmt
                    example: sqft
                anyOf:
                  - required:
                      - status
                  - required:
                      - price
                  - required:
                      - measurement
                  - required:
                      - currency
                  - required:
                      - measurement_type
            examples:
              update_status_and_price:
                summary: Update unit status and price
                value:
                  - metadata:
                      unit_number: A101
                      floor: '1'
                      building: Tower A
                    status: reserved
                    price: '850000'
                    currency: usd
              update_measurement:
                summary: Update unit measurement
                value:
                  - metadata:
                      unit_id: UNIT_001
                      block: B
                    measurement: 1500.75
                    measurement_type: sqft
      responses:
        '200':
          description: Unit updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                    description: Success status indicator
                  message:
                    type: string
                    example: updated unit successfully
                    description: Success message
              examples:
                success:
                  summary: Successful unit update
                  value:
                    status: 1
                    message: updated unit successfully
        '400':
          description: Bad request - Validation errors or update failures
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    properties:
                      errors:
                        type: array
                        items:
                          type: object
                          properties:
                            type:
                              type: string
                              example: field
                            value:
                              type: string
                              example: ''
                            msg:
                              type: string
                              example: metadata is required for item 0
                            path:
                              type: string
                              example: '0'
                            location:
                              type: string
                              example: body
                    description: Validation errors response
                  - type: object
                    properties:
                      status:
                        type: integer
                        example: 0
                      message:
                        type: string
                        example: Unit update failed
                    description: Update failure response
              examples:
                validation_error:
                  summary: Validation Error Example
                  value:
                    errors:
                      - type: field
                        value: ''
                        msg: metadata is required for item 0
                        path: '0'
                        location: body
                      - type: field
                        value: invalid_status
                        msg: 'Invalid status. Valid options are: available, onhold, reserved, sold'
                        path: 0.status
                        location: body
                array_length_error:
                  summary: Array Length Error
                  value:
                    errors:
                      - type: field
                        value: []
                        msg: Only one object is allowed. Found 2 objects
                        path: '0'
                        location: body
                metadata_empty_error:
                  summary: Empty Metadata Error
                  value:
                    errors:
                      - type: field
                        value: {}
                        msg: metadata cant be empty, it must have atleast one key-value pair
                        path: 0.metadata
                        location: body
                missing_fields_error:
                  summary: Missing Required Fields Error
                  value:
                    errors:
                      - type: field
                        value:
                          metadata:
                            unit_id: A101
                        msg: At least one of status, price, measurement, currency, measurement_type fields is required for item 0
                        path: '0'
                        location: body
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Authentication required
                  error:
                    type: string
                    example: Invalid or missing JWT token
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: project_id not found
              examples:
                project_not_found:
                  summary: Project Not Found
                  value:
                    status: 0
                    message: project_id not found
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: Error updating unit
              examples:
                server_error:
                  summary: Internal Server Error
                  value:
                    status: 0
                    message: Database connection failed
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Unitplan:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the unitplan
          example: 507f1f77bcf86cd799439017
        project_id:
          type: string
          description: ID of the associated project
          example: 507f1f77bcf86cd799439011
        building_id:
          type: string
          description: ID of the associated building
          example: 507f1f77bcf86cd799439012
        type:
          type: string
          description: Type classification
          example: residential
        name:
          type: string
          description: Unitplan name
          example: Luxury 3BHK Apartment
        thumbnail:
          type: string
          description: URL to the thumbnail image
          example: https://storage.googleapis.com/propvr-assets/unitplans/thumbnails/507f1f77bcf86cd799439017.jpg
        image_url:
          type: string
          description: URL to the full-size image
          example: https://storage.googleapis.com/propvr-assets/unitplans/507f1f77bcf86cd799439017.jpg
        measurement:
          type: number
          description: Area measurement
          example: 1250.5
        measurement_type:
          type: string
          enum:
            - sqft
            - sqmt
          description: Unit of measurement
          example: sqft
        tour_id:
          type: string
          description: Associated virtual tour ID
          example: 507f1f77bcf86cd799439013
        bedrooms:
          type: string
          enum:
            - studio
            - 1BHK
            - 2BHK
            - 3BHK
            - 4BHK
            - 5BHK
            - 6BHK
            - 7BHK
            - 8BHK
            - 9BHK
            - 10BHK
            - 0BHK
            - penthouse
            - townhouse
            - podium
            - suite
            - duplex
            - 1.5BHK
            - 2.5BHK
            - 3.5BHK
            - 4.5BHK
            - 5.5BHK
            - 6.5BHK
            - 7.5BHK
            - 8.5BHK
            - 9.5BHK
            - 10.5BHK
          description: Number of bedrooms
          example: 3BHK
        is_residential:
          type: boolean
          description: Whether the unit is residential
          example: true
        is_commercial:
          type: boolean
          description: Whether the unit is commercial
          example: false
        bathrooms:
          type: number
          description: Number of bathrooms
          example: 2
        is_furnished:
          type: boolean
          description: Whether the unit is furnished
          example: true
        unit_type:
          type: string
          enum:
            - villa
            - flat
            - villa_floor
          description: Type of unit
          example: flat
        exterior_type:
          type: string
          enum:
            - scene
            - gallery
          description: Type of exterior view
          example: scene
        scene_id:
          type: string
          description: Associated scene ID
          example: 507f1f77bcf86cd799439015
        gallery_id:
          type: array
          items:
            type: string
          description: Associated gallery IDs
          example:
            - 507f1f77bcf86cd799439016
        floor_unitplans:
          type: array
          items:
            type: string
          description: Floor unitplan IDs (for villa type)
          example:
            - 507f1f77bcf86cd799439019
        style:
          type: string
          description: Architectural style
          example: modern
        balcony_measurement:
          type: number
          description: Balcony area measurement
          example: 50.5
        balcony_measurement_type:
          type: string
          enum:
            - sqft
            - sqmt
          description: Balcony measurement unit
          example: sqft
        suite_area:
          type: string
          description: Suite area measurement
          example: '1100'
        suite_area_type:
          type: string
          enum:
            - sqft
            - sqmt
          description: Suite area measurement unit
          example: sqft
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
          example: '2024-01-15T10:30:00Z'
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: '2024-01-15T14:45:00Z'

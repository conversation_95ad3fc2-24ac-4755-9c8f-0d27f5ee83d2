#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function findFiles(dir, pattern, ignorePatterns = []) {
  const results = [];
  
  // Check if directory should be ignored
  const relativePath = path.relative(process.cwd(), dir);
  for (const ignorePattern of ignorePatterns) {
    if (relativePath.includes(ignorePattern.replace('/**', ''))) {
      return results;
    }
  }
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativePath = path.relative(process.cwd(), fullPath);
      
      // Skip if item should be ignored
      let shouldIgnore = false;
      for (const ignorePattern of ignorePatterns) {
        if (item.startsWith('.') || relativePath.includes(ignorePattern.replace('/**', ''))) {
          shouldIgnore = true;
          break;
        }
      }
      
      if (shouldIgnore) continue;
      
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Recursively search subdirectories
        results.push(...findFiles(fullPath, pattern, ignorePatterns));
      } else if (stat.isFile() && item.includes(pattern)) {
        // File matches pattern
        results.push(relativePath);
      }
    }
  } catch (error) {
    // Skip directories we can't read
    console.warn(`Warning: Cannot read directory ${dir}`);
  }
  
  return results;
}

async function lintRedocFiles() {
  try {
    console.log('🔍 Searching for *redoc.yaml files...\n');
    
    // Find all files with "redoc.yaml" in their name using built-in modules
    const redocFiles = findFiles(
      process.cwd(), 
      'redoc.yaml', 
      ['node_modules', 'dist', '.git', '.npm', 'coverage']
    );

    if (redocFiles.length === 0) {
      process.exit(1);
    }

    redocFiles.forEach((file, index) => {
      console.log(`   ${index + 1}. ${file}`);
    });
    console.log('');

    // Lint each file
    let hasErrors = false;
    const results = [];

    for (const file of redocFiles) {
      try {
        const output = execSync(`npx redocly lint "${file}"`, { 
          encoding: 'utf8',
          stdio: 'pipe'
        });
        
        console.log(` ${file} - Valid`);
        results.push({ file, status: 'valid', output });
        
      } catch (error) {
        console.log(` ${file} - Has errors`);
        console.log(error.stdout || error.message);
        results.push({ file, status: 'error', error: error.stdout || error.message });
        hasErrors = true;
      }
      console.log(''); // Add spacing between files
    }

    // Summary
    console.log('='.repeat(50));
    
    const validFiles = results.filter(r => r.status === 'valid');
    const errorFiles = results.filter(r => r.status === 'error');
    
    console.log(`✅ Valid files: ${validFiles.length}`);
    console.log(`❌ Files with errors: ${errorFiles.length}`);
    
    if (validFiles.length > 0) {
      console.log('\n✅ Valid files:');
      validFiles.forEach(r => console.log(`   - ${r.file}`));
    }
    
    if (errorFiles.length > 0) {
      console.log('\n❌ Files with errors:');
      errorFiles.forEach(r => console.log(`   - ${r.file}`));
    }

    if (hasErrors) {
      console.log('\n💡 Run individual file linting for detailed error information:');
      errorFiles.forEach(r => {
        console.log(`   npx redocly lint "${r.file}"`);
      });
      process.exit(1);
    } else {
      console.log('\n🎉 All redoc files are valid!');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ Error running lint script:', error.message);
    process.exit(1);
  }
}

// Run the script
lintRedocFiles();

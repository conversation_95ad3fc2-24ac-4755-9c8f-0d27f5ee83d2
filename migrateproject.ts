import { admin } from './src/config/firebase';
import { generateProjectId } from './src/helpers/projects';

export async function MigrateProject (
  uid: string,
  project_id: string,
  organization: string,
): Promise<void> {
  const project_ref = await admin
    .database()
    .ref('users/' + uid + '/Projects/' + project_id)
    .get();
  const project_data = project_ref.val();
  console.log(project_data);
  const new_project_id = await generateProjectId();
  await admin
    .firestore()
    .collection('projects')
    .doc(new_project_id)
    .set({ ...project_data, organization_id: organization });
  return project_data;
}
// MigrateProject("lZyTQkxqk4flOsraGJtzxOkzwS32","Test","EBLRbJ")

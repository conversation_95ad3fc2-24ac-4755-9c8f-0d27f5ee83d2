import dotenv from 'dotenv';
import axios from 'axios';
dotenv.config({ path: '.env.test' });

const data = JSON.stringify({
  'email': 'karthik<PERSON><EMAIL>',
  'password': 'qwerty',
});

const config = {
  method: 'post',
  maxBodyLength: Infinity,
  url: process.env.BASE_URL+'/user/AuthenticateUser',
  headers: {
    'Content-Type': 'application/json',
  },
  data: data,
};

axios.request(config)
  .then(() => {
    // Process.env.EXPIRED_ACCESSTOKEN = process.env.ACCESSTOKEN;
    // Process.env.ACCESSTOKEN = response.data.accessToken;
  })
  .catch((error) => {
    console.log(error);
  });

# 🌍Environment Variables
Environment variables are essential for configuring and securing your application. This project leverages the dotenv package to manage environment variables.

# 📁 Setting Up Your .env File
* Navigate to the root of the project.
* Create a file named .env.
* Populate the .env file with the necessary variables.
* Use the template below as a guide:

| Variable Name  | Value |
| ------------- | ------------- |
| `MONGO_CONNECTION_STRING`  | `mongodb+srv://admin:<EMAIL>/devDB?retryWrites=true&w=majority`  |



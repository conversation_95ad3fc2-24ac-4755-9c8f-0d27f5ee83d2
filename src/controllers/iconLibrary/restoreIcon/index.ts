import { ExtendedRequest } from '../../../types/extras';
import { IconLibraryModule } from '../../../modules/iconLibrary';
import { Response } from 'express';
import logger from '../../../config/logger';

export async function restoreIcon (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const organization_id = request.organization_id as string;
    const trash_id = request.body.trash_id as string;

    if (!organization_id) {
      response.status(400).json({
        status: 0,
        error: 'Organization ID is required',
        message: 'This operation is only available for organization-level icons',
      });
      return;
    }

    // Initialize module for organization icons only
    const iconLibrary = new IconLibraryModule(organization_id);

    await iconLibrary
      .restoreIcon(organization_id, trash_id)
      .then(() => {
        response.status(201).json({
          status: 1,
          message: 'Icon restored successfully',
        });
      })
      .catch((error: Error) => {
        logger.error('Error in restoreIcon', { message: error });
        response
          .status(500)
          .json({
            status: 0,
            error: 'Error restoring icon: ' + error.message,
          });
      });
  } catch (error) {
    logger.error('Error in restoreIcon', { message: error });
    response
      .status(500)
      .json({
        status: 0,
        error: 'Error restoring icon: ' + (error instanceof Error ? error.message : 'Unknown error'),
      });
  }
}

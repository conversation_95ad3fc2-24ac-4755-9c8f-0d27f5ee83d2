import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const restoreIconValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('trash_id', 'Trash ID is required').notEmpty().isString(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default restoreIconValidate;

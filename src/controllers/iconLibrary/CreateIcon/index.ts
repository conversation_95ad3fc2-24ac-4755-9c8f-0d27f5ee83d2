import { Request, Response } from 'express';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { ToLowerCase } from '../../../helpers/dataFormatHelper';
import { Types } from 'mongoose';
import { IconLibraryModule } from '../../../modules/iconLibrary';
import { ExtendedRequest } from '../../../types/extras';

export async function CreateIconLibrary (
  request: Request,
  response: Response,
): Promise<void>{
  try {
    const reqBody = request.body;
    const requestFiles = request.files;
    const _id = new Types.ObjectId();
    const iconLibrary = new IconLibraryModule();
    const storagePath = (iconLibrary.storagepath + _id) as string;

    if (requestFiles === undefined) {
      response.send('Error reading SVG file:');
      return;
    }
    let filesToUpload: Express.Multer.File | Express.Multer.File[] |
      { [fieldname: string]: Express.Multer.File[]; } = [];

    Object.values(requestFiles).forEach((filesArray: Express.Multer.File[]) => {
      filesArray.forEach( (file: Express.Multer.File) => {

        if (file.fieldname === 'icon') {
          console.log('111', file);

          // Wrap the file in an object with the field name as key
          filesToUpload = { [file.fieldname]: [file] };
        }
      });
    });
    console.log('filesto Upload', filesToUpload);

    UploadUnitplanFiles(filesToUpload, storagePath).then(
      async (urlObject: { [key: string]: string }) => {
        console.log('urlObject', urlObject);

        iconLibrary.CreateIcon({
          _id: _id,
          name: ToLowerCase(reqBody.name),
          type: reqBody.type,
          category: reqBody.category,
          iconURL: urlObject.icon,
          iconHeight: reqBody.iconheight,
          iconWidth: reqBody.iconwidth,
        })
          .then((res) => {
            response.send({
              status: 1,
              message: 'CreateIcon Succesfully',
              data: res,
            });
          });

      });
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in CreateIconLibrary', message: error.message });
    }
  }

}

export async function CreateOrgIconLibrary (
  request: ExtendedRequest,
  response: Response,
): Promise<void>{
  try {
    const reqBody = request.body;
    const requestFiles = request.files;
    const _id = new Types.ObjectId();
    const organization_id = request.organization_id as string;
    const iconLibrary = new IconLibraryModule(organization_id);
    const storagePath = (iconLibrary.storagepath + _id) as string;

    if (requestFiles === undefined) {
      response.send('Error reading SVG file:');
      return;
    }
    let filesToUpload: Express.Multer.File | Express.Multer.File[] |
      { [fieldname: string]: Express.Multer.File[]; } = [];

    Object.values(requestFiles).forEach((filesArray: Express.Multer.File[]) => {
      filesArray.forEach( (file: Express.Multer.File) => {

        if (file.fieldname === 'icon') {
          console.log('111', file);

          // Wrap the file in an object with the field name as key
          filesToUpload = { [file.fieldname]: [file] };
        }
      });
    });
    console.log('filesto Upload', filesToUpload);

    UploadUnitplanFiles(filesToUpload, storagePath).then(
      async (urlObject: { [key: string]: string }) => {
        console.log('urlObject', urlObject);

        iconLibrary.CreateIcon({
          _id: _id,
          name: ToLowerCase(reqBody.name),
          type: reqBody.type,
          category: reqBody.category,
          iconURL: urlObject.icon,
          iconHeight: reqBody.iconheight,
          iconWidth: reqBody.iconwidth,
          organization_id: organization_id,
        })
          .then((res) => {
            response.send({
              status: 1,
              message: 'CreateOrgIcon Succesfully',
              data: res,
            });
          });

      });
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in CreateOrgIconLibrary', message: error.message });
    }
  }

}

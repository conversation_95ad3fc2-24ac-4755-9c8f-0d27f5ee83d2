
import { Request, Response } from 'express';
import { IconLibraryModule } from '../../../modules/iconLibrary';
import { ExtendedRequest } from '../../../types/extras';

export async function GetIconsLibrary (
  request: Request,
  response: Response,
):Promise<void> {
  const iconType = request.query.type as string;
  const iconCategory = request.query.category as string;
  console.log('type', iconType);
  console.log('cat', iconCategory);

  const iconLibrary = new IconLibraryModule();

  try {
    iconLibrary.GetIcon(iconType, iconCategory)
      .then((res) => {
        console.log('res', res);
        response.send({
          status: 1,
          message: 'GetIcon Succesfull',
          data: res,
        });
      }).catch((err) => {
        response.send({
          status: 0,
          message: err.message,
        });
      });
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in GetIconsLibrary', message: error.message });
    }
  }

}

export async function GetOrgIconsLibrary (
  request: ExtendedRequest,
  response: Response,
):Promise<void> {
  const iconType = request.query.type as string;
  const iconCategory = request.query.category as string;
  const organization_id = request.organization_id as string;
  console.log('type', iconType);
  console.log('cat', iconCategory);
  console.log('organization_id', organization_id);

  const iconLibrary = new IconLibraryModule(organization_id);

  try {
    iconLibrary.GetIcon(iconType, iconCategory)
      .then((res) => {
        console.log('res', res);
        response.send({
          status: 1,
          message: 'GetOrgIcon Succesfull',
          data: res,
        });
      }).catch((err) => {
        response.send({
          status: 0,
          message: err.message,
        });
      });
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in GetOrgIconsLibrary', message: error.message });
    }
  }

}

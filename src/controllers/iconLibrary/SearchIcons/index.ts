
import { Request, Response } from 'express';
import { IconLibraryModule } from '../../../modules/iconLibrary';
import { QueryParams } from '../../../types/iconLibrary';
import { arrayToObject } from '../../../helpers/dataFormatHelper';
import { ExtendedRequest } from '../../../types/extras';

export async function SearchIcon (
  request: Request,
  response: Response,
): Promise<void> {
  const query:QueryParams = { ...request.query };

  const iconLibrary = new IconLibraryModule();

  try {
    const iconSearchResults = await iconLibrary.SearchIcon(query);
    if (iconSearchResults) {
      const SearchResultObj = arrayToObject(iconSearchResults);
      response.send({
        status: 1,
        message: 'SearchIcon Succesfull',
        data: SearchResultObj,
      });
    } else {
      response
        .status(404)
        .send({status: 0, data: 'No Icons Found'});
    }
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in SearchIconsLibrary', message: error.message });
    }
  }

}

export async function SearchOrgIcon (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const query:QueryParams = { ...request.query };
  const organization_id = request.organization_id as string;

  const iconLibrary = new IconLibraryModule(organization_id);

  try {
    const iconSearchResults = await iconLibrary.SearchIcon(query);
    if (iconSearchResults) {
      const SearchResultObj = arrayToObject(iconSearchResults);
      response.send({
        status: 1,
        message: 'SearchOrgIcon Succesfull',
        data: SearchResultObj,
      });
    } else {
      response
        .status(404)
        .send({status: 0, data: 'No Icons Found'});
    }
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in SearchOrgIconsLibrary', message: error.message });
    }
  }

}

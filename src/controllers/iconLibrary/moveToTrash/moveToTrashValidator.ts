import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const moveToTrashValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('icon_ids', 'Icon IDs are required').isArray().notEmpty(),
  body('icon_ids.*', 'Each icon ID must be a valid string').isString(),
  body('timeStamp', 'Timestamp is required').isNumeric(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default moveToTrashValidate;

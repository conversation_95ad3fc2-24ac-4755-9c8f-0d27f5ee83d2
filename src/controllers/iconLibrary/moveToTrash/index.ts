import { ExtendedRequest } from '../../../types/extras';
import { IconLibraryModule } from '../../../modules/iconLibrary';
import { Response } from 'express';
import logger from '../../../config/logger';

export async function moveIconToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const organization_id = request.organization_id as string;
    const icon_ids = request.body.icon_ids as Array<string>;
    const timeStamp = request.body.timeStamp as number;

    if (!organization_id) {
      response.status(400).json({
        status: 0,
        error: 'Organization ID is required',
        message: 'This operation is only available for organization-level icons',
      });
      return;
    }

    // Initialize module for organization icons only
    const iconLibrary = new IconLibraryModule(organization_id);

    await iconLibrary
      .moveToTrash(icon_ids, organization_id, timeStamp)
      .then(() => {
        response.status(201).json({
          status: 1,
          message: 'Icon(s) moved to trash successfully',
        });
      })
      .catch((error: Error) => {
        logger.error('Error in moveIconToTrash', { message: error });
        response
          .status(500)
          .json({
            status: 0,
            error: 'Error moving icon(s) to trash: ' + error.message,
          });
      });
  } catch (error) {
    logger.error('Error in moveIconToTrash', { message: error });
    response
      .status(500)
      .json({
        status: 0,
        error: 'Error moving icon(s) to trash: ' + (error instanceof Error ? error.message : 'Unknown error'),
      });
  }
}

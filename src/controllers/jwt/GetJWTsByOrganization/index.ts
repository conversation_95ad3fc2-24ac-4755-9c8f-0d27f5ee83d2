import { Response } from 'express';
import { JWTModule } from '../../../modules/jwt';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetJWTsByOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const organization_id = request.organization_id as string;

    const jwtModule = new JWTModule();
    const jwtRecords = await jwtModule.getJWTsByOrganization(organization_id);
    const jwtObject: { [key: string]: any } = {};
    jwtRecords.forEach((record) => {
      jwtObject[record._id.toString()] = {
        _id: record._id,
        name: record.name,
        role: record.role,
        organization: record.organization,
        created_at: record.created_at,
        expiry: record.expiry,
      };
    });

    response.status(200).send({
      status: 1,
      data: jwtObject,
    });
  } catch (err) {
    logger.error('Error in GetJWTsByOrganization', { message: err });
    response.status(500).send({
      status: 0,
      message: err instanceof Error ? err.message : 'Internal server error',
    });
  }
}

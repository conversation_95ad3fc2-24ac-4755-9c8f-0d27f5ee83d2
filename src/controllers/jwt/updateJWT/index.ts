import logger from '../../../config/logger';
import { JWTModule } from '../../../modules/jwt';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export async function updateJWT (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const JWTMod = new JWTModule();
  JWTMod.updateJWT(request.body.token)
    .then((res) => {
      if (res) {
        response.status(201).json({ status: 1, data: res });
      } else {
        response.status(500).json({status: 0, error: 'No record found'});
      }
    })
    .catch((error: Error) => {
      logger.error('Error while updating sidebar option', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while updating sidebar option'+ error });
    });
}

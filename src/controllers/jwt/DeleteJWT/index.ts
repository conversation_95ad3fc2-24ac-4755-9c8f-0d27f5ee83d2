import { Response } from 'express';
import { JWTModule } from '../../../modules/jwt';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function DeleteJWT (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const organization_id = request.organization_id as string;
    const jwtId = request.body.jwtId;
    const jwtModule = new JWTModule();
    const result = await jwtModule.deleteJWT(jwtId, organization_id);
    response.status(200).send({
      status: 1,
      message: result,
    });
  } catch (err) {
    logger.error('Error in DeleteJWT', { message: err });
    response.status(400).send({
      status: 0,
      message: err instanceof Error ? err.message : 'Internal server error',
    });
  }
}

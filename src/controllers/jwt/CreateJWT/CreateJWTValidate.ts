import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const CreateJWTValidator = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('name', 'Name is required').notEmpty().isString(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },

];

export default CreateJWTValidator;

import { Response } from 'express';
import jwt from 'jsonwebtoken';
import { JWTModule } from '../../../modules/jwt';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function CreateJWT (
  request:ExtendedRequest,
  response:Response,
):Promise<string|void>{
  try {
    const organization_id = request.organization_id as string;
    const userRole = request.UserRole?.role as string;
    const name = request.body.name as string;
    // Const expTime = request.body.expiration?request.body.expiration:process.env.JWT_TOKEN_EXP
    // Const secretKey = process.env.JWT_TOKEN as string;
    // Const expTime = process.env.JWT_TOKEN_EXP;
    // Const token = jwt.sign({} as object, secretKey, { expiresIn: expTime});

    const secretKey = process.env.JWT_TOKEN as string;

    const expTime = process.env.JWT_TOKEN_EXP as any;

    // Fix 1: Properly type the payload and options
    const payload = {} as jwt.JwtPayload;
    const options: jwt.SignOptions = {
      expiresIn: expTime,
    };

    const token = jwt.sign(payload, secretKey, options);

    const jwtModule = new JWTModule();
    await jwtModule.CreateJWT(token, organization_id, userRole, name);
    response.status(200).send({status: 1, data: token});
  } catch (err){
    logger.error('Internal server error', {message: err});
    response.status(500).send({status: 0, message: 'Internal server error'});
  }
}

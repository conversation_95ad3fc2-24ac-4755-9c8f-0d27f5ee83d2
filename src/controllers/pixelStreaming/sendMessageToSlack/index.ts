import { Request, Response } from 'express';
import { sendMessageToChannel } from '../../../helpers/slackMessenger';
import logger from '../../../config/logger';

export async function sendToSlack (
  request: Request,
  response: Response,
): Promise<void> {
  try {
    const requestData = request.body;
    console.log(requestData);
    const webhookUrl = process.env.SLACK_WEBHOOKURL as string;
    const heading = 'PixelStreamingReport';
    const messageData = JSON.stringify(
      {
        instanceName: requestData.instance_name,
        resourceName: requestData.resource_name,
        message: requestData.message,
      },
      null,
      2,
    );

    sendMessageToChannel(webhookUrl, heading, messageData)
      .then(() => {
        console.log(response);

        response.status(200).send({
          status: 1,
          message: 'Message Send Successfully',
        });
      })
      .catch((error) => {
        logger.error('Error', {message: error.message});
        response.send({ status: 0, error: error.message });
      });

    // Response.status(200).send({ status: 1, data: requestData });
  } catch (error) {
    logger.error('Unable to retrieve data', {message: error});
    response.status(400).send('Unable to retrieve data');
  }
}

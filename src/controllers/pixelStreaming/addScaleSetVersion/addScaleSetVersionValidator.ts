import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const addScaleSetVersionValidate = [
  body('project_id', 'Project ID is required').notEmpty(),
  body('organization_id', 'Organization Id is required').notEmpty(),
  body('unrealApplicationDownloadUri', 'Unreal URI is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default addScaleSetVersionValidate;

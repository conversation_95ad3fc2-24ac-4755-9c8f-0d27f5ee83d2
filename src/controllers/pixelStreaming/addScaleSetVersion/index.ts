import  { ScaleSetModule } from '../../../modules/pixelstreaming';
import { Request, Response } from 'express';
import { ProjectModule } from '../../../modules/projects';

export async function addScaleSetVersion (req: Request, res: Response):Promise<void> {
  const project_id = req.body.project_id as string;
  const organization_id = req.body.organization_id as string;
  if (req.body.unrealApplicationDownloadUri){
    const defaultScalesetObject = {
      'version': 7,
      'instancesPerNode': 1,
      'resolutionWidth': 1920,
      'resolutionHeight': 1080,
      'pixelstreamingApplicationName': '',
      'fps': 30,
      'unrealApplicationDownloadUri': req.body.unrealApplicationDownloadUri,
      'msImprovedWebserversDownloadUri': `https://afuekadmin.blob.core.windows.net/zips
      /msImprovedWebservers.zip?sv=2023-08-03&st=2023-10-10T06%3A21%3A05Z&se=2023-10-10T06%3A56%3A05Z&sr=b&sp=
      r&sig=wUKqVkc%2F7juktgEOW%2BuzNPY%2F7PrmPecU0bGMY%2BVelRc%3D`.replace(/\s+/g, ''),
      'msPrereqsDownloadUri': `https://afuekadmin.blob.core.windows.net/zips/msPrereqs.zip
      ?sv=2023-08-03&st=2023-10-10T06%3A21%3A05Z&se=2023-10-10T06%3A56%3A05Z&sr=b&sp=r&sig=N44DHdSGEUS
      3dduw07L8OqioUB%2BVzESDHyd%2FpcEP5%2Fg%3D`.replace(/\s+/g, ''),
      'enableAutoScale': true,
      'instanceCountBuffer': 1,
      'percentBuffer': 100,
      'minMinutesBetweenScaledowns': 10,
      'scaleDownByAmount': 1,
      'minInstanceCount': 1,
      'maxInstanceCount': 2,
      'stunServerAddress': 'stun:stun.l.google.com:19302',
      'turnServerAddress': 'turn:************:19303',
      'turnUsername': 'propvr',
      'turnPassword': 'revsmart@123',
    };
    const projectModule = new ProjectModule(organization_id);
    const { resourceGroupName, vmScaleSetName } = await projectModule.getVMDetails(project_id) || {};
    if (!resourceGroupName || !vmScaleSetName) {
      res.status(500).send({message: 'Resource group name does not exist on project'});
      return;
    }

    const ScaleSetObj = new ScaleSetModule(resourceGroupName, vmScaleSetName);
    const addedScaleSet = await ScaleSetObj.addScaleSetVersion(defaultScalesetObject, project_id, organization_id);
    console.log(addedScaleSet);
    if (addedScaleSet===true){
      res.status(201).send({ status: 1, message: 'Successfully  created scaleset version' });
    } else {
      res.status(201).send({ status: 0, message: 'Error creating scaleset version'});
    }
  } else {
    res.status(400).send({message: 'Unreal URI Field Required', data: []});
  }
}

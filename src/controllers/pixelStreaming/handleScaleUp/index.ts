import { ScaleSetModule } from '../../../modules/pixelstreaming';
import { SessionModule } from '../../../modules/sessions';
import { ProjectModule } from './../../../modules/projects/index';
import { Request, Response } from 'express';

export async function handleScaleUp (request: Request, response: Response): Promise<void> {
  const organization_id = request.headers.organization as string;
  const { project_id, session_id } = request.body;

  if (!organization_id) {
    response.status(400).send({ status: 0, error: 'No organization found' });
    return;
  }

  if (!project_id) {
    response.status(400).send({ status: 0, error: 'Missing project_id' });
    return;
  }

  const projectModule = new ProjectModule(organization_id);
  const sessionModule = new SessionModule();

  try {
    // Retrieve project details and validate required fields
    const project = await projectModule.getProjectById(project_id);
    if (!project) {
      response.status(404).send({ status: 0, error: 'Project not found' });
      return;
    }

    const VMDetails = await projectModule.getVMDetails(project_id);
    if (!VMDetails?.resourceGroupName || !VMDetails?.vmScaleSetName || VMDetails.minInstanceCount === undefined) {
      response.status(400).send({ status: 0, error: 'Invalid project VM details or min instance count missing' });
      return;
    }

    if (!project.projectSettings?.pixelstreaming?.pixel_streaming_endpoint) {
      response.status(400).send({ status: 0, error: 'Pixel streaming endpoint not configured' });
      return;
    }

    // Initialize ScaleSetModule and get access token
    const scalesetModule = new ScaleSetModule(VMDetails.resourceGroupName, VMDetails.vmScaleSetName);
    const vm_accesstoken = await scalesetModule.getAccessToken();
    if (!vm_accesstoken) {
      response.status(400).send({ status: 0, error: 'Unable to get VM Access Token' });
      return;
    }

    // Get the current capacity of the VM scale set
    const currentCapacity = await scalesetModule.getCurrentCapacity(vm_accesstoken);
    if (currentCapacity === null || currentCapacity === undefined) {
      response.status(400).send({ status: 0, error: 'Unable to get VM current capacity' });
      return;
    }

    // Retrieve session details
    const session = await sessionModule.getSessionById(session_id);
    if (!session?.schedule_time) {
      response.status(400).send({ status: 0, error: 'Session not found' });
      return;
    }

    const scaleup_date = new Date(session.schedule_time);
    const next30Minutes = new Date(scaleup_date.getTime() + (30 * 60000));

    // Check for session availability and calculate required capacity
    const availabilityConfig = {
      organization_id,
      project_id,
      start_time: scaleup_date,
      end_time: next30Minutes,
      maxConcurrentSessions: project.projectSettings.pixelstreaming.max_concurrent_sessions || 10,
    };
    const activeSessionCount = await sessionModule.checkAvailability(availabilityConfig);

    const requiredCapacity = Math.max(activeSessionCount.activeSessions, VMDetails.minInstanceCount);

    // Scale up if current capacity is less than required
    if (currentCapacity < requiredCapacity) {
      await scalesetModule.updateVmScaleSetCapacity(vm_accesstoken, requiredCapacity);
      response.status(200).send({ status: 1, message: `Scaled up to ${requiredCapacity} instances.` });
    } else {
      response.status(200).send({ status: 1, message: 'Required capacity already available. No scaling needed' });
    }
  } catch (error) {
    console.error('Error during scaling operation:', error);
    response.status(500).send({ status: 0, error: 'Internal server error' });
  }
}

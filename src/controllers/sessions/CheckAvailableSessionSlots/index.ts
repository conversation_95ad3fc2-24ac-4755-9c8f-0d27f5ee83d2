import { Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';

export async function CheckAvailableSessionSlots (
  request: ExtendedRequest,
  response: Response,
):Promise<void>{
  const organization_id = request.organization_id as string;
  const startTime = new Date(request.body.start_time);
  const endTime = new Date(request.body.end_time);

  const session = new SessionModule();
  const IsAuthenticated = request.IsAuthenticated;
  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  try {
    const sessionResult = await session.CheckAvailableSessionSlots(organization_id, startTime, endTime);
    console.log('sssss', sessionResult);

    if (sessionResult){
      response.status(201).json({ status: 1, data: sessionResult });
    } else {
      response.status(500).json({ status: 0, error: 'Error CheckAvailableSessionSlots: ' });
    }
  } catch (err){
    logger.error('Error while creating session', { message: err });
    response.status(500).json({ status: 0, error: 'Error CheckAvailableSessionSlots : ' + err });
  }
}

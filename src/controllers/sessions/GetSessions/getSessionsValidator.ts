import { Request, Response, NextFunction } from 'express';
import { validationResult, header, query } from 'express-validator';
const GetSessionFilter = ['previous', 'scheduled', 'upcoming'];
const GetSessionsValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  query('type', 'Invalid Session Type value. Please ensure that you are using a valid session type value').optional()
    .isIn(GetSessionFilter),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default GetSessionsValidate;

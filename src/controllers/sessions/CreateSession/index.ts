import logger from '../../../config/logger';
import { ProjectModule } from '../../../modules/projects';
import { SessionModule, invalidateSessionAPIs } from '../../../modules/sessions';
import { TaskModule } from '../../../modules/tasks';
import { ExtendedRequest } from '../../../types/extras';
import { Session, SessionStatus, createSessionInput } from '../../../types/session';
import { Response } from 'express';

async function setupScalingTasks (sessionData: Session) {
  const task = new TaskModule();
  const queueName = 'auto-scaler';
  const scaling_payload = { project_id: sessionData.project_id, session_id: sessionData._id };
  const scalingheaders = {
    'organization': sessionData.organization_id,
  };
  const scheduledTime = sessionData.schedule_time as string;
  const slottime = scheduledTime.replace(/[:.]/g, '-'); // For task naming
  // Set task times for scaling up and scaling down
  let scaleUpTime = new Date(scheduledTime);
  scaleUpTime.setMinutes(scaleUpTime.getMinutes() - 30); // 30 minutes before session

  const scaleDownTime = new Date(scheduledTime);
  scaleDownTime.setMinutes(scaleDownTime.getMinutes() + 30); // After 30 minutes
  const currentTime = new Date();
  let scaleUpTaskId, scaleDownTaskId;
  // Ensure the scale-up task is in the future
  if (scaleUpTime < currentTime) {
    scaleUpTime = currentTime;
    scaleUpTaskId = `${sessionData.project_id}_scaleup_${slottime}_${sessionData._id}`;
    scaleDownTaskId = `${sessionData.project_id}_scaledown_${slottime}_${sessionData._id}`;
  } else {
    // Task IDs
    scaleUpTaskId = `${sessionData.project_id}_scaleup_${slottime}`;
    scaleDownTaskId = `${sessionData.project_id}_scaledown_${slottime}`;
  }
  // URLs for scaling up and scaling down
  const handleScaleUpURL = process.env.BASE_URL + 'scaleSets/handlescaleup';
  const handleScaleDownURL = process.env.BASE_URL + 'scaleSets/handlescaledown';
  // Check and create the scale-up task
  const scaleUptaskExists = await task.checkIfTaskExists(queueName, scaleUpTaskId);
  if (!scaleUptaskExists) {
    await task.createTask(queueName, handleScaleUpURL, scaling_payload, scaleUpTime.toISOString(),
      scaleUpTaskId, scalingheaders);
    logger.info('Auto Scaling Up task setup success');
  }

  // Check and create the scale-down task
  const scaleDownTaskExists = await task.checkIfTaskExists(queueName, scaleDownTaskId);
  if (!scaleDownTaskExists) {
    await task.createTask(queueName, handleScaleDownURL, scaling_payload,
      scaleDownTime.toISOString(), scaleDownTaskId, scalingheaders);
    logger.info('Auto Scaling Down task setup success');
  }
}
// Setup mail invitation task
async function setupMailingTasks (sessionData: Session) {
  const task = new TaskModule();
  const mailQueueName = 'mails';
  const hostInviteURL = `${process.env.BASE_URL}mailer/sendHostInvite`;
  const reminderURL = `${process.env.BASE_URL}mailer/sendReminder`;
  const payload = { session_id: sessionData._id };

  const inviteMailTime = new Date();
  inviteMailTime.setSeconds(inviteMailTime.getSeconds() + 15); // Schedule invite mail 15 seconds later

  const inviteMailTimeISO = inviteMailTime.toISOString();
  const inviteTaskId = `${sessionData._id}_invite`;
  const headers = {};
  try {
    await task.createTask(mailQueueName, hostInviteURL, payload, inviteMailTimeISO, inviteTaskId, headers);
    logger.info('Invite Mailing task setup success');
    // Schedule reminder mail 10 minutes before the session start
    const reminderMailTime = new Date(sessionData.start);
    reminderMailTime.setMinutes(reminderMailTime.getMinutes() - 10);
    const reminderMailTimeISO = reminderMailTime.toISOString();
    const reminderTaskId = `${sessionData._id}_reminder`;

    const now = new Date();

    // Only schedule the reminder if the reminder time is valid (i.e., in the future)
    if (reminderMailTime > now && reminderMailTime > inviteMailTime) {
      await task.createTask(mailQueueName, reminderURL, payload, reminderMailTimeISO, reminderTaskId, headers);
      logger.info('Reminder mail task setup success');
    } else {
      logger.warn('Reminder mail time is in the past or invalid, not scheduling reminder.');
    }
  } catch (error) {
    logger.error('Error in setting up mailing task', { message: error });
    throw new Error('Error while setting up mailing task');
  }
}
async function setupEndSessionTask (sessionData: Session) {
  const task = new TaskModule();
  const mailQueueName = 'session-ender';
  const hostInviteURL = `${process.env.BASE_URL}session/EndSessionbyTask`;
  const payload = { session_id: sessionData._id };

  // Check session status
  const invalidStatuses = [SessionStatus.CANCELLED, SessionStatus.ONHOLD, SessionStatus.ENDED];
  if (invalidStatuses.includes(sessionData.status)) {
    logger.info(`Session ${sessionData._id} is already in '${sessionData.status}' status. No action will be taken.`);
    return;
  }

  // Schedule the task 2 hours after the session start time
  const startTime = new Date(sessionData.start);
  const sessionEndTime = new Date(startTime.getTime() + (2 * 60 * 60 * 1000)); // Add 2 hours in milliseconds
  const sessionEndTimeISO = sessionEndTime.toISOString();
  const sessionEndTaskId = `${sessionData._id}_ender`;

  try {
    await task.createTask(mailQueueName, hostInviteURL, payload, sessionEndTimeISO, sessionEndTaskId, {});
    logger.info(`End Session task for session ${sessionData._id} scheduled successfully.`);
  } catch (error) {
    logger.error(`Error in setting up end session task for session ${sessionData._id}`, { message: error });
    throw new Error('Error while setting up session end task.');
  }
}

export async function CreateSession (
  request: ExtendedRequest,
  response: Response,
): Promise<Session | void> {
  const organization_id = request.organization_id as string;
  const IsAuthenticated = request.IsAuthenticated;
  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }

  const session = new SessionModule();
  const project = new ProjectModule(organization_id);

  const createSessionData: createSessionInput = {
    user_id: IsAuthenticated.uid,
    source: request.body.source,
    is_scheduled: request.body.is_scheduled,
    project_id: request.body.project_id || undefined,
    organization_id: organization_id,
    duration: request.body.duration || undefined,
    schedule_time: request.body.schedule_time,
    referrer: request.headers.referer,
    is_reserved: request.body.is_reserved || false,
  };

  // Check if schedule_time has already passed
  const now = new Date();
  const schedule_time = new Date(request.body.schedule_time);
  if (createSessionData.is_scheduled && (schedule_time < now)) {
    response.status(400).send({ status: 0, message: 'Scheduled time has already passed' });
    return;
  }

  try {
    const sessionData = await session.CreateSession(createSessionData);
    if (sessionData) {
      const VMDetails =sessionData.project_id ? await project.getVMDetails(sessionData.project_id):null;
      // Early join session - scaling task scheduling
      if (sessionData.is_scheduled && sessionData.schedule_time && VMDetails && VMDetails.autoScale) {
        if (!VMDetails || !VMDetails.resourceGroupName || !VMDetails.vmScaleSetName){
          response.status(400).send({ status: 0, message: 'Resource Group Name or scaleset name missing' });
          return;
        }
        await setupScalingTasks(sessionData);
      }
      await setupMailingTasks(sessionData);
      await setupEndSessionTask(sessionData);

      response.status(201).json({ status: 1, data: sessionData });

      // Invalidate session APIs (non-blocking)
      invalidateSessionAPIs().then((res) => {
        logger.info('Session APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating session APIs', { error: err });
      });
    }
  } catch (error) {
    logger.error('Error while creating session', { message: error });
    response.status(500).json({ status: 0, error: 'Error while creating session: ' + error });
  }
}

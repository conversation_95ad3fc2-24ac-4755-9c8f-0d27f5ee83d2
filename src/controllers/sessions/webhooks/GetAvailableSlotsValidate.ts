import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const GetAvailableSlotsValidate = [
  body('project_id', 'Project ID is required').notEmpty(),
  body('date', 'Date is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default GetAvailableSlotsValidate;

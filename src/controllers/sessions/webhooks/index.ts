import { ExtendedRequest } from '../../../types/extras';
import { SessionModule } from '../../../modules/sessions';
import { Response } from 'express';
import { Session, SessionSource, bookSessionInput } from '../../../types/session';
import { UserModule } from '../../../modules/user';
import logger from '../../../config/logger';

export async function GetAvailableSlots (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = request.organization_id as string;
  const getSlotsObj = {
    project_id: request.body.project_id,
    date: request.body.date,
    organization_id: organization_id,
    zone: request.body.zone,
  };
  session.getAvailableSlots(getSlotsObj).then((availableSlots) => {
    response.status(200).json({ status: 1, data: availableSlots });
  }).catch((err) => {
    logger.error('Unable to fetch available slots', {message: err});
    response.status(404).json({ status: 0, error: 'Unable to fetch available slots' + err });
  });

}
export async function GetMonthlySlots (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = request.organization_id as string;
  console.log(request.organization_id);
  // Ensure dates are in ISO string format and valid
  const fromDate = request.body.from_date;
  const toDate = request.body.to_date;
  const getSlotsObj = {
    project_id: request.body.project_id,
    from_date: fromDate,
    to_date: toDate,
    organization_id: organization_id,
    zone: request.body.zone,
  };
  console.log(getSlotsObj);
  session.getAvailableSlotsForDateRange(getSlotsObj).then((availableSlots) => {
    response.status(200).json({ status: 1, data: availableSlots });
  }).catch((err) => {
    logger.error('Unable to fetch available slots', {message: err});
    response.status(404).json({ status: 0, error: 'Unable to fetch available slots' + err });
  });

}
export async function BookSession (
  request: ExtendedRequest,
  response: Response,
): Promise<Session | void> {
  const organization_id = request.organization_id as string;
  const session = new SessionModule();
  const bookSessionData : bookSessionInput = {
    project_id: request.body.project_id,
    type: request.body.type,
    source: SessionSource.API,
    is_scheduled: request.body.is_scheduled,
    description: request.body.description,
    organization_id: organization_id,
    schedule_time: request.body.schedule_time,
    config: request.body.config,
    referrer: request.headers.referer,
  };
  console.log(request.headers.referer);
  await session
    .bookSession(bookSessionData)
    .then((sessionData) => {
      response.status(201).json({ status: 1, data: sessionData });
    })
    .catch((error: Error) => {
      logger.error('Error while creating the project', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the project'+ error });
    });
}
export async function GetAnonymousSessions (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const session = new SessionModule();
  const sessionData = await session.getAnonymousSessions(project_id);
  if (sessionData){
    response.status(200).json({ status: 1, data: sessionData });
  } else {
    logger.error('No Anonymous Sessions');
    response.status(404).json({ status: 0, error: 'No Anonymous Sessions' });
  }
}
export async function AssignSession (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const user = new UserModule();
  const user_email = request.body.user_email as string;
  const user_id = await user.getUserIdByEmail(user_email);
  if (!user_id) {
    response.status(404).send({ status: 0, error: 'User not found in organization.' });
    return;
  }
  const session_id = request.body.session_id as string;
  session
    .AssignSession(user_id, session_id)
    .then((sessionData) => {
      response.status(200).send({ status: 1, data: sessionData });
    })
    .catch((error) => {
      logger.error('Error in AssignSession', {message: error});
      response.status(409).send({ status: 0, error: 'Error while updating session' + error });
    });
}

import logger from '../../../config/logger';
import { SessionModule } from '../../../modules/sessions';
import { Request, Response } from 'express';

export async function GetSession (
  request: Request,
  response: Response,
): Promise<void> {
  const sessionId= request.body.sessionId;
  const session = new SessionModule();

  const sessionData = await session.getSessionById(sessionId);

  if (sessionData) {
    response.status(200).json({ status: 1, data: sessionData });
  } else {
    logger.error('Session not found');
    response.status(404).json({ status: 0, error: 'Session not found' });
  }
}

import logger from '../../../config/logger';
import { SessionModule } from '../../../modules/sessions';
import { ExtendedRequest } from '../../../types/extras';
import { Session, bookSessionInput } from '../../../types/session';
import { Response } from 'express';
export async function BookSession (
  request: ExtendedRequest,
  response: Response,
): Promise<Session | void> {
  const organization_id = request.organization_id as string;
  const IsAuthenticated = request.IsAuthenticated;
  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  const session = new SessionModule();
  const bookSessionData : bookSessionInput = {
    project_id: request.body.project_id,
    type: request.body.type,
    source: request.body.source,
    is_scheduled: request.body.is_scheduled,
    description: request.body.description,
    organization_id: organization_id,
    schedule_time: request.body.schedule_time,
    config: request.body.config,
    referrer: request.headers.referer,
  };
  console.log(request.headers.referer);
  await session
    .bookSession(bookSessionData)
    .then((sessionData) => {
      response.status(201).json({ status: 1, data: sessionData });
    })
    .catch((error: Error) => {
      logger.error('Error in BookSession', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the project'+ error });
    });
}

import { Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function CheckInstanceAvailability (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = request.organization_id as string;

  const { start_time, end_time, exclude_session_id } = request.body; // ✅ extract it

  if (!start_time ) {
    response.status(400).json({
      status: 0,
      error: 'start_time are required',
    });
    return;
  }

  try {
    const startTime = new Date(start_time);
    const endTime = new Date(end_time);

    if (endTime <= startTime) {
      response.status(400).json({
        status: 0,
        error: 'end_time must be after start_time',
      });
      return;
    }

    const availabilityConfig = {
      organization_id,
      start_time: startTime,
      end_time: endTime,
      maxConcurrentSessions: 0,
      exclude_session_id, // ✅ pass it here
    };

    const availability = await session.CheckInstanceAvailability(availabilityConfig);

    const isAvailable = availability.availableSessions > 0;

    response.status(200).json({
      status: 1,
      data: {
        available: isAvailable,
        available_instances: availability.availableSessions,
        active_sessions: availability.activeSessions,
        time_slot: availability.slotStart,
        conflicting_session_ids: availability.conflictingSessionIds,  // ← Add this
        message: isAvailable
          ? 'Instance available for the requested time slot'
          : 'No instances available for the requested time slot',
      },
    });
  } catch (error) {
    logger.error('Unable to check instance availability', { message: error });
    response.status(500).json({
      status: 0,
      error: 'Unable to check instance availability: ' + error,
    });
  }
}

import { body, header, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const CheckInstanceAvailabilityValidator = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('start_time')
    .notEmpty()
    .withMessage('start_time is required')
    .isISO8601()
    .withMessage('start_time must be a valid ISO 8601 date'),

  body('end_time')
    .optional()
    .isISO8601()
    .withMessage('end_time must be a valid ISO 8601 date'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default CheckInstanceAvailabilityValidator;

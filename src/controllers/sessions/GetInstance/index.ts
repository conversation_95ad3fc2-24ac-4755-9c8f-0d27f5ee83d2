import { ExtendedRequest } from '../../../types/extras';
import { Session } from '../../../types/session';
import { Response } from 'express';
export async function CreateSession (
  request: ExtendedRequest,
  response: Response,
): Promise<Session | void> {
  const IsAuthenticated = request.IsAuthenticated;
  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  interface sessiondata {
    project_id: string,
    user_id: string,
    session_id: string,
    is_scheduled?: boolean
  }
  const createSessionData : sessiondata = {
    project_id: request.body.project_id,
    user_id: IsAuthenticated.uid,
    session_id: request.body.session_id,
  };

  // Check if schedule_time has already passed
  const now = new Date();
  const schedule_time = new Date(request.body.schedule_time);
  if (createSessionData.is_scheduled && (schedule_time < now)) {
    response.status(400).send({ status: 0, message: 'Scheduled time has already passed' });
    return;
  }

}

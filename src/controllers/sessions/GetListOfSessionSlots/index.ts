import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { SessionModule } from '../../../modules/sessions';

export async function getListOfSessionSlots (
  request:ExtendedRequest,
  response:Response,
):Promise<void>{
  const session = new SessionModule();
  const organization_id = request.organization_id as string;
  const userSelectedDate = request.body.date as Date;
  try {
    session.getSessionSlots(organization_id, userSelectedDate).then((res) => {
      if (res){
        response.status(200).send({status: 1, data: res});
      }
    }).catch((err) => {
      throw new Error(`Error in getListOfSessionSlots ${err}`);
    });
  } catch (error){
    if (error instanceof Error){
      response.status(404).send({status: 0, error: `Error in getListOfSessionSlots:${error.message}`});
    }
  }
}

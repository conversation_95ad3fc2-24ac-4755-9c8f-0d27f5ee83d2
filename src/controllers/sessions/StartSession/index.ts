import { Request, Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
export async function StartSession (
  request: Request,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const session_id = request.body.session_id;
  const organization_id = await session.getOrganizationId(session_id);
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }

  session
    .StartSession(session_id)
    .then((sessionData) => {
      response.send({ status: 1, data: sessionData });
    })
    .catch((error) => {
      logger.error('Error in StartSession', {message: error});
      response.send({ status: 0, error: 'Unable to start session' });
    });
}

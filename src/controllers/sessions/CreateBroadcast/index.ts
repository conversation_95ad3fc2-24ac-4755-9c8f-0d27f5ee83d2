import logger from '../../../config/logger';
import { SessionModule, invalidateSessionAPIs } from '../../../modules/sessions';
import { ExtendedRequest } from '../../../types/extras';
import { Session, SessionSource, SessionType, createSessionInput } from '../../../types/session';
import { Response } from 'express';
import { generateGuestUserId } from '../../../helpers/guestUser';

/**
 * Create a public broadcast session for ALE projects
 * No authentication required - this is a public endpoint
 *
 * Host creates session from PropVR viewer page → shares link → Guests join via CreateLead API
 *
 * @param request - Express request with body containing project_id, organization_id, and name
 * @param response - Express response
 */
export async function CreateBroadcastSession (
  request: ExtendedRequest,
  response: Response,
): Promise<Session | void> {
  try {
    const { project_id, organization_id, name, duration, description } = request.body;

    logger.info('CreateBroadcastSession Called', {
      project_id,
      organization_id,
      name,
      duration,
      description,
    });

    // Step 1: Generate a broadcast host user ID
    // This is the HOST's temporary user ID (not the guests who will join later)
    const broadcast_host_id = generateGuestUserId(name);

    logger.info('Generated broadcast host user ID', {
      name,
      broadcast_host_id,
    });

    // Step 2: Create the broadcast session
    const session = new SessionModule();

    const createSessionData: createSessionInput = {
      user_id: broadcast_host_id,
      project_id: project_id,
      organization_id: organization_id,
      source: SessionSource.BROADCAST,
      type: SessionType.ALE,
      is_scheduled: false,
      duration: duration,
      description: description || `Broadcast session by ${name}`,
      schedule_time: null,
      referrer: request.headers.referer,
      tag: 'broadcast',
    };

    const sessionData = await session.CreateSession(createSessionData);

    if (!sessionData) {
      logger.error('Failed to create broadcast session');
      response.status(500).json({
        status: 0,
        error: 'Failed to create broadcast session',
      });
      return;
    }

    logger.info('Broadcast session created successfully', {
      session_id: sessionData._id,
      broadcast_host_id,
      project_id,
      organization_id,
    });

    // Return session data in the same format as regular CreateSession
    response.status(201).json({
      status: 1,
      data: {
        _id: sessionData._id,
        status: sessionData.status,
        pixel_duration_minutes: sessionData.pixel_duration_minutes,
        code: sessionData.code,
        start: sessionData.start,
        type: sessionData.type,
        scheduled_end_time: sessionData.scheduled_end_time,
        invite_link: sessionData.invite_link,
        organization_id: sessionData.organization_id,
        user_id: sessionData.user_id, // This contains the guest_<uuid> format
        source: sessionData.source,
        last_interaction_time: sessionData.last_interaction_time,
        is_scheduled: sessionData.is_scheduled,
        schedule_time: sessionData.schedule_time,
        end_time: sessionData.end_time,
        instance_start_time: sessionData.instance_start_time,
        instance_end_time: sessionData.instance_end_time,
        participants: sessionData.participants,
        is_pixelstreaming_active: sessionData.is_pixelstreaming_active,
        is_reserved: sessionData.is_reserved,
        host_name: name, // Additional field for analytics
      },
    });

    // Invalidate session APIs (non-blocking)
    invalidateSessionAPIs().then((res) => {
      logger.info('Session APIs invalidated successfully', { result: res });
    }).catch((err) => {
      logger.error('Error invalidating session APIs', { error: err });
    });

  } catch (error) {
    logger.error('Error while creating broadcast session', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    response.status(500).json({
      status: 0,
      error: 'Error while creating broadcast session: ' +
        (error instanceof Error ? error.message : 'Unknown error'),
    });
  }
}

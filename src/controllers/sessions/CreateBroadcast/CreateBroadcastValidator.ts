import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const CreateBroadcastValidate = [
  body('project_id', 'Project ID is required').notEmpty().isString(),
  body('organization_id', 'Organization ID is required').notEmpty().isString(),
  body('name', 'Name is required').notEmpty().isString().isLength({ min: 1, max: 100 })
    .withMessage('Name must be between 1 and 100 characters'),
  body('duration', 'Duration must be a number between 1 and 240')
    .optional()
    .isInt({ min: 1, max: 240 }),
  body('description', 'Description must not exceed 500 characters')
    .optional()
    .isString()
    .isLength({ max: 500 }),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default CreateBroadcastValidate;

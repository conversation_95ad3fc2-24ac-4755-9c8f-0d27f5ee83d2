import { ExtendedRequest } from '../../../types/extras';
import { SessionModule } from '../../../modules/sessions';
import { Response } from 'express';
import logger from '../../../config/logger';

export async function checkAvailability (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = request.organization_id as string;
  const getSlotsObj = {
    project_id: request.body.project_id || undefined,
    start_time: new Date(request.body.start_time),
    end_time: new Date(request.body.end_time),
    organization_id: organization_id,
    maxConcurrentSessions: 10,
  };
  session.checkAvailability(getSlotsObj).then((availableSlots) => {
    response.status(200).json({ status: 1, data: availableSlots });
  }).catch((err) => {
    logger.error('Unable to fetch available slots', {message: err});
    response.status(404).json({ status: 0, error: 'Unable to fetch available slots' + err });
  });

}

import { Request, Response } from 'express';
import { SessionModule, invalidateSessionAPIs } from '../../../modules/sessions';
import logger from '../../../config/logger';
import { UpdateSessionInput } from '../../../types/session';

export async function UpdateSession (
  request: Request,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = await session.getOrganizationId(request.body.session_id);

  const update_session_data: UpdateSessionInput = {
    session_id: request.body.session_id,
    lead_id: request.body.lead_id,
    duration_minutes: request.body.duration_minutes,
    pixel_duration_minutes: request.body.pixel_duration_minutes,
    is_pixelstreaming_active: request.body.is_pixelstreaming_active,
    type: request.body.type,
    project_id: request.body.project_id,
    is_reserved: request.body.is_reserved,
    instance_start_time: request.body.instance_start_time,
  };
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }

  session
    .UpdateSessionDuration(update_session_data)
    .then(async (sessionData) => {
      response.send({ status: 1, data: sessionData });

      // Invalidate session APIs (non-blocking)
      invalidateSessionAPIs().then((res) => {
        logger.info('Session APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating session APIs', { error: err });
      });
    })
    .catch((error) => {
      logger.error('Error in UpdateSession', {message: error});
      response.send({ status: 0, error: 'Error while updating session' + error });
    });
}

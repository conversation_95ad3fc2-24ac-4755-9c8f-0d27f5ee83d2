import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { SessionType } from '../../../types/session';

const updateSessionValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('session_id', 'Session ID is required').notEmpty(),
  body('duration_minutes', 'Duration in minutes is required').notEmpty(),
  body('project_id').optional(),
  body('type').optional().isIn(Object.values(SessionType)),
  body('is_pixelstreaming_active').optional().isBoolean(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default updateSessionValidate;

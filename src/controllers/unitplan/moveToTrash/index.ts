import { ExtendedRequest } from '../../../types/extras';
import { unitplanModule, invalidateUnitplanAPIs } from '../../../modules/unitplan';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const unitplan = new unitplanModule(project_id, organization_id);
  const unitplan_id = request.body.unitplan_id;
  const timeStamp = request.body.timeStamp;

  try {
    // Get unit names linked to this unitplan before moving to trash
    const linkedUnitNames = await unitplan.getUnitNamesByUnitplanId(unitplan_id);

    await unitplan
      .moveToTrash(unitplan_id, project_id, organization_id, timeStamp)
      .then(() => {
        const message = linkedUnitNames.length > 0
          ? 'Unitplan moved to trash successfully. Please update the following units that were linked to this unitplan'
          : 'Unitplan moved to trash successfully. No units were linked to this unitplan.';

        response.status(201).json({
          status: 1,
          message: message,
          linkedUnits: linkedUnitNames,
          unitCount: linkedUnitNames.length,
        });

        invalidateUnitplanAPIs(organization_id, project_id, unitplan_id).then((res) => {
          logger.info('Unitplan APIs invalidated successfully', { result: res });
        }).catch((err) => {
          logger.error('Error invalidating unitplan APIs', { error: err });
        });
      })
      .catch((error: Error) => {
        logger.error('Error in moveToTrash', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error moving unitplans to trash: '+ error });
      });
  } catch (error) {
    logger.error('Error in moveToTrash', {message: error});
    response
      .status(500)
      .json({ status: 0, error: 'Error getting linked units: ' + error });
  }
}

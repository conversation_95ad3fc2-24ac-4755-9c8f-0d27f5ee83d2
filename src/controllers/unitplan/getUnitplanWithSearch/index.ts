import { Request, Response } from 'express';
import { unitListResponse } from '../../../types/units';
import { unitplanModule } from '../../../modules/unitplan';
import logger from '../../../config/logger';

export async function getUnitplanWithSearch (
  request: Request,
  response: Response,
): Promise<unitListResponse | void> {

  const {project_id, limit, pageSize, searchText } = request.body;

  // Convert string values to numbers for pagination, keep null if not provided
  const limitNum = limit ? parseInt(limit as string) : null;
  const pageSizeNum = pageSize ? parseInt(pageSize as string) : null;
  const searchTextStr = searchText as string || '';

  logger.info('getUnitplanWithSearch Called', {
    project_id,
    limitNum,
    pageSizeNum,
    searchTextStr,
  });

  const { organization_id } = request.body;
  const unitplan = new unitplanModule(project_id, organization_id);

  await unitplan
    .getUnitplanWithSearch(project_id, limitNum, pageSizeNum, searchTextStr)
    .then((unitplanData) => {
      response.status(200).json({ status: 1, data: unitplanData });
    })
    .catch((error: Error) => {
      logger.error('Error in getUnitplanWithSearch', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while fetching the unit plans' });
      console.error(error);
    });
}

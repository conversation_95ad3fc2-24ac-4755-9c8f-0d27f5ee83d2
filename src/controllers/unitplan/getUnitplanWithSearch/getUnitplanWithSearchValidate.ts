import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const getUnitplanWithSearchValidate = [
  body('project_id', 'Project ID is required').notEmpty(),
  body('searchText').optional({ checkFalsy: true }),
  body('limit', 'Limit must be numeric if provided').optional().isNumeric(),
  body('pageSize', 'Page Size must be numeric if provided').optional().isNumeric(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default getUnitplanWithSearchValidate;

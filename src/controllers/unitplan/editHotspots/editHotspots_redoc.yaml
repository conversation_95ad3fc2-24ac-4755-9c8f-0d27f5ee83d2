openapi: 3.0.3
info:
  title: PropVR Unitplan - Edit Hotspots API
  description: |
    ## **Edit Hotspots API**
    
    **For Property Developers & Real Estate Management**
    
    Edit Hotspots operations for unitplan management and configuration.
  version: 1.0.0

servers:
  - url: https://dashboard-api-prod-172924419383.us-central1.run.app/unitplan
    description: Production server
  - url: https://platform-backend-274706608007.us-central1.run.app/unitplan
    description: Staging server

security:
  - BearerAuth: []

paths:
  /editHotspots:
    post:
      operationId: editHotspots
      tags:
        - Unitplan
      summary: Edit Hotspots
      description: |
        **Edit Hotspots operation**
        
        This endpoint allows you to:
        - Perform edit hotspots operations
        - Manage unitplan data and configuration
        - Handle unitplan related functionality
        - Maintain data integrity and audit trails
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                id:
                  type: string
                  description: Unique identifier (for updates)
                  example: "507f1f77bcf86cd799439011"
                name:
                  type: string
                  description: Name or title
                  example: "Sample Edit Hotspots"
                description:
                  type: string
                  description: Description of the item
                  example: "Sample description for edit hotspots"
                project_id:
                  type: string
                  description: Associated project identifier
                  example: "507f1f77bcf86cd799439012"
                status:
                  type: string
                  description: Current status
                  example: "active"
                  enum: ["active", "inactive", "pending", "archived"]
                metadata:
                  type: object
                  description: Additional metadata
                  example: {
                    "category": "sample",
                    "priority": "high",
                    "tags": ["property", "management"]
                  }
            examples:
              create_example:
                summary: Create unitplan example
                value:
                  name: "New Unitplan"
                  description: "A comprehensive unitplan for property management"
                  project_id: "507f1f77bcf86cd799439012"
                  status: "active"
                  metadata:
                    category: "premium"
                    priority: "high"
                    tags: ["property", "management", "vr"]
              update_example:
                summary: Update unitplan example
                value:
                  id: "507f1f77bcf86cd799439011"
                  name: "Updated Unitplan"
                  description: "Updated description for unitplan"
                  status: "active"
                  metadata:
                    category: "standard"
                    priority: "medium"
      responses:
        '200':
          description: Operation completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: Edit Hotspots completed successfully
                  data:
                    $ref: '#/components/schemas/UnitplanResponse'
              examples:
                success:
                  summary: Successful edit hotspots response
                  value:
                    status: 1
                    message: "Edit Hotspots completed successfully"
                    data: {
                      "id": "507f1f77bcf86cd799439011",
                      "name": "Sample Unitplan",
                      "description": "Sample description for unitplan",
                      "project_id": "507f1f77bcf86cd799439012",
                      "status": "active",
                      "created_at": "2024-01-15T10:30:00Z",
                      "updated_at": "2024-01-15T14:45:00Z",
                      "metadata": {
                        "category": "premium",
                        "priority": "high",
                        "tags": ["property", "management", "vr"]
                      }
                    }
        '400':
          description: Bad request - Invalid input data
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Invalid input data
              examples:
                validation_error:
                  summary: Validation error example
                  value:
                    status: 0
                    error: "Required field 'name' is missing"
                invalid_format:
                  summary: Invalid format example
                  value:
                    status: 0
                    error: "Invalid data format provided"
        '401':
          description: Unauthorized - Invalid or missing authentication
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Unauthorized access
              examples:
                missing_token:
                  summary: Missing authentication token
                  value:
                    status: 0
                    error: "Authorization token is required"
                invalid_token:
                  summary: Invalid authentication token
                  value:
                    status: 0
                    error: "Invalid or expired token"
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Insufficient permissions
              examples:
                permission_denied:
                  summary: Permission denied example
                  value:
                    status: 0
                    error: "You don't have permission to perform this action"
        '404':
          description: Not found - Resource does not exist
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Resource not found
              examples:
                not_found:
                  summary: Resource not found example
                  value:
                    status: 0
                    error: "Unitplan not found with the provided ID"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Internal server error
              examples:
                server_error:
                  summary: Server error example
                  value:
                    status: 0
                    error: "An unexpected error occurred while processing edit hotspots"
                database_error:
                  summary: Database error example
                  value:
                    status: 0
                    error: "Database connection failed"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

  schemas:
    UnitplanResponse:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier
          example: "507f1f77bcf86cd799439011"
        name:
          type: string
          description: Name or title
          example: "Sample Unitplan"
        description:
          type: string
          description: Description
          example: "Sample description for unitplan"
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-15T10:30:00Z"
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T14:45:00Z"
        status:
          type: string
          description: Current status
          example: "active"
          enum: ["active", "inactive", "pending", "archived"]

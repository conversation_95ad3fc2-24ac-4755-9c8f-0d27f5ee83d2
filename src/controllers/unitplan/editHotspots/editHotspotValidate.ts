import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { hotspots } from '../../../types/unitplan';

const editHotspotValidator = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('unitplan_id', 'Unitplan ID is required').notEmpty(),
  body('hotspot_id', 'Hotspot ID is required').notEmpty(),
  body('hotspots')
    .exists().withMessage('hotspots is required')
    .isObject().withMessage('hotspots should be an object')
    .custom((value: any) => {
      if (value === null || typeof value !== 'object') {
        return true;
      }

      if (Object.keys(value).length === 0) {
        throw new Error('hotspots should not be a empty object');
      }

      const optionalKeys: (keyof hotspots)[] =
      ['text', 'x', 'y', 'scale', 'type', 'image_id', 'label_id', 'group_id', 'subGroup_id'];

      // Type check
      const errors: string[] = [];
      if ('text' in value && typeof value.text !== 'string') {
        errors.push('text must be a string');
      }

      if ('x' in value && typeof value.x !== 'number') {
        errors.push('x must be a number');
      }

      if ('y' in value && typeof value.y !== 'number') {
        errors.push('y must be a number');
      }

      if ('scale' in value && typeof value.scale !== 'string') {
        errors.push('scale must be a string');
      }

      if ('type' in value && typeof value.type !== 'string') {
        errors.push('type must be a string');
      }

      if ('image_id' in value && value.image_id !== null && typeof value.image_id !== 'string') {
        errors.push('image_id must be a string');
      }

      if ('label_id' in value && (typeof value.label_id !== 'string')) {
        errors.push('label_id must be a string');
      }

      if ('group_id' in value && (typeof value.group_id !== 'string')) {
        errors.push('group_id must be a string');
      }

      if ('subGroup_id' in value && (typeof value.subGroup_id !== 'string')) {
        errors.push('subGroup_id must be a string');
      }

      if (errors.length > 0){
        throw new Error(`Error: ${errors.join(', ')}`);
      }

      // Additional properties check
      const allowedKeys = optionalKeys;
      const extraKeys = Object.keys(value).filter((key) => !allowedKeys.includes(key as keyof hotspots));
      if (extraKeys.length > 0) {
        throw new Error(`Unexpected keys in hotspots: ${extraKeys.join(', ')}`);
      }

      return true;
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default editHotspotValidator;

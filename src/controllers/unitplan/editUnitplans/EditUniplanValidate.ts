import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { measurementType, unitplan_type } from '../../../types/unitplan';

// Interface UploadedFiles {
//   LowRes?: Express.Multer.File[]; // Make lowRes optional by adding '?'
//   HighRes?: Express.Multer.File[]; // Make highRes optional by adding '?'
// }

const EditUnitplanValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  body('unitplan_id')
    .optional()
    .notEmpty()
    .withMessage('Unitplan ID field cannot be empty');

  body('project_id')
    .optional()
    .notEmpty()
    .withMessage('Project ID field cannot be empty');

  body('type')
    .optional()
    .notEmpty()
    .withMessage('Type field cannot be empty');

  body('name').optional().notEmpty().withMessage('Name field cannot be empty');

  body('measurement')
    .optional()
    .notEmpty()
    .withMessage('Measurement field cannot be empty');

  body('measurement_type')
    .optional()
    .notEmpty()
    .withMessage('Measurement type field cannot be empty')
    .isIn(Object.values(measurementType))
    .withMessage(
      'Invalid measurement type. Please ensure that you are using a valid measurement type value',
    );

  body('balcony_measurement')
    .optional()
    .notEmpty()
    .withMessage('Balcony Measurement field cannot be empty');

  body('balcony_measurement_type')
    .optional()
    .notEmpty()
    .withMessage('Balcony Measurement type field cannot be empty')
    .isIn(Object.values(measurementType))
    .withMessage(
      'Invalid measurement type. Please ensure that you are using a valid measurement type value',
    );

  body('suite_area')
    .optional();

  body('suite_area_type')
    .optional();

  body('tour_id')
    .optional()
    .notEmpty()
    .withMessage('Tour ID field cannot be empty');

  body('bedrooms')
    .optional()
    .notEmpty()
    .withMessage('Bedroom field cannot be empty')
    .isIn(Object.values(unitplan_type))
    .withMessage(
      'Invalid type value. Please ensure that you are using a valid type value',
    );

  body('is_residential')
    .optional()
    .notEmpty()
    .withMessage('Is Residential field cannot be empty');

  body('bathrooms')
    .optional()
    .notEmpty()
    .withMessage('Bathrooms field cannot be empty');

  body('is_furnished')
    .optional()
    .notEmpty()
    .withMessage('Is Furnished field cannot be empty');
  body('thumbnail')
    .optional()
    .isArray({ min: 1 })
    .withMessage('thumbnail image field cannot be empty');

  body('image_url')
    .optional()
    .isArray({ min: 1 })
    .withMessage('image_url image field cannot be empty');

  body('is_commercial')
    .optional()
    .notEmpty()
    .withMessage('Is Confidential field cannot be empty');
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
    return;
  }

  next();
};

export default EditUnitplanValidate;

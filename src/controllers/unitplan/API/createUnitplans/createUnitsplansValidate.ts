import { Request, Response, NextFunction } from 'express';
import { exterior_type, measurementType, unitplan_type, unitType } from '../../../../types/unitplan';
import { body, validationResult } from 'express-validator';

interface UploadedFiles {
    unitplan_image: Express.Multer.File[];
}

const CreateUnitplanValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  const unitTypeVal = req.body.unit_type;
  if (files) {
    if ((req.body.unit_type === 'flat') && (!files.unitplan_image)) {
      res
        .status(400)
        .json({ error: 'unitplan_image field is required for flat unit type.' });
    } else {
      const requiredTextFields = [
        'name',
        'is_furnished',
        'unit_type',
        ...(unitTypeVal !== 'villa_floor' ? ['measurement', 'measurement_type', 'bedrooms'] : ['parent_unitplan']),
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        body('bedrooms', 'Invalid bedroom value. Please ensure that you are using a valid type value')
          .isIn(Object.values(unitplan_type)).run(req);
        body('measurement_type',
          'Invalid measurement type. Please ensure that you are using a valid measurement type value')
          .isIn(Object.values(measurementType)).run(req);
        body('unit_type', 'Invalid type value. Please ensure that you are using a valid type value')
          .isIn(Object.values(unitType)).run(req);
        body('exterior_type', 'Invalid exterior type value. Please ensure that you are using a valid type value')
          .isIn(Object.values(exterior_type)).run(req);
        body('balcony_measurement_type',
          'Invalid Balcony measurement type. Please ensure that you are using a valid measurement type value')
          .optional({checkFalsy: true}).isIn(Object.values(measurementType)).run(req);
        body('suite_area_type',
          'suite area type. Please ensure that you are using a valid measurement type value')
          .optional({checkFalsy: true}).isIn(Object.values(measurementType)).run(req);
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default CreateUnitplanValidate;

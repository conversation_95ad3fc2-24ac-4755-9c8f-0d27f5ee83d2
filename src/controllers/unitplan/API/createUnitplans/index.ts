import { Response } from 'express';
import { FileRequest } from '../../../../types/extras';
import { Types } from 'mongoose';
// Import fs from 'fs';
import { unitplanModule } from '../../../../modules/unitplan';
// Import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../../config/logger';
import { UploadUnitplanFiles } from '../../../../helpers/uploadFirebase';
import { resizeImage } from '../../../../helpers/storageUpload';

export default async function CreateUnitplan (
  request: FileRequest,
  response: Response,
): Promise<void> {
  interface unitplanInterface {
    project_id: string;
    building_id: string;
    type: string;
    name: string;
    measurement: number;
    measurement_type: string;
    tour_id: string;
    bedrooms: string;
    is_residential: boolean;
    bathrooms?: number;
    is_furnished: boolean;
    unit_type:string;
    suite_area:string,
    suite_area_type:string,
    parent_unitplan:string;
    order:number;
    scene_id?:string,
    gallery_id?:string,
    exterior_type?:string,
    style?: string
    balcony_measurement?: number,
    balcony_measurement_type?: string,
    is_commercial?: boolean
  }
  const reqbody: unitplanInterface = request.body;
  const project_id = request.headers.project_id as string;
  const organization_id = request.headers.organization as string;
  const unitplan = new unitplanModule(project_id, organization_id);
  const id = new Types.ObjectId();
  const requestFiles = request.files as { [fieldname: string]: Express.Multer.File[] } | undefined;
  if (reqbody.unit_type === 'flat' || reqbody.unit_type === 'villa_floor' ){
    if (!requestFiles || !requestFiles.unitplan_image) {
      response.status(400).json({ status: 0, message: 'unitplan_image is required for this unit type' });
      return;
    }
    let resizedThumbnail;
    try {
      resizedThumbnail = await resizeImage(requestFiles, 1280, 720);

      if (!resizedThumbnail) {
        response.status(500).json({ status: 0, message: 'Failed to resize image' });
        return;
      }
    } catch (error) {
      response.status(500).json({ status: 0, message: 'Error resizing image: ' + error });
      return;
    }

    // Create a new file structure with both original and resized images
    const filesToUpload = {
      unitplan_image: requestFiles.unitplan_image,
      unitplan_thumbnail: [resizedThumbnail],
    };

    UploadUnitplanFiles(filesToUpload, unitplan.storagepath + id)
      .then((urlObject: { [key: string]: string }) => {
        unitplan
          .createUnitplan({
            _id: id,
            project_id: reqbody.project_id,
            building_id: reqbody.building_id,
            type: reqbody.type,
            name: reqbody.name,
            thumbnail: urlObject.unitplan_thumbnail,
            image_url: urlObject.unitplan_image,
            measurement: reqbody.measurement && Number(reqbody.measurement),
            measurement_type: reqbody.measurement_type,
            tour_id: reqbody.tour_id,
            bedrooms: reqbody.bedrooms,
            is_residential: request.body.is_residential === 'true',
            is_furnished: request.body.is_furnished === 'true',
            bathrooms: reqbody.bathrooms && Number(reqbody.bathrooms),
            unit_type: reqbody.unit_type,
            exterior_type: reqbody.exterior_type,
            scene_id: reqbody.scene_id,
            gallery_id: reqbody.gallery_id?JSON.parse(reqbody.gallery_id):undefined,
            style: reqbody.style,
            balcony_measurement: reqbody.balcony_measurement && Number(reqbody.balcony_measurement),
            balcony_measurement_type: reqbody.balcony_measurement_type,
            suite_area: reqbody.suite_area,
            suite_area_type: reqbody.suite_area_type,
            is_commercial: reqbody.is_commercial || false,
          })
          .then(async (res) => {
            if (reqbody.unit_type !== 'villa_floor') {
              response.json({ status: 1, data: res });
            } else {
              unitplan.editUnitplan(reqbody.parent_unitplan, {
                floor_unitplans: id.toString(),
              }, 'append').then(() => {
                response.json({ status: 1, data: res });
              })
                .catch((err) => {
                  logger.error('Error in editUnitplan', {message: err});
                  response.json({ status: 0, message: err });
                });
            }
          })
          .catch((error) => {
            logger.error('Error in createUnitplan', {message: error});
            response.json({ status: 0, message: error });
          });
      })
      .catch((error) => {
        logger.error('Error in UploadUnitplanFiles', {message: error});
        response.json({ status: 0, message: 'error loggssss'+error });
      });
  } else {
    unitplan.createUnitplan({
      _id: id,
      project_id: reqbody.project_id,
      building_id: reqbody.building_id,
      type: reqbody.type,
      name: reqbody.name,
      measurement: reqbody.measurement && Number(reqbody.measurement),
      measurement_type: reqbody.measurement_type,
      tour_id: reqbody.tour_id,
      bedrooms: reqbody.bedrooms,
      is_residential: request.body.is_residential === 'true',
      is_furnished: request.body.is_furnished === 'true',
      bathrooms: reqbody.bathrooms && Number(reqbody.bathrooms),
      unit_type: reqbody.unit_type,
      exterior_type: reqbody.exterior_type,
      scene_id: reqbody.scene_id,
      gallery_id: reqbody.gallery_id?JSON.parse(reqbody.gallery_id):undefined,
      style: reqbody.style,
      balcony_measurement: reqbody.balcony_measurement && Number(reqbody.balcony_measurement),
      balcony_measurement_type: reqbody.balcony_measurement_type,
      is_commercial: reqbody.is_commercial || false,
      suite_area: reqbody.suite_area,
      suite_area_type: reqbody.suite_area_type,
    })
      .then(async (res) => {
        response.json({ status: 1, data: res });
      })
      .catch((error) => {
        logger.error('Error in createUnitplan', {message: error});
        response.json({ status: 0, message: error });
      });
  }
}

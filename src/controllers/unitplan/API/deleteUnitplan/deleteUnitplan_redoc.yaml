openapi: 3.0.3
info:
  title: PropVR Delete Unitplan API
  description: |
    ## Delete Unitplan API
    
    **For Property Developers & Real Estate Management**
    
    Safely delete unit plans and move them to trash. This API performs soft deletes by moving 
    unitplans to trash rather than permanently deleting them, with checks for linked units.
  version: 1.0.0

servers:
  - url: https://api.propvr.com/dashboard
    description: Production server
  - url: https://staging-api.propvr.com/dashboard
    description: Staging server

security:
  - BearerAuth: []

paths:
  /deleteUnitplan/{project_id}:
    post:
      operationId: deleteUnitplan
      tags:
        - Unitplans
      summary: Delete a unitplan
      description: |
        **Safely delete a unit plan and move it to trash**
        
        This endpoint performs a soft delete by moving the unitplan to trash rather than permanently deleting it. The operation:
        - Moves the unitplan to trash with a timestamp
        - Checks for linked units and provides warnings
        - Returns information about affected units
        
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
          description: The ID of the project containing the unitplan
          example: "507f1f77bcf86cd799439011"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - unitplan_id
              properties:
                unitplan_id:
                  type: string
                  description: ID of the unitplan to delete
                  example: "507f1f77bcf86cd799439017"
      responses:
        '201':
          description: Unitplan deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: "Unitplan deleted successfully. Please update the following units that were linked to this unitplan"
                  linkedUnits:
                    type: array
                    items:
                      type: string
                    description: Names of units that were linked to this unitplan
                    example: ["Unit A-101", "Unit A-102"]
                  unitCount:
                    type: integer
                    description: Number of linked units
                    example: 2
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        msg:
                          type: string
                        param:
                          type: string
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: "Error moving unitplans to trash"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

tags:
  - name: Unitplans
    description: |
      **For Property Developers & Real Estate Management**
      
      Safely delete unit plans with proper trash management and linked unit tracking.

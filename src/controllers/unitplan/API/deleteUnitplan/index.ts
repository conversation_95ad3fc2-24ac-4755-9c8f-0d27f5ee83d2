import { ExtendedRequest } from '../../../../types/extras';
import { unitplanModule } from '../../../../modules/unitplan';

import { Response } from 'express';
import logger from '../../../../config/logger';
export async function deleteUnitplan (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.headers.organization as string;
  const unitplan = new unitplanModule(project_id, organization_id);
  const unitplan_id = request.body.unitplan_id;
  const timeStamp = Date.now();

  try {
    // Get unit names linked to this unitplan before deleting
    const linkedUnitNames = await unitplan.getUnitNamesByUnitplanId(unitplan_id);

    await unitplan
      .moveToTrash(unitplan_id, project_id, organization_id, timeStamp)
      .then(() => {
        const message = linkedUnitNames.length > 0
          ? 'Unitplan deleted successfully. Please update the following units that were linked to this unitplan'
          : 'Unitplan deleted successfully. No units were linked to this unitplan.';

        response.status(201).json({
          status: 1,
          message: message,
          linkedUnits: linkedUnitNames,
          unitCount: linkedUnitNames.length,
        });
      })
      .catch((error: Error) => {
        logger.error('Error in moveToTrash', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error moving unitplans to trash: '+ error });
      });
  } catch (error) {
    logger.error('Error in deleteUnitplan', {message: error});
    response
      .status(500)
      .json({ status: 0, error: 'Error getting linked units: ' + error });
  }
}

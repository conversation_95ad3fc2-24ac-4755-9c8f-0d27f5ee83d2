openapi: 3.0.3
info:
  title: PropVR Update Unitplan API
  description: |
    ## Update Unitplan API
    
    **For Property Developers & Real Estate Management**
    
    Update existing unit plan details and optionally replace images. This API allows partial 
    updates to existing unit plans including property modifications, image replacements, 
    and association updates.
  version: 1.0.0

servers:
  - url: https://api.propvr.com/dashboard
    description: Production server
  - url: https://staging-api.propvr.com/dashboard
    description: Staging server

security:
  - BearerAuth: []

paths:
  /updateUnitplan:
    post:
      operationId: updateUnitplan
      tags:
        - Unitplans
      summary: Update an existing unitplan
      description: |
        **Update unit plan details and optionally replace images**
        
        This endpoint allows partial updates to existing unit plans. You can:
        - Update any combination of unitplan properties
        - Replace images (with automatic thumbnail generation)
        - Modify measurements, classifications, and associations
        - Update gallery and tour links
        
        **File Upload Notes:**
        - Image uploads are optional for updates
        - When updating to `flat` unit type, `unitplan_image` becomes required
        - New images automatically generate resized thumbnails
        
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - unitplan_id
                - project_id
              properties:
                unitplan_id:
                  type: string
                  description: ID of the unitplan to update
                  example: "507f1f77bcf86cd799439017"
                project_id:
                  type: string
                  description: ID of the project this unitplan belongs to
                  example: "507f1f77bcf86cd799439011"
                building_id:
                  type: string
                  description: ID of the building this unitplan belongs to
                  example: "507f1f77bcf86cd799439012"
                type:
                  type: string
                  description: Type classification of the unitplan
                  example: "residential"
                name:
                  type: string
                  description: Name of the unitplan
                  example: "Updated Luxury 3BHK Apartment"
                measurement:
                  type: number
                  description: Area measurement
                  example: 1300.5
                measurement_type:
                  type: string
                  enum: [sqft, sqmt]
                  description: Unit of measurement
                  example: "sqft"
                tour_id:
                  type: string
                  description: Associated virtual tour ID
                  example: "507f1f77bcf86cd799439013"
                bedrooms:
                  type: string
                  enum: [studio, 1BHK, 2BHK, 3BHK, 4BHK, 5BHK, 6BHK, 7BHK, 8BHK, 9BHK, 10BHK, 0BHK, penthouse, townhouse, podium, suite, plot, office, shop, duplex, 1.5BHK, 2.5BHK, 3.5BHK, 4.5BHK, 5.5BHK, 6.5BHK, 7.5BHK, 8.5BHK, 9.5BHK, 10.5BHK]
                  description: Number of bedrooms
                  example: "3BHK"
                is_residential:
                  type: boolean
                  description: Whether the unit is residential
                  example: true
                bathrooms:
                  type: number
                  description: Number of bathrooms
                  example: 3
                is_furnished:
                  type: boolean
                  description: Whether the unit is furnished
                  example: false
                unit_type:
                  type: string
                  enum: [villa, flat, villa_floor, plot, office, shop]
                  description: Type of unit
                  example: "flat"
                suite_area:
                  type: string
                  description: Suite area measurement
                  example: "1200"
                suite_area_type:
                  type: string
                  enum: [sqft, sqmt]
                  description: Suite area measurement unit
                  example: "sqft"
                scene_id:
                  type: string
                  description: Associated scene ID
                  example: "507f1f77bcf86cd799439015"
                gallery_id:
                  type: string
                  description: Associated gallery ID (JSON string)
                  example: '["507f1f77bcf86cd799439016", "507f1f77bcf86cd799439018"]'
                floor_unitplans:
                  type: string
                  description: Floor unitplans (JSON string)
                  example: '["507f1f77bcf86cd799439019"]'
                exterior_type:
                  type: string
                  enum: [scene, gallery]
                  description: Type of exterior view
                  example: "gallery"
                style:
                  type: string
                  description: Architectural style
                  example: "contemporary"
                balcony_measurement:
                  type: number
                  description: Balcony area measurement
                  example: 60.0
                balcony_measurement_type:
                  type: string
                  enum: [sqft, sqmt]
                  description: Balcony measurement unit
                  example: "sqft"
                is_commercial:
                  type: boolean
                  description: Whether the unit is commercial
                  example: false
                unitplan_image:
                  type: string
                  format: binary
                  description: Updated unitplan image file (required when changing unit_type to flat)
      responses:
        '200':
          description: Unitplan updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  data:
                    $ref: '#/components/schemas/Unitplan'
        '400':
          description: Bad request - validation errors or missing required fields
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        msg:
                          type: string
                        param:
                          type: string
                  error:
                    type: string
                    example: "No fields provided for editing."
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "Error resizing image"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

  schemas:
    Unitplan:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the unitplan
          example: "507f1f77bcf86cd799439017"
        project_id:
          type: string
          description: ID of the associated project
          example: "507f1f77bcf86cd799439011"
        building_id:
          type: string
          description: ID of the associated building
          example: "507f1f77bcf86cd799439012"
        type:
          type: string
          description: Type classification
          example: "residential"
        name:
          type: string
          description: Unitplan name
          example: "Luxury 3BHK Apartment"
        thumbnail:
          type: string
          description: URL to the thumbnail image
          example: "https://storage.googleapis.com/propvr-assets/unitplans/thumbnails/507f1f77bcf86cd799439017.jpg"
        image_url:
          type: string
          description: URL to the full-size image
          example: "https://storage.googleapis.com/propvr-assets/unitplans/507f1f77bcf86cd799439017.jpg"
        measurement:
          type: number
          description: Area measurement
          example: 1250.5
        measurement_type:
          type: string
          enum: [sqft, sqmt]
          description: Unit of measurement
          example: "sqft"
        tour_id:
          type: string
          description: Associated virtual tour ID
          example: "507f1f77bcf86cd799439013"
        bedrooms:
          type: string
          enum: [studio, 1BHK, 2BHK, 3BHK, 4BHK, 5BHK, 6BHK, 7BHK, 8BHK, 9BHK, 10BHK, 0BHK, penthouse, townhouse, podium, suite, plot, office, shop, duplex, 1.5BHK, 2.5BHK, 3.5BHK, 4.5BHK, 5.5BHK, 6.5BHK, 7.5BHK, 8.5BHK, 9.5BHK, 10.5BHK]
          description: Number of bedrooms
          example: "3BHK"
        is_residential:
          type: boolean
          description: Whether the unit is residential
          example: true
        is_commercial:
          type: boolean
          description: Whether the unit is commercial
          example: false
        bathrooms:
          type: number
          description: Number of bathrooms
          example: 2
        is_furnished:
          type: boolean
          description: Whether the unit is furnished
          example: true
        unit_type:
          type: string
          enum: [villa, flat, villa_floor, plot, office, shop]
          description: Type of unit
          example: "flat"
        exterior_type:
          type: string
          enum: [scene, gallery]
          description: Type of exterior view
          example: "scene"
        scene_id:
          type: string
          description: Associated scene ID
          example: "507f1f77bcf86cd799439015"
        gallery_id:
          type: array
          items:
            type: string
          description: Associated gallery IDs
          example: ["507f1f77bcf86cd799439016"]
        floor_unitplans:
          type: array
          items:
            type: string
          description: Floor unitplan IDs (for villa type)
          example: ["507f1f77bcf86cd799439019"]
        style:
          type: string
          description: Architectural style
          example: "modern"
        balcony_measurement:
          type: number
          description: Balcony area measurement
          example: 50.5
        balcony_measurement_type:
          type: string
          enum: [sqft, sqmt]
          description: Balcony measurement unit
          example: "sqft"
        suite_area:
          type: string
          description: Suite area measurement
          example: "1100"
        suite_area_type:
          type: string
          enum: [sqft, sqmt]
          description: Suite area measurement unit
          example: "sqft"
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-15T10:30:00Z"
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T14:45:00Z"

tags:
  - name: Unitplans
    description: |
      **For Property Developers & Real Estate Management**
      
      Update existing unit plan details and manage property modifications.

import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { exterior_type, measurementType, unitplan_type, unitType } from '../../../../types/unitplan';

interface UploadedFiles {
  unitplan_image?: Express.Multer.File[];
}

const UpdateUnitplanValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  const unitTypeVal = req.body.unit_type;

  // Required fields for update
  body('unitplan_id')
    .notEmpty()
    .withMessage('Unitplan ID field cannot be empty');

  body('project_id')
    .notEmpty()
    .withMessage('Project ID field cannot be empty');

  // Optional field validations - only validate if the field is provided
  body('bedrooms', 'Invalid bedroom value. Please ensure that you are using a valid type value')
    .optional({ checkFalsy: true })
    .isIn(Object.values(unitplan_type));

  body('measurement_type', 'Invalid measurement type. Please ensure that you are using a valid measurement type value')
    .optional({ checkFalsy: true })
    .isIn(Object.values(measurementType));

  body('unit_type', 'Invalid type value. Please ensure that you are using a valid type value')
    .optional({ checkFalsy: true })
    .isIn(Object.values(unitType));

  body('exterior_type', 'Invalid exterior type value. Please ensure that you are using a valid type value')
    .optional({ checkFalsy: true })
    .isIn(Object.values(exterior_type));

  body('balcony_measurement_type', 'Invalid Balcony measurement type. Please ensure that you are using a valid measurement type value')
    .optional({ checkFalsy: true })
    .isIn(Object.values(measurementType));

  body('suite_area_type', 'Invalid suite area type. Please ensure that you are using a valid measurement type value')
    .optional({ checkFalsy: true })
    .isIn(Object.values(measurementType));

  // Validate file requirements if unit_type is being updated to 'flat'
  if (unitTypeVal === 'flat' && files && !files.unitplan_image) {
    res.status(400).json({
      error: 'unitplan_image field is required when updating unit_type to flat.',
    });
    return;
  }

  // Validate required fields for villa_floor type if being updated
  if (unitTypeVal === 'villa_floor') {
    if (req.body.measurement || req.body.measurement_type || req.body.bedrooms) {
      res.status(400).json({
        error: 'measurement, measurement_type, and bedrooms fields are not allowed for villa_floor unit type. Use parent_unitplan instead.',
      });
      return;
    }
  }

  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
    return;
  }

  next();
};

export default UpdateUnitplanValidate;


import { Request, Response } from 'express';
import { unitplanModule } from '../../../../modules/unitplan';
import logger from '../../../../config/logger';

export async function GetUnitplans (
  request:Request,
  response:Response,
):Promise<void>{
  const {project_id} = request.params;
  const organization_id = request.headers.organization as string;
  const unitplan = new unitplanModule(project_id, organization_id);

  const unitplanList = await unitplan.getListOfUnitplan();

  if (unitplanList) {
    response.status(200).json({ status: 1, data: unitplanList });
  } else {
    logger.error('unitplans not found');
    response.status(404).json({ status: 0, error: 'unitplans not found' });
  }

}

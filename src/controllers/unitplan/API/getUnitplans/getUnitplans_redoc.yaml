openapi: 3.0.3
info:
  title: PropVR Get Unitplans API
  description: |
    ## Get Unitplans API
    
    **For Property Developers & Real Estate Management**
    
    Retrieve all unit plans associated with a specific project. This API returns comprehensive 
    unit plan information including basic details, image URLs, measurements, and associated tours.
  version: 1.0.0

servers:
  - url: https://api.propvr.com/dashboard
    description: Production server
  - url: https://staging-api.propvr.com/dashboard
    description: Staging server

security:
  - BearerAuth: []

paths:
  /getUnitplans/{project_id}:
    get:
      operationId: getUnitplans
      tags:
        - Unitplans
      summary: Get all unitplans for a project
      description: |
        **Retrieve all unit plans associated with a specific project**
        
        This endpoint returns a comprehensive list of all unit plans within a project, including:
        - Basic unitplan information
        - Image URLs and thumbnails
        - Measurements and specifications
        - Associated tours and galleries
        
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: path
          required: true
          schema:
            type: string
          description: The ID of the project to retrieve unitplans for
          example: "507f1f77bcf86cd799439011"
      responses:
        '200':
          description: Unitplans retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Unitplan'
        '404':
          description: Unitplans not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: "unitplans not found"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

  schemas:
    Unitplan:
      type: object
      properties:
        _id:
          type: string
          description: Unique identifier for the unitplan
          example: "507f1f77bcf86cd799439017"
        project_id:
          type: string
          description: ID of the associated project
          example: "507f1f77bcf86cd799439011"
        building_id:
          type: string
          description: ID of the associated building
          example: "507f1f77bcf86cd799439012"
        type:
          type: string
          description: Type classification
          example: "residential"
        name:
          type: string
          description: Unitplan name
          example: "Luxury 3BHK Apartment"
        thumbnail:
          type: string
          description: URL to the thumbnail image
          example: "https://storage.googleapis.com/propvr-assets/unitplans/thumbnails/507f1f77bcf86cd799439017.jpg"
        image_url:
          type: string
          description: URL to the full-size image
          example: "https://storage.googleapis.com/propvr-assets/unitplans/507f1f77bcf86cd799439017.jpg"
        measurement:
          type: number
          description: Area measurement
          example: 1250.5
        measurement_type:
          type: string
          enum: [sqft, sqmt]
          description: Unit of measurement
          example: "sqft"
        tour_id:
          type: string
          description: Associated virtual tour ID
          example: "507f1f77bcf86cd799439013"
        bedrooms:
          type: string
          enum: [studio, 1BHK, 2BHK, 3BHK, 4BHK, 5BHK, 6BHK, 7BHK, 8BHK, 9BHK, 10BHK, 0BHK, penthouse, townhouse, podium, suite, plot, office, shop, duplex, 1.5BHK, 2.5BHK, 3.5BHK, 4.5BHK, 5.5BHK, 6.5BHK, 7.5BHK, 8.5BHK, 9.5BHK, 10.5BHK]
          description: Number of bedrooms
          example: "3BHK"
        is_residential:
          type: boolean
          description: Whether the unit is residential
          example: true
        is_commercial:
          type: boolean
          description: Whether the unit is commercial
          example: false
        bathrooms:
          type: number
          description: Number of bathrooms
          example: 2
        is_furnished:
          type: boolean
          description: Whether the unit is furnished
          example: true
        unit_type:
          type: string
          enum: [villa, flat, villa_floor, plot, office, shop]
          description: Type of unit
          example: "flat"
        exterior_type:
          type: string
          enum: [scene, gallery]
          description: Type of exterior view
          example: "scene"
        scene_id:
          type: string
          description: Associated scene ID
          example: "507f1f77bcf86cd799439015"
        gallery_id:
          type: array
          items:
            type: string
          description: Associated gallery IDs
          example: ["507f1f77bcf86cd799439016"]
        floor_unitplans:
          type: array
          items:
            type: string
          description: Floor unitplan IDs (for villa type)
          example: ["507f1f77bcf86cd799439019"]
        style:
          type: string
          description: Architectural style
          example: "modern"
        balcony_measurement:
          type: number
          description: Balcony area measurement
          example: 50.5
        balcony_measurement_type:
          type: string
          enum: [sqft, sqmt]
          description: Balcony measurement unit
          example: "sqft"
        suite_area:
          type: string
          description: Suite area measurement
          example: "1100"
        suite_area_type:
          type: string
          enum: [sqft, sqmt]
          description: Suite area measurement unit
          example: "sqft"
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-15T10:30:00Z"
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T14:45:00Z"

tags:
  - name: Unitplans
    description: |
      **For Property Developers & Real Estate Management**
      
      Retrieve unit plan information for property development projects.

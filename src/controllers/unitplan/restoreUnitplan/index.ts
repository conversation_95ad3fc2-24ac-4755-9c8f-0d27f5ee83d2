import { ExtendedRequest } from '../../../types/extras';
import { unitplanModule, invalidateUnitplanAPIs } from '../../../modules/unitplan';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreUnitplan (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const unitplan = new unitplanModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await unitplan
    .restoreUnitplan(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: '<PERSON>p<PERSON> got restored'});

      invalidateUnitplanAPIs(organization_id, project_id).then((res) => {
        logger.info('Unitplan APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating unitplan APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in restoreUnitplan', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Unitplan : '+ error });
    });
}

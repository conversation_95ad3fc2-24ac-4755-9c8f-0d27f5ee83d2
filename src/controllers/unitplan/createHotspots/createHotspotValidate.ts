import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { hotspots, hotspotType, scaleType } from '../../../types/unitplan';

const createHotspotValidator = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('unitplan_id', 'Unitplan ID is required').notEmpty(),
  body('hotspots')
    .exists().withMessage('hotspots is required')
    .isObject().withMessage('hotspots should be an object')
    .custom((value: any) => {
      if (value === null || typeof value !== 'object') {
        return true;
      }

      if (Object.keys(value).length === 0) {
        throw new Error('hotspots should not be a empty object');
      }

      const requiredKeys: (keyof hotspots)[] = ['text', 'x', 'y', 'scale', 'type']; // Required keys
      const optionalKeys: (keyof hotspots)[] = ['image_id', 'label_id', 'group_id', 'subGroup_id']; // Optional key
      const errors: string[] = []; // Error messages

      // Missing required keys check
      const missingKeys = requiredKeys.filter((key) => !(key in value));
      if (missingKeys.length > 0) {
        errors.push(`Missing required keys: ${missingKeys.join(', ')}`);
      }

      // Value type based validation
      const typeValidations: { [key:string]: string[]} = {
        [hotspotType.DEFAULT]: [],
        [hotspotType.IMAGE]: ['image_id'],
        [hotspotType.LABEL]: ['label_id'],
        [hotspotType.GROUP]: ['group_id'],
        [hotspotType.SUBGROUP]: ['group_id', 'subGroup_id'],
      };

      // Type check
      if (typeof value.text !== 'string') {
        errors.push('text must be a string');
      }
      if (typeof value.x !== 'number') {
        errors.push('x must be a number');
      }
      if (typeof value.y !== 'number') {
        errors.push('y must be a number');
      }

      if ('scale' in value && typeof value.scale !== 'string') {
        errors.push('scale must be a string');
      } else if ('scale' in value && typeof value.scale === 'string'){
        const requiredScales = Object.values(scaleType).includes(value.scale);
        if (!requiredScales) {
          errors.push(`Invalid scale type. Must be one of: ${String(Object.values(scaleType))
            .slice(0, String(Object.values(scaleType)).length)}`);
        }
      }

      if ('type' in value && typeof value.type !== 'string'){
        errors.push('type must be a string');
      } else if ('type' in value && typeof value.type === 'string' ){
        const requiredForType = typeValidations[value.type];
        if (requiredForType){
          const missingTypeKeys = requiredForType.filter((key) => !(key in value));

          if (missingTypeKeys.length > 0){
            errors.push(`Missing required keys for type ${value.type}: ${missingTypeKeys.join(', ')}`);
          }

          requiredForType.forEach((key) => {
            if (key in value && typeof value[key] !== 'string'){
              errors.push(`${key} must be string`);
            }
          });

          const allowedForType = [...requiredKeys, ...optionalKeys];
          const unexpectedKeys = Object.keys(value).filter((key)  => !allowedForType.includes(key as keyof hotspots));
          if (unexpectedKeys.length > 0){
            errors.push(`Unexpected keys for type ${value.type}: ${unexpectedKeys.join(', ')}`);
          }
        } else {
          errors.push(`Invalid type. Must be one of: ${String(Object.values(hotspotType))
            .slice(0, String(Object.values(hotspotType)).length)}`);
        }
      }

      if (value.type === hotspotType.DEFAULT && 'image_id' in value && typeof value.image_id !== 'string') {
        errors.push('image_id must be a string if provided');
      }

      if (errors.length > 0){
        throw new Error(`Error: ${errors.join(', ')}`);
      }

      // Additional properties check
      const allowedKeys = [...requiredKeys, ...optionalKeys];
      const extraKeys = Object.keys(value).filter((key) => !allowedKeys.includes(key as keyof hotspots));
      if (extraKeys.length > 0) {
        throw new Error(`Unexpected keys in hotspots: ${extraKeys.join(', ')}`);
      }

      return true;
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createHotspotValidator;

import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const deleteVillaFloorValidate = [
  body('unitplan_id', 'Unitplan ID is required').notEmpty().isString(),
  body('parentID', 'Parent ID is required').notEmpty().isString(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default deleteVillaFloorValidate;

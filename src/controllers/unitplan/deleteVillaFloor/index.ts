import { ExtendedRequest } from '../../../types/extras';
import { unitplanModule, invalidateUnitplanAPIs } from '../../../modules/unitplan';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function deleteVillaFloor (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const unitplan = new unitplanModule(project_id, organization_id);
  const unitplan_id = request.body.unitplan_id;
  const parentID = request.body.parentID;

  try {
    // Delete the villa floor unitplan from database
    await unitplan.deleteVillaFloor(unitplan_id, parentID);

    response.status(200).json({
      status: 1,
      message: 'Villa floor deleted successfully',
    });

    // Invalidate relevant APIs cache
    invalidateUnitplanAPIs(organization_id, project_id, unitplan_id)
      .then((res) => {
        logger.info('Unitplan APIs invalidated successfully', { result: res });
      })
      .catch((err) => {
        logger.error('Error invalidating unitplan APIs', { error: err });
      });
  } catch (error) {
    logger.error('Error in deleteVillaFloor', {message: error});
    response
      .status(500)
      .json({ status: 0, error: 'Error deleting villa floor: ' + error });
  }
}

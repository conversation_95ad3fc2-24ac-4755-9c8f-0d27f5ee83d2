import logger from '../../../config/logger';
import { ShortUrlModule } from '../../../modules/shorterUrl';
import { ExtendedRequest } from '../../../types/extras';
import {  shortUrlInput } from '../../../types/shorterUrl';
import { Response } from 'express';

export async function generateShortUrl (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const IsAuthenticated = request.IsAuthenticated;
    if (!IsAuthenticated) {
      response.status(401).json({ status: 0, error: 'Not authorized' });
      return;
    }

    const shortUrl = new ShortUrlModule();
    const shortUrlData: shortUrlInput = {
      org_url: request.body.org_url,
      image: request.body.image,
      description: request.body.description,
      title: request.body.title,
    };

    const url = await shortUrl.generateShortUrl(shortUrlData);
    response.status(201).json({ status: 1, data: url });
  } catch (error) {
    logger.error('Error while fetching the short URL', {message: error});
    response.status(500).json({ status: 0, error: `Error while creating the short URL:${error} ` });
  }
}

import { Response } from 'express';
import { DamacUnitSyncUpModule } from '../../modules/damacUnitSyncUp';
import logger from '../../config/logger';
import { ExtendedRequest } from '../../types/extras';

export async function damacUnitSyncup (request: ExtendedRequest, response: Response): Promise<void> {
  try {
    const organization = request.headers.organization as string;
    if (!organization) {
      response.status(400).json({
        status: 0,
        message: 'Organization required',
      });
      return;
    }

    const damacUnitSyncUpMod = new DamacUnitSyncUpModule();
    const result = await damacUnitSyncUpMod.syncUnits(organization);

    response.status(200).json({
      status: 1,
      message: 'Units synchronized successfully',
      updatedUnits: result || [],
    });
  } catch (error) {
    logger.error('Error in syncup of units', error);
    response.status(500).json({
      status: 0,
      message: 'Failed to synchronize units',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

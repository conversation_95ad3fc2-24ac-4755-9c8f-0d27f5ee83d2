import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { ProjectSceneModule } from '../../../modules/projectScene';
import { MasterSceneModule } from '../../../modules/masterScene';
import { takeScreenshotAndUpload } from '../../../helpers/screenshotHelper';
import logger from '../../../config/logger';

export async function createPreview (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const organization_id = request.headers.organization as string;
    const { _id, url, type } = request.body;
    const project_id = request.headers.project_id as string;

    try {
      new URL(url);
    } catch {
      response.status(400).json({ status: 0, error: 'Invalid URL format' });
      return;
    }

    logger.info('createPreview Called', {
      _id,
      url,
      type,
      project_id,
      organization_id,
    });

    let storagePath: string;
    if (project_id && organization_id) {
      storagePath = `CreationtoolAssets/${organization_id}/projects/${project_id}/previews/${type}`;
    } else if (organization_id) {
      storagePath = `CreationtoolAssets/${organization_id}/previews/${type}`;
    } else {
      storagePath = `CreationtoolAssets/previews/${type}`;
    }

    storagePath = storagePath.replace(/\/+$/, '').replace(/\/+/g, '/');

    const previewUrl = await takeScreenshotAndUpload(url, storagePath);

    const updateData = { preview: previewUrl };
    let updatedDoc: any = null;

    switch (type) {
      case 'scenes':
        updatedDoc = await new ProjectSceneModule(project_id, organization_id)
          .UpdateScene(_id, updateData as any);
        break;
      case 'master_scenes':
        updatedDoc = await new MasterSceneModule(organization_id)
          .updateScene(_id, updateData as any);
        break;
      default:
        response.status(400).json({
          status: 0,
          error: `Type '${type}' is not supported. Supported types: scenes, master_scenes`,
        });
        return;
    }

    if (!updatedDoc) {
      response.status(404).json({
        status: 0,
        error: `Document with id '${_id}' not found in ${type} collection`,
      });
      return;
    }

    logger.info('createPreview Successful', { _id, preview: previewUrl, type });
    response.status(201).json({
      status: 1,
      data: {
        _id: updatedDoc._id,
        preview: previewUrl,
      },
    });
  } catch (error) {
    logger.error('Error in createPreview', { error });
    response.status(500).json({
      status: 0,
      error: error instanceof Error ? error.message : 'Failed to create preview',
    });
  }
}

import { Request, Response } from 'express';
import { AnalyticsModule } from '../../../modules/analytics';

export async function generateReport (req: Request, res: Response): Promise<void> {
  const { startDate, endDate, orgId, projectId } = req.body;
  const analyticsModule = new AnalyticsModule();
  try {
    const report = await analyticsModule.runReport({ startDate, endDate, orgId, projectId });
    res.status(200).json({ status: 1, data: report });
  } catch (error) {
    console.error('Error running report:', error);
    res.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

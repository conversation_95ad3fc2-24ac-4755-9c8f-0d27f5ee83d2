import { Response } from 'express';
import { StatusModule } from '../../../modules/status';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';

export async function getAllSubstatus (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;

  if (!project_id) {
    response.status(400).json({
      status: 0,
      message: 'project_id is required in params',
    });
    return;
  }

  try {
    const statusModule = new StatusModule(project_id);
    const uniqueSubstatuses = await statusModule.getAllSubstatus();

    response.status(200).json({ status: 1, data: uniqueSubstatuses });
  } catch (error) {
    logger.error('Error in getAllSubstatus', { error, project_id });
    response.status(500).json({
      status: 0,
      message: error instanceof Error ? error.message : 'Error in gettingallsubstatuses',
    });
  }
}

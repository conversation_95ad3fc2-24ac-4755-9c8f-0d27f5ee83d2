import { Request, Response, NextFunction } from 'express';
import { body, param, validationResult } from 'express-validator';

export const createSubstatusValidator = [
  param('project_id').notEmpty().withMessage('project_id is required').isString().withMessage('project_id must be a string'),
  body('category').notEmpty().withMessage('category is required').isString().withMessage('category must be a string'),
  body('substatus').notEmpty().withMessage('substatus is required').isString().withMessage('substatus must be a string'),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ status: 0, errors: errors.array() });
      return;
    }
    next();
  },
];

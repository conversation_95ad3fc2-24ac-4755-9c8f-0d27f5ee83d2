import { Response } from 'express';
import { StatusModule } from '../../../modules/status';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';

export async function createSubstatus (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { category, substatus } = request.body;
  const { project_id } = request.params;

  if (!category || !substatus) {
    response.status(400).json({
      status: 0,
      message: 'category, and substatus are required',
    });
    return;
  }

  try {
    const statusModule = new StatusModule(project_id);
    const createdStatus = await statusModule.createSubstatus(category, substatus);

    if (createdStatus) {
      response.status(200).json({ status: 1, data: createdStatus });
    }
  } catch (error) {
    logger.error('Error in createSubstatus', { error, project_id, category, substatus });
    response.status(500).json({
      status: 0,
      message: error instanceof Error ? error.message : 'Error creating substatus',
    });
  }
}

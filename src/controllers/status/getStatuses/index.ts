import { Response } from 'express';
import { StatusModule } from '../../../modules/status';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';

export async function getStatus (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;

  if (!project_id) {
    response.status(400).json({
      status: 0,
      message: 'project_id is required in headers',
    });
    return;
  }

  try {
    const statusModule = new StatusModule(project_id);
    const status = await statusModule.getStatus();

    if (status){
      response.status(200).json({ status: 1, data: status });
    }

  } catch (error) {
    logger.error('Error in getStatus', { error, project_id });
    response.status(500).json({
      status: 0,
      message: error instanceof Error ? error.message : 'Error in getstatus',
    });
  }
}

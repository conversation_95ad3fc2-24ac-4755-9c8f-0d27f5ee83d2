import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import { InvitesModule } from '../../../modules/invites';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { invitation_id } = request.params;
  const organization_id = request.organization_id as string;
  const invitation = new InvitesModule();
  const timeStamp = request.body.timeStamp;

  await invitation
    .moveToTrash(invitation_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving invitation to trash: '+ error });
    });
}

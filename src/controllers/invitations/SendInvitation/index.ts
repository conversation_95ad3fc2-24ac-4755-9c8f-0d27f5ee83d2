import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { Invitation } from '../../../types/invitations';
import { InvitesModule } from '../../../modules/invites';
import { OrganizationModule } from '../../../modules/organization';
import logger from '../../../config/logger';
export async function SendInvitation (
  request: ExtendedRequest,
  response: Response,
): Promise<Invitation | void> {
  logger.info('SendInvitation Called', {invite: request.body});
  const organization = new OrganizationModule();
  const invitation = new InvitesModule();
  const organization_id = request.organization_id as string;
  const IsAuthenticated = request.IsAuthenticated;

  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }

  const userInvite = await invitation.findUserInviteById(request.body.invitation_id);
  if (!userInvite) {
    response.send({ status: 0, error: 'Invitation does not exist' });
    return;
  }

  const organizationDetails = await organization.GetOrganization(userInvite.organization_id);
  if (!organizationDetails) {
    response.send({ status: 0, error: 'Invalid organization' });
    return;
  }

  try {
    const InviteObj = new InvitesModule();
    const refererHeader = request.headers.referer;
    const defaultUrl = 'https://dashboard.propvr.tech';
    const refererUrl = Array.isArray(refererHeader) ?
      refererHeader[0] : refererHeader || defaultUrl;
    await InviteObj.SendInvite({
      email: userInvite.email,
      organization_id: organization_id,
      organization_name: organizationDetails.name,
      role: userInvite.role,
      referer_url: refererUrl,
      user_name: '',
    });
    response.send({ status: 1, error: 'Invite send to user successfully' });
  } catch (error) {
    logger.error('Error while sending invitaion mail', {message: error});
    response
      .status(500)
      .json({ status: 0, error: 'Error while sending invitaion mail'+ error });
  }
}

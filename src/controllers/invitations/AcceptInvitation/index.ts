import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { Invitation } from '../../../types/invitations';
import { InvitesModule } from '../../../modules/invites';
import logger from '../../../config/logger';
export async function AcceptInvitation (
  request: ExtendedRequest,
  response: Response,
): Promise<Invitation | void> {
  const organization_id = request.organization_id as string;
  const IsAuthenticated = request.IsAuthenticated;
  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  const invitation = new InvitesModule();
  const userInvite = await invitation.findUserInviteById(request.body.invite_id);
  if (!userInvite) {
    response.send({ status: 0, error: 'Invitation Does not exist' });
    return;
  }
  const user_data = {
    user_id: IsAuthenticated.uid,
    email: userInvite.email,
    first_name: IsAuthenticated.displayName || null,
    last_name: IsAuthenticated.displayName || null,
    organization_id: [organization_id],
  };
  await invitation
    .AcceptInvite(user_data, userInvite)
    .then((inviteData) => {
      response.status(201).json({ status: 1, data: inviteData });
    })
    .catch((error: Error) => {
      logger.error('Error while creating the project', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the project'+ error });
    });
}

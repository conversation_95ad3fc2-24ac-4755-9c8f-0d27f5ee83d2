import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';

const acceptInviteValidate = [
  body('invite_id', 'Invite ID  is required').notEmpty(),
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default acceptInviteValidate;

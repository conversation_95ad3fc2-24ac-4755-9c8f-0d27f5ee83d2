import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const GenerateTileValidate = [
  body('storagePath', 'storagePath is required').notEmpty(),
  body('project_id', 'project_id is required').notEmpty(),
  body('tour_id', 'tour_id is required').notEmpty(),
  body('imageurl', 'Image Url is required').notEmpty(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default GenerateTileValidate;

import { Request, Response, NextFunction } from 'express';
import { validationResult, body, param } from 'express-validator';
import { Direction } from '../../../types/direction';
import { measurementType, priceCurrency } from '../../../types/units';
import { StatusModule } from '../../../modules/status';

const createUnitValidate = [
  body('project_id', 'Project Id is required').notEmpty(),
  param('unit_id', 'Unit Id is required').notEmpty(),
  body('name').optional(),
  body('unitplan_id').optional(),
  body('floor_id').optional(),
  body('building_id').optional(),
  body('currency').optional().isIn(Object.values(priceCurrency)),
  body('measurement').optional({ checkFalsy: true }).isNumeric(),
  body('balcony_measurement').optional({ checkFalsy: true }).isNumeric(),
  body('balcony_measurement_type').optional().isIn(Object.values(measurementType)),
  body('suite_area').optional({ checkFalsy: true }).isNumeric(),
  body('suite_area_type').optional().isIn(Object.values(measurementType)),
  body('status', 'Invalid Status. Please ensure that you are using a valid status value')
    .optional()
    .notEmpty()
    .isString().withMessage('status must be a string')
    .custom(async (value, { req }) => {
      if (!value) {
        return true;
      }

      const project_id = req.body?.project_id;
      if (!project_id) {
        throw new Error('project_id is required in request body to validate status');
      }

      try {
        const statusModule = new StatusModule(project_id);
        const validSubstatuses = await statusModule.getAllSubstatus();

        // Check if the provided status exists in the valid substatuses
        if (!validSubstatuses.includes(value)) {
          throw new Error(`Invalid status: ${value}. Valid options are: ${validSubstatuses.join(', ')}`);
        }

        return true;
      } catch (error) {
        if (error instanceof Error) {
          throw error;
        }
        throw new Error('Error validating status');
      }
    }),
  body('cta_link').optional(),
  body('direction').optional().custom((value) => {
    if (value === '' || value === null || value === undefined) {
      return true; // Allow empty string, null, or undefined
    }
    return Object.values(Direction).includes(value);
  }),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createUnitValidate;

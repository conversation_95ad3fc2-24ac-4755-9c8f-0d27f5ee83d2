import { Request, Response } from 'express';
import { Units } from '../../../types/units';
import { UnitModule, invalidateUnitAPIs } from '../../../modules/units';
import logger from '../../../config/logger';

export async function UpdateUnit (request: Request, response: Response): Promise<Units | void> {
  const {project_id} = request.body;
  const {unit_id}  = request.params;
  const organization_id = request.headers.organization as string;
  const unit = new UnitModule(project_id);

  unit.updateUnit(request.body, unit_id, organization_id).then(async (res) => {
    response.send({status: 1, data: res});

    invalidateUnitAPIs(organization_id, project_id).then((invalidationResult) => {
      logger.info('Unit APIs invalidated successfully', { result: invalidationResult });
    }).catch((invalidationResult) => {
      logger.error('Error invalidating unit APIs', { error: invalidationResult });
    });
  })
    .catch((err) => {
      logger.error('Error in updateUnit', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

import { Response } from 'express';
import { Units } from '../../../types/units';
import { UnitModule, invalidateUnitAPIs } from '../../../modules/units';
import { Types } from 'mongoose';
import logger from '../../../config/logger';
import multer from 'multer';
import { ExtendedRequest } from '../../../types/extras';

async function CreateUnit (unitData: Units, organization_id: string) {
  const { unitplan_id, project_id, name, status, metadata,
    floor_id, building_id, community_id, price, currency, tour_id, measurement, measurement_type,
    balcony_measurement, balcony_measurement_type, cta_link, direction } = unitData;
  const unit = new UnitModule(project_id);

  // Build payload object, excluding direction if it's "" or null
  const unitPayload: any = {
    _id: new Types.ObjectId(),
    unitplan_id,
    project_id,
    name,
    status,
    metadata,
    floor_id,
    building_id,
    community_id,
    currency,
    price,
    tour_id,
    measurement,
    measurement_type,
    balcony_measurement,
    balcony_measurement_type,
    cta_link,
  };

  // Only add direction if it's not null, undefined, or empty string
  if (direction !== null && String(direction) !== '') {
    unitPayload.direction = direction;
  }

  return unit.createUnit(unitPayload, organization_id);
}

async function UpdateUnit (payload: { [key: string]: string }, unit_id: string, organization_id: string): Promise<Units | null> {
  const project_id = payload.project_id as string;
  const unit = new UnitModule(project_id);

  return unit.updateUnit(payload, unit_id, organization_id);
}

const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB limit
  },
  fileFilter: (_req, file, cb) => {
    if (file.mimetype === 'application/json') {
      cb(null, true);
    } else {
      cb(new Error('.json is only allowed'));
    }
  },
}).single('file');

const processJSONToObjects = (buffer : object) => {
  return new Promise((resolve, reject) => {
    try {
      const jsonText = buffer.toString();
      let jsonData;

      // Parse JSON
      try {
        jsonData = JSON.parse(jsonText);
      } catch (error) {
        throw new Error(`Invalid JSON format: ${error}`);
      }

      if (!Array.isArray(jsonData)) {
        jsonData = [jsonData];
      }

      if (jsonData.length === 0) {
        throw new Error('JSON file is empty or contains no data');
      }

      const results: { [x: string]: unknown; _rowIndex?: number; _originalData?: unknown; }[] = [];

      // Process each object
      const dataObject = jsonData[0];
      Object.values(dataObject).forEach((item, index) => {
        try {
          const processedObject : { [key: string]: unknown } = {};
          const metadata: { [key: string]: unknown } = {};

          // Add type guard to ensure item is an object
          if (typeof item !== 'object' || item === null) {
            console.error(`Item ${index + 1} is not an object:`, item);
            results.push({
              _rowIndex: index + 1,
              _originalData: item,
            });
            return;
          }

          Object.keys(item as Record<string, unknown>).forEach((key) => {
            let value = (item as Record<string, unknown>)[key];

            // Handle null/undefined values
            if (value === null || value === undefined || value === '') {
              value = null;
            } else if (typeof value === 'object') {
              return;
            }

            // Handle currency field
            if (key === 'currency') {
              if (value !== null && value !== undefined && value !== '') {
                processedObject[key] = (value as string).toLowerCase();
              } else {
                // Send as empty string
                processedObject[key] = '';
              }
              return;
            }

            // Handle metadata fields
            if (key.startsWith('metadata_')) {
              const metadataKey = key.replace('metadata_', '');
              metadata[metadataKey] = value;
            } else {
              processedObject[key] = value;
            }
          });

          // Add metadata object
          if (Object.keys(metadata).length > 0) {
            processedObject.metadata = metadata;
          }
          processedObject._rowIndex = index + 1;
          results.push(processedObject);

        } catch (error) {
          console.error(`Error processing object ${index + 1}:`, error);
          results.push({
            _rowIndex: index + 1,
            _originalData: item,
          });
        }
      });

      resolve({
        data: results,
      });

    } catch (error) {
      reject(error);
    }
  });
};

export const BulkCreateUnits = async ( req: ExtendedRequest, res : Response) :Promise<void> => {
  try {
    new Promise(() => {
      upload(req, res, async (err) => {
        if (err) {
          logger.error('Error uploading file', { error: err });
          res.status(400).json({
            message: err.message,
            error: err,
          });
        }

        if (!req.file) {
          res.status(400).json({
            message: 'No file uploaded',
          });
        }

        if (req.file){
          processJSONToObjects(req.file.buffer)
            .then(async (result: unknown) => {
              const { data } = result as { data: Units[] };

              if (!data || data.length === 0) {
                res.status(400).json({ message: 'No valid data found in the CSV file.' });
              }

              const organization_id = req.headers.organization as string;

              const promises = data.map((unit: Units) => {
                if (unit?.isNew) {
                  return CreateUnit(unit, organization_id);
                }

                const { project_id } = unit;
                if (!project_id) {
                  throw new Error('project_id is required for updating unit');
                }

                // Build payload
                const payload: { [key: string]: string } = {};
                for (const [key, value] of Object.entries(unit)) {
                  if (key === '_id' || value === undefined) {
                    continue;
                  }
                  if (value === null) {
                    // Keep null
                    payload[key] = value;
                    continue;
                  }
                  if (key === 'metadata' && typeof value === 'object') {
                    // Keep metadata as object, don't convert
                    payload[key] = value;
                  } else {
                    // Convert everything else to string
                    payload[key] = (typeof value === 'object') ? JSON.stringify(value) : String(value);
                  }
                }
                return UpdateUnit(payload, unit._id.toString(), organization_id);
              });

              const results = await Promise.all(promises);
              res.status(201).json({ status: 1, data: results });

              const project_id = req.body.project_id;
              invalidateUnitAPIs(organization_id, project_id).then((invalidationResult) => {
                logger.info('Unit APIs invalidated successfully', { result: invalidationResult });
              }).catch((invalidationError) => {
                logger.error('Error invalidating unit APIs', { error: invalidationError });
              });

            }).catch((error) => {
              console.error('Error processing CSV:', error);
              res.status(500).json({ status: 0, error: 'Error processing CSV file' });
            });
        }
      });
    });

  } catch (error){
    logger.error('Error in BulkCreateUnits', {message: error});
    res.status(500).json({ status: 0, error: 'Error while creating the units' });
  }

};

import { Request, Response, NextFunction } from 'express';
import { validationResult, header } from 'express-validator';

const bulkCreateUnitsValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default bulkCreateUnitsValidate;

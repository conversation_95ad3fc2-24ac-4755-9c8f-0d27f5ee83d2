import { ExtendedRequest } from '../../../types/extras';
import { UnitModule, invalidateUnitAPIs } from '../../../modules/units';
import { Response } from 'express';
import logger from '../../../config/logger';

export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const units = new UnitModule(project_id);
  const unit_id = request.body.unit_id;
  const timeStamp = request.body.timeStamp;

  await units
    .moveToTrash(unit_id, project_id, organization_id, timeStamp)
    .then(async () => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });

      invalidateUnitAPIs(organization_id, project_id).then((res) => {
        logger.info('Unit APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating unit APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving units to trash: '+ error });
    });
}

import { Request, Response } from 'express';
import { unitListResponse } from '../../../types/units';
import { UnitModule } from '../../../modules/units';
import logger from '../../../config/logger';

export async function getAllUnitsWithPagination (
  request: Request,
  response: Response,
): Promise<unitListResponse | void> {

  const {project_id, limit, pageSize, searchText } = request.body;

  // Convert string values to numbers for pagination
  const limitNum = parseInt(limit as string) || 10;
  const pageSizeNum = parseInt(pageSize as string) || 0;
  const searchTextStr = searchText as string || '';

  const unit = new UnitModule(project_id);
  await unit
    .getAllUnitsWithPagination(project_id, limitNum, pageSizeNum, searchTextStr)
    .then((unitData) => {
      response.status(200).json({ status: 1, data: unitData });
    })
    .catch((error: Error) => {
      logger.error('Error in getAllUnitsWithPagination', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while fetching the units' });
      console.error(error);
    });
}

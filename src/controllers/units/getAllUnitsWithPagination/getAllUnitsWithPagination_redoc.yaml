openapi: 3.0.3
info:
  title: PropVR Units - Get All Units With Pagination API
  description: |
    ## **Get All Units With Pagination API**
    
    **For Property Developers & Real Estate Management**
    
    Get All Units With Pagination operations for units management and configuration.
  version: 1.0.0

servers:
  - url: https://dashboard-api-prod-172924419383.us-central1.run.app/units
    description: Production server
  - url: https://platform-backend-274706608007.us-central1.run.app/units
    description: Staging server

security:
  - BearerAuth: []

paths:
  /getAllUnitsWithPagination:
    get:
      operationId: getAllUnitsWithPagination
      tags:
        - Units
      summary: Get All Units With Pagination
      description: |
        **Get All Units With Pagination operation**
        
        This endpoint allows you to:
        - Perform get all units with pagination operations
        - Manage units data and configuration
        - Handle units related functionality
        - Maintain data integrity and audit trails
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: query
          required: false
          schema:
            type: string
          description: Unique identifier for filtering
          example: "507f1f77bcf86cd799439011"
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Maximum number of items to return
          example: 20
        - name: offset
          in: query
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
          description: Number of items to skip for pagination
          example: 0
        - name: search
          in: query
          required: false
          schema:
            type: string
          description: Search term for filtering results
          example: "sample search"
      responses:
        '200':
          description: Operation completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: Get All Units With Pagination completed successfully
                  data:
                    $ref: '#/components/schemas/UnitsResponse'
              examples:
                success:
                  summary: Successful get all units with pagination response
                  value:
                    status: 1
                    message: "Get All Units With Pagination completed successfully"
                    data: [
                      {
                        "id": "507f1f77bcf86cd799439011",
                        "name": "Sample Units 1",
                        "description": "First sample units",
                        "project_id": "507f1f77bcf86cd799439012",
                        "status": "active",
                        "created_at": "2024-01-15T10:30:00Z",
                        "updated_at": "2024-01-15T14:45:00Z"
                      },
                      {
                        "id": "507f1f77bcf86cd799439013",
                        "name": "Sample Units 2", 
                        "description": "Second sample units",
                        "project_id": "507f1f77bcf86cd799439012",
                        "status": "inactive",
                        "created_at": "2024-01-14T09:20:00Z",
                        "updated_at": "2024-01-14T16:30:00Z"
                      }
                    ]
        '400':
          description: Bad request - Invalid input data
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Invalid input data
              examples:
                validation_error:
                  summary: Validation error example
                  value:
                    status: 0
                    error: "Required field 'name' is missing"
                invalid_format:
                  summary: Invalid format example
                  value:
                    status: 0
                    error: "Invalid data format provided"
        '401':
          description: Unauthorized - Invalid or missing authentication
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Unauthorized access
              examples:
                missing_token:
                  summary: Missing authentication token
                  value:
                    status: 0
                    error: "Authorization token is required"
                invalid_token:
                  summary: Invalid authentication token
                  value:
                    status: 0
                    error: "Invalid or expired token"
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Insufficient permissions
              examples:
                permission_denied:
                  summary: Permission denied example
                  value:
                    status: 0
                    error: "You don't have permission to perform this action"
        '404':
          description: Not found - Resource does not exist
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Resource not found
              examples:
                not_found:
                  summary: Resource not found example
                  value:
                    status: 0
                    error: "Units not found with the provided ID"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: Internal server error
              examples:
                server_error:
                  summary: Server error example
                  value:
                    status: 0
                    error: "An unexpected error occurred while processing get all units with pagination"
                database_error:
                  summary: Database error example
                  value:
                    status: 0
                    error: "Database connection failed"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

  schemas:
    UnitsResponse:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier
          example: "507f1f77bcf86cd799439011"
        name:
          type: string
          description: Name or title
          example: "Sample Units"
        description:
          type: string
          description: Description
          example: "Sample description for units"
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-15T10:30:00Z"
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T14:45:00Z"
        status:
          type: string
          description: Current status
          example: "active"
          enum: ["active", "inactive", "pending", "archived"]

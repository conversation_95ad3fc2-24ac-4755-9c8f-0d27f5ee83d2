import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const getAllUnitsWithPaginationValidate = [
  body('project_id', 'Project ID is required').notEmpty(),
  body('unitplan_id', 'Unit Plan ID is optional').optional(),
  body('limit', 'Limit is required').notEmpty().isNumeric(),
  body('pageSize', 'Page Size is required').notEmpty().isNumeric(),
  body('searchText').optional({ checkFalsy: true }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default getAllUnitsWithPaginationValidate;

import { Response  } from 'express';
import { UnitModule, invalidateUnitAPIs } from '../../../../modules/units';
import { ExtendedRequest } from '../../../../types/extras';
import { priceCurrency } from '../../../../types/units';
import logger from '../../../../config/logger';
import { generateSessionId } from '../../../../helpers/session';

interface UnitsRequestBody{
  project_id:string,
  units: Array<string>,
  status: string,
  price?: string,
  currency?:priceCurrency
}
export async function createFilterUnits (
  request : ExtendedRequest,
  response : Response,
) :Promise<void>{
  try {
    const { project_id, units, status, price, currency } = request.body as UnitsRequestBody;

    const successful: string[] = [];
    const failed: Array<{ unitName: string; error: string }> = [];

    const sessionId:string = await generateSessionId();

    const unitModule = new UnitModule(project_id);

    const unitLibraryModule = new UnitModule(project_id, sessionId);

    const processUnits = async (unitName: string): Promise<string | null> => {
      try {
        const resultUnit = await unitModule.getUnitsByName(unitName);
        if (!resultUnit){
          logger.error(`Error: No Units Found for ${unitName}`);
          throw new Error(`No Units Found for ${unitName}`);
        }

        resultUnit.status = status;
        if (price !== undefined) {
          resultUnit.price = price;
        }
        if (currency !== undefined) {
          resultUnit.currency = currency;
        }
        if (!resultUnit.created_at){
          resultUnit.created_at = new Date().toISOString();
        }

        const unitsResult = await unitLibraryModule.createUnit(resultUnit);
        if (!unitsResult) {
          throw new Error(`Failed to create unit entry for ${unitName}`);
        }
        return resultUnit ? `${unitName} has been created successfully` : null;
      } catch (error) {
        logger.error(`Error processing unit ${unitName}:`, error);
        throw error;
      }
    };

    const results = await Promise.allSettled(
      units.map((unitName) => processUnits(unitName)),
    );

    results.forEach((result, index) => {
      const unitName = units[index];
      if (result.status === 'fulfilled' && result.value) {
        successful.push(result.value);
      } else {
        const errorMessage = result.status === 'rejected'
          ? result.reason?.message || 'Unknown error'
          : 'Processing failed';

        failed.push({
          unitName: unitName,
          error: errorMessage,
        });
      }
    });

    if (successful.length > 0){
      response.status(200).json({
        status: 1,
        message: `${successful.length} out of ${units.length} units have been created successfully`,
        data: sessionId,
      });

      const organization_id = request.organization_id as string;
      invalidateUnitAPIs(organization_id, project_id).then((res) => {
        logger.info('Unit APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating unit APIs', { error: err });
      });
    } else {
      response.status(500).json({
        status: 0,
        message: 'All Units Creation operations failed',
        data: failed,
      });
    }

  } catch (error) {
    logger.error('An error occurred during Units creation:', error);
    response.status(500).json({
      status: 0,
      error: `Error during Units Creation: ${error instanceof Error ? error.message : error}`,
    });
  }
}

openapi: 3.0.3
info:
  title: PropVR Units - Create Filter Units API
  description: Create filter units for property management and unit processing
  version: 1.0.0

servers:
  - url: https://api.propvr.com/api
  - url: https://staging-api.propvr.com/api

security:
  - BearerAuth: []

paths:
  /unit/filters:
    post:
      operationId: createFilterUnits
      tags:
        - Units
      summary: Create Filter Units
      description: |
        Create and process filter units for a specific project. This endpoint allows you to:
        - Process multiple units by name
        - Update unit status (available, onhold, reserved, sold)
        - Set pricing and currency information
        - Bulk process units with filtering capabilities
      security:
        - BearerAuth: []
      parameters:
        - name: authorization
          in: header
          required: true
          schema:
            type: string
          description: API key for authentication
          example: "Bearer your_jwt_token_here"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - project_id
                - units
                - status
              properties:
                project_id:
                  type: string
                  description: Unique identifier of the project
                  example: "507f1f77bcf86cd799439011"
                units:
                  type: array
                  description: Array of unit names to process
                  items:
                    type: string
                  example: ["Unit-A101", "Unit-A102", "Unit-B201"]
                status:
                  type: string
                  description: Status to set for the units
                  enum: ["available", "onhold", "reserved", "sold"]
                  example: "available"
                price:
                  type: string
                  description: Price for the units (optional, numeric string)
                  pattern: "^[0-9]+(\\.[0-9]+)?$"
                  example: "750000"
                currency:
                  type: string
                  description: Currency code for the price (optional)
                  enum: [
                    "aed", "ars", "aud", "bgn", "brl", "bsd", "cad", "chf", "clp", "cny",
                    "cop", "czk", "dkk", "dop", "egp", "eur", "fjd", "gbp", "gtq", "hkd",
                    "hrk", "huf", "idr", "ils", "inr", "isk", "jpy", "krw", "kzt", "mxn",
                    "myr", "nok", "nzd", "pab", "pen", "php", "pkr", "pln", "pyg", "ron",
                    "rub", "sek", "sgd", "thb", "try", "twd", "uah", "usd", "uyu", "vnd", "zar"
                  ]
                  example: "usd"
      responses:
        '200':
          description: Units processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                  data:
                    type: object
                    properties:
                      successful:
                        type: array
                        description: Array of successfully processed unit names
                        items:
                          type: string
                        example: ["Unit-A101", "Unit-A102"]
                      failed:
                        type: array
                        description: Array of failed units with error details
                        items:
                          type: object
                          properties:
                            unitName:
                              type: string
                              example: "Unit-B201"
                            error:
                              type: string
                              example: "No Units Found for Unit-B201"
                        example: []
                      sessionId:
                        type: string
                        description: Session ID for the processing operation
                        example: "session_507f1f77bcf86cd799439011"
                  message:
                    type: string
                    example: "Units processed successfully"
                  error:
                    type: string
        '400':
          description: Bad request - Validation errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                          example: "field"
                        value:
                          type: string
                          example: ""
                        msg:
                          type: string
                          example: "project_id is required"
                        path:
                          type: string
                          example: "project_id"
                        location:
                          type: string
                          example: "body"
                examples:
                  validation_error:
                    summary: Validation Error Example
                    value:
                      errors:
                        - type: "field"
                          value: ""
                          msg: "project_id is required"
                          path: "project_id"
                          location: "body"
                        - type: "field"
                          value: "invalid_status"
                          msg: "Invalid status. Valid options are: available, onhold, reserved, sold"
                          path: "status"
                          location: "body"
                  authorization_missing:
                    summary: Missing Authorization Header
                    value:
                      errors:
                        - type: "field"
                          value: ""
                          msg: "apikey is required"
                          path: "authorization"
                          location: "headers"
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "Authentication required"
                  error:
                    type: string
                    example: "Invalid or missing JWT token"
        '403':
          description: Forbidden - Insufficient project permissions
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "Access denied"
                  error:
                    type: string
                    example: "Insufficient permissions for this project"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "Internal server error"
                  error:
                    type: string
                    example: "An unexpected error occurred while processing units"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

  schemas:
    UnitStatus:
      type: string
      enum: ["available", "onhold", "reserved", "sold"]
      description: Status of the unit
      
    Currency:
      type: string
      enum: [
        "aed", "ars", "aud", "bgn", "brl", "bsd", "cad", "chf", "clp", "cny",
        "cop", "czk", "dkk", "dop", "egp", "eur", "fjd", "gbp", "gtq", "hkd",
        "hrk", "huf", "idr", "ils", "inr", "isk", "jpy", "krw", "kzt", "mxn",
        "myr", "nok", "nzd", "pab", "pen", "php", "pkr", "pln", "pyg", "ron",
        "rub", "sek", "sgd", "thb", "try", "twd", "uah", "usd", "uyu", "vnd", "zar"
      ]
      description: Supported currency codes

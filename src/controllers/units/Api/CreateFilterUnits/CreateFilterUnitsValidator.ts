import { Request, Response, NextFunction } from 'express';
import { body, check, validationResult } from 'express-validator';
import { priceCurrency } from '../../../../types/units';
import { StatusModule } from '../../../../modules/status';

const CreateFilterUnitsValidator = [
  check('authorization', 'apikey is required').notEmpty(),
  body('project_id', 'project_id is required').notEmpty(),
  body('units').isArray().withMessage('Units must be an String array'),
  body('price')
    .optional()
    .isString().withMessage('price must be a string')
    .isNumeric().withMessage('price must be a string containing only numbers'),

  body('currency').optional()
    .custom((value) => {
      // Check if valid currency
      const validCurrencies = Object.values(priceCurrency);
      if (!validCurrencies.includes(value)) {
        throw new Error(`Invalid currency: ${value}. Valid options: ${validCurrencies.join(', ')}`);
      }

      if (typeof value !== 'string') {
        throw new Error('currency must be a string');
      }

      return true;
    }),

  body('status', 'status is required')
    .notEmpty()
    .isString().withMessage('status must be a string')
    .custom(async (value, { req }) => {

      const project_id = req.body?.project_id;
      if (!project_id) {
        throw new Error('project_id is required in request body to validate status');
      }

      try {
        const statusModule = new StatusModule(project_id);
        const validSubstatuses = await statusModule.getAllSubstatus();

        // Check if the provided status exists in the valid substatuses
        if (!validSubstatuses.includes(value)) {
          throw new Error(`Invalid status: ${value}. Valid options are: ${validSubstatuses.join(', ')}`);
        }

        return true;
      } catch (error) {
        // If it's already a validation error, re-throw it
        if (error instanceof Error) {
          throw error;
        }
        throw new Error('Error validating status');
      }
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default CreateFilterUnitsValidator;

import { Response  } from 'express';
import { UnitModule } from '../../../../modules/units';
import { ExtendedRequest } from '../../../../types/extras';
import logger from '../../../../config/logger';

interface filterDrop{
  project_id:string,
  session_id:string
}

export async function filterDropCollection (
  request : ExtendedRequest,
  response : Response,
) :Promise<void>{
  try {
    const { project_id, session_id } = request.body as filterDrop;

    const unitModule = new UnitModule(project_id, session_id);

    const dropResult:boolean = await unitModule.dropUnitsCollection();

    if (dropResult){
      response.status(200).send({status: 1, message: 'Collection Dropped Successfully !'});
    } else {
      response.status(400).send({status: 0, message: 'Collection does not Exists'});
    }

  } catch (error) {
    logger.error('An error occurred during Unit Collection Drop:', error);
    response.status(500).json({
      status: 0,
      error: `Error during Unit Collection Drop: ${error instanceof Error ? error.message : error}`,
    });
  }
}

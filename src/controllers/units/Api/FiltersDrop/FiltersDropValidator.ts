import { Request, Response, NextFunction } from 'express';
import { body, check, validationResult } from 'express-validator';

const FiltersDropValidator = [
  check('authorization', 'apikey is required').notEmpty(),
  body('project_id', 'project_id is required').notEmpty(),
  body('session_id').isString().withMessage('Session Id must be a String'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default  FiltersDropValidator;

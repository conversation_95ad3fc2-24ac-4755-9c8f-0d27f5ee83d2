openapi: 3.0.3
info:
  title: PropVR Units - Filters Drop API
  description: Drop unit collection filters for a specific project and session
  version: 1.0.0

servers:
  - url: https://api.propvr.com/api
  - url: https://staging-api.propvr.com/api

security:
  - BearerAuth: []

paths:
  /unit/filtersDrop:
    post:
      operationId: filterDropCollection
      tags:
        - Units
      summary: Drop Unit Collection Filters
      description: |
        Drop (delete) unit collection filters for a specific project and session. This endpoint allows you to:
        - Remove all filtered units from a specific session
        - Clean up temporary unit collections
        - Reset filter states for a project session
        - Free up resources by dropping unused collections
      security:
        - BearerAuth: []
      parameters:
        - name: authorization
          in: header
          required: true
          schema:
            type: string
          description: API key for authentication
          example: "Bearer your_jwt_token_here"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - project_id
                - session_id
              properties:
                project_id:
                  type: string
                  description: Unique identifier of the project
                  example: "507f1f77bcf86cd799439011"
                session_id:
                  type: string
                  description: Session identifier for the unit collection to drop
                  example: "session_507f1f77bcf86cd799439011"
      responses:
        '200':
          description: Collection dropped successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                    description: Success status indicator
                  message:
                    type: string
                    example: "Collection Dropped Successfully !"
                    description: Success message
              examples:
                success:
                  summary: Successful collection drop
                  value:
                    status: 1
                    message: "Collection Dropped Successfully !"
        '400':
          description: Bad request - Collection does not exist or validation errors
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    properties:
                      status:
                        type: integer
                        example: 0
                      message:
                        type: string
                        example: "Collection does not Exists"
                    description: Collection not found response
                  - type: object
                    properties:
                      errors:
                        type: array
                        items:
                          type: object
                          properties:
                            type:
                              type: string
                              example: "field"
                            value:
                              type: string
                              example: ""
                            msg:
                              type: string
                              example: "project_id is required"
                            path:
                              type: string
                              example: "project_id"
                            location:
                              type: string
                              example: "body"
                    description: Validation errors response
              examples:
                collection_not_exists:
                  summary: Collection does not exist
                  value:
                    status: 0
                    message: "Collection does not Exists"
                validation_error:
                  summary: Validation Error Example
                  value:
                    errors:
                      - type: "field"
                        value: ""
                        msg: "project_id is required"
                        path: "project_id"
                        location: "body"
                      - type: "field"
                        value: 123
                        msg: "Session Id must be a String"
                        path: "session_id"
                        location: "body"
                authorization_missing:
                  summary: Missing Authorization Header
                  value:
                    errors:
                      - type: "field"
                        value: ""
                        msg: "apikey is required"
                        path: "authorization"
                        location: "headers"
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "Authentication required"
                  error:
                    type: string
                    example: "Invalid or missing JWT token"
        '403':
          description: Forbidden - Insufficient project permissions
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "Access denied"
                  error:
                    type: string
                    example: "Insufficient permissions for this project"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  error:
                    type: string
                    example: "Error during Unit Collection Drop: Database connection failed"
              examples:
                server_error:
                  summary: Internal Server Error
                  value:
                    status: 0
                    error: "Error during Unit Collection Drop: Database connection failed"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

  schemas:
    FilterDropRequest:
      type: object
      required:
        - project_id
        - session_id
      properties:
        project_id:
          type: string
          description: Unique identifier of the project
          example: "507f1f77bcf86cd799439011"
        session_id:
          type: string
          description: Session identifier for the unit collection to drop
          example: "session_507f1f77bcf86cd799439011"
    
    SuccessResponse:
      type: object
      properties:
        status:
          type: integer
          example: 1
          description: Success status indicator (1 = success)
        message:
          type: string
          example: "Collection Dropped Successfully !"
          description: Success message
    
    ErrorResponse:
      type: object
      properties:
        status:
          type: integer
          example: 0
          description: Error status indicator (0 = error)
        message:
          type: string
          example: "Collection does not Exists"
          description: Error message
        error:
          type: string
          example: "Error during Unit Collection Drop: Database connection failed"
          description: Detailed error information
    
    ValidationError:
      type: object
      properties:
        errors:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                example: "field"
              value:
                type: string
                example: ""
              msg:
                type: string
                example: "project_id is required"
              path:
                type: string
                example: "project_id"
              location:
                type: string
                example: "body"

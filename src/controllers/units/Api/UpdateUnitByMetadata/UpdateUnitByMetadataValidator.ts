import { Request, Response, NextFunction } from 'express';
import { body, check, header, validationResult } from 'express-validator';
import { measurementType, priceCurrency } from '../../../../types/units';
import { StatusModule } from '../../../../modules/status';

const updateUnitByMetadataValidator = [
  check('authorization', 'apikey is required').notEmpty(),
  header('project_id', 'project_id is required').notEmpty(),
  body().isArray().withMessage(' must be an array'),
  body().isArray({ min: 1, max: 1 }).withMessage('Only one object is allowed in the array'),

  body('*').custom((item, { req, path }) => {
    const pathParts = path.split('.');
    if (pathParts.length > 1) {
      return true;
    } // Skip nested validations

    const index = pathParts[0];

    // Check array length - only one object allowed
    if (Array.isArray(req.body) && req.body.length !== 1) {
      throw new Error(`Only one object is allowed. Found ${req.body.length} objects`);
    }

    if (!item || typeof item !== 'object') {
      throw new Error(`Item ${index} must be an object`);
    }

    // Metadata required
    if (!item.metadata) {
      throw new Error(`metadata is required for item ${index}`);
    }

    // At least one other field
    const otherFields = ['status', 'price', 'measurement', 'currency', 'measurement_type'];
    const hasOtherField = otherFields.some((field) => item[field] !== undefined);

    if (!hasOtherField) {
      throw new Error(`At least one of ${otherFields.join(', ')} fields is required for item ${index}`);
    }

    return true;
  }).exists().withMessage('Object is required'),

  body('*.metadata')
    .custom((value) => {
      if (!value || typeof value !== 'object') {
        throw new Error('metadata must be an object');
      }

      const keys = Object.keys(value);
      if (keys.length === 0) {
        throw new Error('metadata cant be empty, it must have atleast one key-value pair');
      }

      // Find empty values
      const emptyKeys = keys.filter((key) => {
        if (key){
          const val = value[key];
          return typeof val !== 'string' || val.trim() === '';
        }
        return typeof key !== 'string' || key.trim() === '';
      });

      if (emptyKeys.length > 0) {
        throw new Error(`metadata fields cannot be empty: ${emptyKeys.join(', ')}`);
      }

      return true;
    }),

  body('*.status')
    .optional()
    .isString().withMessage('status must be a string')
    .custom(async (value, { req }) => {
      if (!value) {
        return true;
      }

      const project_id = req.headers?.project_id as string | undefined;
      if (!project_id) {
        throw new Error('project_id is required in headers to validate status');
      }

      try {
        const statusModule = new StatusModule(project_id);
        const validSubstatuses = await statusModule.getAllSubstatus();

        // Check if the provided status exists in the valid substatuses
        if (!validSubstatuses.includes(value)) {
          throw new Error(`Invalid status: ${value}. Valid options are: ${validSubstatuses.join(', ')}`);
        }

        return true;
      } catch (error) {
        // If getAllSubstatus throws an error, re-throw it
        if (error instanceof Error) {
          throw error;
        }
        throw new Error('Error validating status');
      }
    }),

  body('*.price')
    .optional()
    .isString().withMessage('price must be a string')
    .isNumeric().withMessage('price must be a string containing only numbers'),

  body('*.currency').optional()
    .custom((value) => {
      // Check if valid currency
      const validCurrencies = Object.values(priceCurrency);
      if (!validCurrencies.includes(value)) {
        throw new Error(`Invalid currency: ${value}. Valid options: ${validCurrencies.join(', ')}`);
      }

      if (typeof value !== 'string') {
        throw new Error('currency must be a string');
      }

      return true;
    }),

  body('*.measurement')
    .optional()
    .isNumeric().withMessage('measurement must be a number'),

  body('*.measurement_type').optional()
    .custom((value) => {
      // Check if valid measurement type
      const validateMeasurementType = Object.values(measurementType);
      if (!validateMeasurementType.includes(value)) {
        throw new Error(`Invalid measurement type: ${value}. Valid options: ${validateMeasurementType.join(', ')}`);
      }

      if (typeof value !== 'string') {
        throw new Error('measurement_type must be a string');
      }

      return true;
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default  updateUnitByMetadataValidator;

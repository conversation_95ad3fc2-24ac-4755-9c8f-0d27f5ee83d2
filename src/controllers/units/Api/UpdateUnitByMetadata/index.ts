import { Response  } from 'express';
import { UnitModule, invalidateUnitAPIs } from '../../../../modules/units';
import logger from '../../../../config/logger';
import { ProjectModule } from '../../../../modules/projects';
import { Units } from '../../../../types/units';
import { ExtendedRequest } from '../../../../types/extras';
import { ppgUnitType, unitMetaData } from '../../../../types/webhooks';

export async function updateUnitByMetadata (
  request : ExtendedRequest,
  response : Response,
) :Promise<void>{

  const organization_id = request.organization_id as string;
  const project_id = request.headers.project_id as string;
  const unitData = request.body;
  const unit = new UnitModule(project_id);
  const projects = new ProjectModule(organization_id);
  const projectData = await projects.getProjectById(project_id);

  if (projectData){
    Promise.all(unitData.map((Data:ppgUnitType) => {
      return new Promise((resolve, reject) => {
        unit.updateUnitByMeta(Data.metadata as unitMetaData, Data).then((res:Units|null) => {
          resolve(res);
        }).catch((err:Error) => {
          reject(err);
        });
      });
    })).then(() => {
      response.send({status: 1, 'message': 'updated unit successfully'});

      invalidateUnitAPIs(organization_id, project_id).then((res) => {
        logger.info('Unit APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating unit APIs', { error: err });
      });
    }).catch((err) => {
      logger.error('Error in unitsArray Mapping', {message: err});
      response.status(400).send({status: 0, message: err});
    });
  } else {
    response.send({status: 0, message: 'project_id not found'});
  }

}

openapi: 3.0.3
info:
  title: PropVR Units - Update Unit By Metadata API
  description: Update unit information using metadata for property management
  version: 1.0.0

servers:
  - url: https://api.propvr.com/api
  - url: https://staging-api.propvr.com/api

security:
  - BearerAuth: []

paths:
  /unit/updateUnitByMetadata:
    post:
      operationId: updateUnitByMetadata
      tags:
        - Units
      summary: Update Unit By Metadata
      description: |
        Update unit information using metadata for a specific project. This endpoint allows you to:
        - Update unit properties based on metadata matching
        - Modify unit status, price, measurements, and currency
        - Process single unit updates with comprehensive validation
        - Maintain data integrity through metadata-based matching
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: header
          required: true
          schema:
            type: string
          description: Project ID to identify which project the unit belongs to
          example: "507f1f77bcf86cd799439011"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              minItems: 1
              maxItems: 1
              description: Array containing exactly one unit object to update
              items:
                type: object
                required:
                  - metadata
                properties:
                  metadata:
                    type: object
                    description: Metadata object used to identify and match the unit
                    additionalProperties:
                      type: string
                    example:
                      unit_number: "A101"
                      floor: "1"
                      building: "Tower A"
                    minProperties: 1
                  status:
                    type: string
                    description: New status for the unit
                    enum: ["available", "onhold", "reserved", "sold"]
                    example: "available"
                  price:
                    type: string
                    description: New price for the unit (numeric string)
                    pattern: "^[0-9]+(\\.[0-9]+)?$"
                    example: "850000"
                  currency:
                    type: string
                    description: Currency code for the price
                    enum: [
                      "aed", "ars", "aud", "bgn", "brl", "bsd", "cad", "chf", "clp", "cny",
                      "cop", "czk", "dkk", "dop", "egp", "eur", "fjd", "gbp", "gtq", "hkd",
                      "hrk", "huf", "idr", "ils", "inr", "isk", "jpy", "krw", "kzt", "lbp",
                      "mxn", "myr", "nok", "nzd", "pab", "pen", "php", "pkr", "pln", "pyg",
                      "qar", "ron", "rub", "sar", "sek", "sgd", "thb", "try", "twd", "uah",
                      "usd", "uyu", "vnd", "zar", "jod", ""
                    ]
                    example: "usd"
                  measurement:
                    type: number
                    description: Unit measurement/area
                    example: 1200.5
                  measurement_type:
                    type: string
                    description: Type of measurement unit
                    enum: ["sqft", "sqmt"]
                    example: "sqft"
                anyOf:
                  - required: ["status"]
                  - required: ["price"]
                  - required: ["measurement"]
                  - required: ["currency"]
                  - required: ["measurement_type"]
            examples:
              update_status_and_price:
                summary: Update unit status and price
                value:
                  - metadata:
                      unit_number: "A101"
                      floor: "1"
                      building: "Tower A"
                    status: "reserved"
                    price: "850000"
                    currency: "usd"
              update_measurement:
                summary: Update unit measurement
                value:
                  - metadata:
                      unit_id: "UNIT_001"
                      block: "B"
                    measurement: 1500.75
                    measurement_type: "sqft"
      responses:
        '200':
          description: Unit updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 1
                    description: Success status indicator
                  message:
                    type: string
                    example: "updated unit successfully"
                    description: Success message
              examples:
                success:
                  summary: Successful unit update
                  value:
                    status: 1
                    message: "updated unit successfully"
        '400':
          description: Bad request - Validation errors or update failures
          content:
            application/json:
              schema:
                oneOf:
                  - type: object
                    properties:
                      errors:
                        type: array
                        items:
                          type: object
                          properties:
                            type:
                              type: string
                              example: "field"
                            value:
                              type: string
                              example: ""
                            msg:
                              type: string
                              example: "metadata is required for item 0"
                            path:
                              type: string
                              example: "0"
                            location:
                              type: string
                              example: "body"
                    description: Validation errors response
                  - type: object
                    properties:
                      status:
                        type: integer
                        example: 0
                      message:
                        type: string
                        example: "Unit update failed"
                    description: Update failure response
              examples:
                validation_error:
                  summary: Validation Error Example
                  value:
                    errors:
                      - type: "field"
                        value: ""
                        msg: "metadata is required for item 0"
                        path: "0"
                        location: "body"
                      - type: "field"
                        value: "invalid_status"
                        msg: "Invalid status. Valid options are: available, onhold, reserved, sold"
                        path: "0.status"
                        location: "body"
                array_length_error:
                  summary: Array Length Error
                  value:
                    errors:
                      - type: "field"
                        value: []
                        msg: "Only one object is allowed. Found 2 objects"
                        path: "0"
                        location: "body"
                metadata_empty_error:
                  summary: Empty Metadata Error
                  value:
                    errors:
                      - type: "field"
                        value: {}
                        msg: "metadata cant be empty, it must have atleast one key-value pair"
                        path: "0.metadata"
                        location: "body"
                missing_fields_error:
                  summary: Missing Required Fields Error
                  value:
                    errors:
                      - type: "field"
                        value: {"metadata": {"unit_id": "A101"}}
                        msg: "At least one of status, price, measurement, currency, measurement_type fields is required for item 0"
                        path: "0"
                        location: "body"
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "Authentication required"
                  error:
                    type: string
                    example: "Invalid or missing JWT token"
        '404':
          description: Project not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "project_id not found"
              examples:
                project_not_found:
                  summary: Project Not Found
                  value:
                    status: 0
                    message: "project_id not found"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 0
                  message:
                    type: string
                    example: "Error updating unit"
              examples:
                server_error:
                  summary: Internal Server Error
                  value:
                    status: 0
                    message: "Database connection failed"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

  schemas:
    UnitUpdateRequest:
      type: array
      minItems: 1
      maxItems: 1
      items:
        type: object
        required:
          - metadata
        properties:
          metadata:
            type: object
            additionalProperties:
              type: string
            minProperties: 1
            description: Metadata object used to identify and match the unit
          status:
            $ref: '#/components/schemas/UnitStatus'
          price:
            type: string
            pattern: "^[0-9]+(\\.[0-9]+)?$"
            description: Unit price as numeric string
          currency:
            $ref: '#/components/schemas/Currency'
          measurement:
            type: number
            description: Unit measurement/area
          measurement_type:
            $ref: '#/components/schemas/MeasurementType'
        anyOf:
          - required: ["status"]
          - required: ["price"]
          - required: ["measurement"]
          - required: ["currency"]
          - required: ["measurement_type"]
    
    UnitStatus:
      type: string
      enum: ["available", "onhold", "reserved", "sold"]
      description: Status of the unit
      
    Currency:
      type: string
      enum: [
        "aed", "ars", "aud", "bgn", "brl", "bsd", "cad", "chf", "clp", "cny",
        "cop", "czk", "dkk", "dop", "egp", "eur", "fjd", "gbp", "gtq", "hkd",
        "hrk", "huf", "idr", "ils", "inr", "isk", "jpy", "krw", "kzt", "lbp",
        "mxn", "myr", "nok", "nzd", "pab", "pen", "php", "pkr", "pln", "pyg",
        "qar", "ron", "rub", "sar", "sek", "sgd", "thb", "try", "twd", "uah",
        "usd", "uyu", "vnd", "zar", "jod", ""
      ]
      description: Supported currency codes
      
    MeasurementType:
      type: string
      enum: ["sqft", "sqmt"]
      description: Type of measurement unit (square feet or square meters)
    
    SuccessResponse:
      type: object
      properties:
        status:
          type: integer
          example: 1
          description: Success status indicator (1 = success)
        message:
          type: string
          example: "updated unit successfully"
          description: Success message
    
    ErrorResponse:
      type: object
      properties:
        status:
          type: integer
          example: 0
          description: Error status indicator (0 = error)
        message:
          type: string
          example: "project_id not found"
          description: Error message
    
    ValidationError:
      type: object
      properties:
        errors:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                example: "field"
              value:
                type: string
                example: ""
              msg:
                type: string
                example: "metadata is required for item 0"
              path:
                type: string
                example: "0"
              location:
                type: string
                example: "body"

import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { Direction } from '../../../types/direction';
import { measurementType, priceCurrency } from '../../../types/units';
import { StatusModule } from '../../../modules/status';

const createUnitValidate = [
  body('name', 'Building Name is required').notEmpty(),
  body('project_id', 'Building Type is required').notEmpty(),
  body('unitplan_id', 'Floor Count is required').notEmpty(),
  body('currency').isIn(Object.values(priceCurrency)),
  body('measurement').isNumeric().optional(),
  body('balcony_measurement').optional({ checkFalsy: true }).isNumeric(),
  body('balcony_measurement_type').optional().isIn(Object.values(measurementType)),
  body('suite_area').optional({ checkFalsy: true }).isNumeric(),
  body('suite_area_type').optional().isIn(Object.values(measurementType)),
  body('status', 'status is required')
    .notEmpty()
    .isString().withMessage('status must be a string')
    .custom(async (value, { req }) => {
      const project_id = req.body?.project_id;
      if (!project_id) {
        throw new Error('project_id is required in request body to validate status');
      }

      try {
        const statusModule = new StatusModule(project_id);
        const validSubstatuses = await statusModule.getAllSubstatus();

        // Check if the provided status exists in the valid substatuses
        if (!validSubstatuses.includes(value)) {
          throw new Error(`Invalid status: ${value}. Valid options are: ${validSubstatuses.join(', ')}`);
        }

        return true;
      } catch (error) {
        if (error instanceof Error) {
          throw error;
        }
        throw new Error('Error validating status');
      }
    }),
  body('cta_link').optional().isString(),
  body('direction').optional().isIn(Object.values(Direction)),

  body('building_id')
    .optional({ checkFalsy: true })
    .isString()
    .withMessage('building_id must be a string'),

  // Validate floor_id with proper dependency on building_id
  body('floor_id')
    .custom((value, { req }) => {
      if (req.body.building_id && !value) {
        throw new Error('floor_id is required when building_id is provided');
      }
      return true;
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createUnitValidate;

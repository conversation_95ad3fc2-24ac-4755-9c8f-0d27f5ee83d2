import { Request, Response } from 'express';
import { Units } from '../../../types/units';
import { UnitModule, invalidateUnitAPIs } from '../../../modules/units';
import { Types } from 'mongoose';
import logger from '../../../config/logger';

export async function CreateUnits (
  request: Request,
  response: Response,
): Promise<Units | void> {

  const { unitplan_id, project_id, name, status, metadata,
    floor_id, building_id, community_id, price, currency, tour_id, measurement, measurement_type,
    balcony_measurement, balcony_measurement_type, cta_link, max_price, suite_area, suite_area_type,
    direction,
  } = request.body;

  const organization_id = request.headers.organization as string;
  const unit = new UnitModule(project_id);

  // Build payload object, excluding direction if it's "" or null
  const unitPayload: any = {
    _id: new Types.ObjectId(),
    unitplan_id,
    project_id,
    name,
    status,
    metadata,
    floor_id,
    building_id,
    community_id,
    currency,
    price,
    tour_id,
    measurement,
    measurement_type,
    balcony_measurement,
    balcony_measurement_type,
    cta_link,
    max_price,
    suite_area,
    suite_area_type,
  };

  // Only add direction if it's not "" or null
  if (direction !== null && direction !== undefined && direction !== '') {
    unitPayload.direction = direction;
  }

  await unit
    .createUnit(unitPayload, organization_id)
    .then(async (unitData) => {
      response.status(201).json({ status: 1, data: unitData });

      invalidateUnitAPIs(organization_id, project_id).then((res) => {
        logger.info('Unit APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating unit APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in createUnit', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the units' });
      console.error(error);
    });
}

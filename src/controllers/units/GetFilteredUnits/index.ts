import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { UnitModule } from '../../../modules/units';
import logger from '../../../config/logger';

export async function getFilteredUnits (
  request:ExtendedRequest,
  response:Response,
):Promise<void>{
  const { project_id } = request.params;
  const query = { ...request.query };
  const unit = new UnitModule(project_id);

  const unitData = await unit.getFilteredUnits(query);

  if (unitData) {
    response.status(200).json({ status: 1, data: unitData });
  } else {
    logger.error('No Units found');
    response.status(404).json({ status: 0, error: 'No Units found' });
  }

}

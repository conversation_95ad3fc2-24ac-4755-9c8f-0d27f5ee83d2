openapi: 3.0.3
info:
  title: PropVR Units Webhooks API
  description: |
    ## Units Management Webhooks
    
    **For Property Management Companies & Real Estate Agencies**
    
    Unit management webhooks enable your organization to synchronize property data, 
    pricing, and availability between your property management system and PropVR's platform.
    Perfect for real estate agencies and property developers who need real-time unit status updates.
  version: 1.0.0

servers:
  - url: https://api.propvr.com/webhooks
    description: Production server
  - url: https://staging-api.propvr.com/webhooks
    description: Staging server

security:
  - BearerAuth: []

paths:
  /ppg/updateUnit:
    post:
      operationId: updateUnitByMeta
      tags:
        - Units
      summary: "Update Unit by Metadata"
      description: |
        Updates one or more units based on their metadata.This webhook is typically called 
        by external property management systems when unit information changes.
    
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: header
          required: true
          schema:
            type: string
          description: The ID of the project containing the units
          example: "507f1f77bcf86cd799439011"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - metadata
              properties:
                metadata:
                  type: object
                  description: Containes the key value pairs to be added in metadata object
                  required:
                    - key
                  properties:
                    key:
                      type: string
                      description: Unit metadata value
                  example:
                    unit_number: "101"
                    floor: "1"
                    building: "A"
                status:
                  type: string
                  enum: [available, sold, reserved, under_construction]
                  description: Current status of the unit
                  example: "available"
                price:
                  type: number
                  description: Unit price
                  example: 500000
                  minimum: 0
                measurement:
                  type: string
                  description: Measurement value for the unit 
                  example: 9832
                measurement_type:
                  type: string
                  description: Unit of measurement
                  enum: [sqmt, sqft] 
                  example: sqmt
            example:
              metadata:
                key: "value"
              status: "available"
              price: 500000
              measurement: "9832"
              measurement_type: "sqft"

      responses:
        '200':
          description: Units updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
              example:
                status: 1
                message: "Status updated successfully for unit"
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 0
                error: "project_id not found"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /ppg/uploadcsv:
    post:
      operationId: uploadCsvFile
      tags:
        - Units
      summary: "Upload CSV File"
      description: |
        Uploads a CSV file containing unit data. The file is processed and a notification 
        is sent to Slack channel upon successful upload.
        
        **Use Case**: Bulk import of unit data from external systems.
      security:
        - BearerAuth: []
      parameters:
        - name: project_id
          in: header
          required: true
          schema:
            type: string
          description: The ID of the project
          example: "507f1f77bcf86cd799439011"
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
                  description: CSV file containing unit data
      responses:
        '200':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
              example:
                status: 1
                message: "File uploaded successfully"
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 0
                error: "file not found"
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication. Include the token in the Authorization header:
        ```
        Authorization: Bearer <your_jwt_token>
        ```

  schemas:
    UnitUpdateData:
      type: object
      required:
        - metadata
      properties:
        metadata:
          type: object
          description: Unit metadata for identification
        status:
          type: string
          enum: [available, sold, reserved, under_construction]
          description: Current status of the unit
          example: "available"
        price:
          type: number
          description: Unit price
          example: 500000
          minimum: 0
        measurement:
          type: string
          description: Measurement value for the unit
          example: 9832
        measurement_type:
          type: string
          enum: [sqmt, sqft ]
          description: Unit of measurement
          example: "sqmt"

    SuccessResponse:
      type: object
      properties:
        status:
          type: integer
          description: Success status (always 1)
          example: 1
        message:
          type: string
          description: Success message
          example: "Operation completed successfully"
    ErrorResponse:
      type: object
      properties:
        status:
          type: integer
          description: Error status (always 0)
          example: 0
        error:
          type: string
          description: Error message
          example: "Invalid parameters provided"

  responses:
    Unauthorized:
      description: Authentication required or invalid token
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 0
            error: "Invalid or missing authentication token"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            status: 0
            error: "Internal server error occurred"

tags:
  - name: Units
    description: |
      **For Property Management Companies & Real Estate Agencies**
      
      Unit management webhooks enable your organization to synchronize property data, 
      pricing, and availability between your property management system and PropVR's platform.
      Perfect for real estate agencies and property developers who need real-time unit status updates.

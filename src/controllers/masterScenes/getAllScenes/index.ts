import { Request, Response } from 'express';
import { MasterSceneModule } from '../../../modules/masterScene';
import { MasterSVGModule } from '../../../modules/masterSVG';
import logger from '../../../config/logger';

export async function getAllScenes (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const type = request.query.type as string;
  const parent = request.query.parent as string;
  //   Pass organization_id in this ProjectModule
  const masterScene = new MasterSceneModule(organization_id);

  const masterScenes = await masterScene.getAllScenes(type, parent);
  const svg = new MasterSVGModule(organization_id);
  if (masterScenes) {
    const keys = Object.keys(masterScenes);
    const promises = keys.map(async (key) => {
      const svgData = await svg.getSvgById(key);
      if (svgData === null || Object.keys(svgData).length === 0) {
        return;
      }
      masterScenes[key].svgData = svgData;
    });
    await Promise.all(promises).then(() => {
      response.status(200).json({ status: 1, data: masterScenes});
    });
  } else {
    logger.error('Scene not found');
    response.status(404).json({ status: 0, error: 'scene not found' });
  }
}

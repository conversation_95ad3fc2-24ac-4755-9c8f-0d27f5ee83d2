import { ExtendedRequest } from '../../../types/extras';
import { masterScene } from '../../../types/masterScene';
import { MasterSceneModule } from '../../../modules/masterScene';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<masterScene | void> {
  const organization_id = request.organization_id as string;
  const scene = new MasterSceneModule(organization_id);
  const scene_Ids = request.body.scene_Ids;
  const timeStamp = request.body.timeStamp;
  await scene
    .moveToTrash(scene_Ids, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error while deleting master scenes', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting master scenes : '+ error });
    });
}

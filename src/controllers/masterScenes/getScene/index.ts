import { Request, Response } from 'express';
import { MasterSceneModule } from '../../../modules/masterScene';
import { MasterSVGModule } from '../../../modules/masterSVG';
import logger from '../../../config/logger';

export async function   getScene (
  request: Request,
  response: Response,
): Promise<void> {
  const { scene_id } = request.params;
  const organization_id = request.headers.organization as string;
  //   Pass organization_id in this ProjectModule
  const masterScene = new MasterSceneModule(organization_id);

  const mastersceneData = await masterScene.getScene(scene_id);
  const svg = new MasterSVGModule(organization_id);

  const svgData = await svg.getSvgById(scene_id);
  if (mastersceneData) {
    response.status(200).json({ status: 1, data: {sceneData: mastersceneData, svgData: svgData} });
  } else {
    logger.error('Scene not found');
    response.status(404).json({ status: 0, error: 'scene not found' });
  }
}

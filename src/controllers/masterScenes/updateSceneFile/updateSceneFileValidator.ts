import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const UpdateSceneFilesValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('scene_id', 'Project Scene ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default UpdateSceneFilesValidate;

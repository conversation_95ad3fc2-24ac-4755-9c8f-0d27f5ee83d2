import {Response} from 'express';
import { FileRequest } from '../../../types/extras';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { updateProjectSceneObj } from '../../../types/projectScene';
import logger from '../../../config/logger';
import { Types } from 'mongoose';
import { EnvVar } from '../../../types/projectSVG';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
import { MasterSceneModule, invalidateMasterSceneAPIs } from './../../../modules/masterScene/index';
export default async function updateSceneFiles (request:FileRequest, response:Response): Promise<void> {
  const reqbody = request.body;
  const organization_id = request.headers.organization as string;
  const projectScene = new MasterSceneModule(organization_id);
  const _id = new Types.ObjectId();
  const storagePath = (projectScene.storagepath + _id) as string;
  if (!request.files) {
    // Handle Multer errors
    response.send('Error reading SVG file:');
    return;
  }
  const requestFiles = request.files;
  if (requestFiles === undefined ){
    response.send('Error reading SVG file:');
    return;
  }
  UploadUnitplanFiles(requestFiles, projectScene.storagepath+reqbody.scene_id)
    .then((urlObject:{ [key: string]: string }) => {
      const payload: updateProjectSceneObj = {
        lowRes: urlObject.lowRes?urlObject.lowRes:undefined,
        highRes: urlObject.highRes?urlObject.highRes:undefined,
        ...(urlObject.lowResNight && {lowResNight: urlObject.lowResNight}),
        ...(urlObject.highResNight && {highResNight: urlObject.highResNight}),
        video: urlObject.video?urlObject.video:undefined,
        info_icon: urlObject.info_icon?urlObject.info_icon:undefined,
      };
      projectScene.getScene(reqbody.scene_id).then(async (scene) => {

        try {
          if (scene.type === 'deep_zoom') {
            if ((reqbody.file_url || reqbody.file_url_night) && storagePath && reqbody.scene_id && organization_id){
              const handleLumaCloudRunJob = async (url:string, file_type:string) => {
                const envVars: EnvVar[] = [
                  { name: 'organization_id', value: organization_id },
                  { name: 'scene_id', value: reqbody.scene_id },
                  { name: 'storagepath', value: storagePath },
                  { name: 'url', value: url },
                  { name: 'scene_type', value: 'master' },
                  {name: 'file_type', value: file_type},
                ];

                console.log('handleLumaCloudRunJob called ->', {organization_id});

                const jobId = 'luma-blender-jobs';
                const runJob = new cloudRunJobModule();
                runJob.runJobExecution(jobId, envVars);
              };
              if (reqbody.file_url){
                payload.file_url = reqbody.file_url;
                handleLumaCloudRunJob(reqbody.file_url, 'day_file');
              }

              if (reqbody.file_url_night){
                payload.file_url_night = reqbody.file_url_night;
                handleLumaCloudRunJob(reqbody.file_url_night, 'night_file');
              }
            } else {
              console.log('Env Variables are not set');
              throw new Error('Env Variables are not set');
            }
          }
          const res = await projectScene.UpdateSceneFiles(reqbody.scene_id, payload, scene.type);
          response.send({ status: 1, message: 'updateSceneFiles Successful', data: res });

          // Invalidate master scene APIs (non-blocking)
          invalidateMasterSceneAPIs(organization_id, reqbody.scene_id).then((invalidateRes) => {
            logger.info('Master scene APIs invalidated successfully', { result: invalidateRes });
          }).catch((err) => {
            logger.error('Error invalidating master scene APIs', { error: err });
          });
        } catch (error) {
          logger.error('Error in updateSceneFiles', { message: error });
          console.log('error');

          response.send({ status: 0, message: error });
        }
      });
    });
}

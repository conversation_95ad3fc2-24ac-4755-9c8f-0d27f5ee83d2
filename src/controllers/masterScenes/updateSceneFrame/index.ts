import {Response} from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import { MasterSceneModule } from './../../../modules/masterScene/index';

export async function updateBulkSceneFrames (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const sceneMod = new MasterSceneModule(organization_id);
  sceneMod.updateBulkSceneFrame(request.body)
    .then((res) => {
      response.status(201).json({ status: 1, message: res });
    })
    .catch((error: Error) => {
      logger.error('Error in updateBulkSceneFrames', {message: error});
      response
        .status(500)
        .json({ status: 0, error: error});
    });
}

import { ExtendedRequest } from '../../../types/extras';
import { masterScene } from '../../../types/masterScene';
import { MasterSceneModule } from '../../../modules/masterScene';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function updateCoordinateSettings (
  request: ExtendedRequest,
  response: Response,
): Promise<masterScene | void> {
  const organization_id = request.organization_id as string;
  const scene = new MasterSceneModule(organization_id);

  const coord_settings = {
    hoverColor: request.body.hoverColor,
    defaultColor: request.body.defaultColor,
    textClass: request.body.textClass,
  };
  const targetSceneId = request.body.masterSceneId;
  await scene
    .updateCoordinateSettings(targetSceneId, coord_settings)
    .then((sceneData) => {
      response.status(201).json({ status: 1, data: sceneData });
    })
    .catch((error: Error) => {
      logger.error('Error while updating coordinate', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating coordinates'+ error });
    });
}

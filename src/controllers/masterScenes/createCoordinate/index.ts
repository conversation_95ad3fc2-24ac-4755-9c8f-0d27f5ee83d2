import { FileRequest } from '../../../types/extras';
import { masterScene } from '../../../types/masterScene';
import { MasterSceneModule, invalidateMasterSceneAPIs } from '../../../modules/masterScene';
import { Response } from 'express';
import mongoose from 'mongoose';
import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
export async function CreateCoordinate (
  request: FileRequest,
  response: Response,
): Promise<masterScene | void> {
  const organization_id = request.organization_id as string;
  const scene = new MasterSceneModule(organization_id);
  const coordinate_id = new mongoose.Types.ObjectId();
  const requestFiles = request.files;
  const coord = {
    _id: coordinate_id,
    lat: request.body.lat,
    lng: request.body.lng,
    name: request.body.name,
    linkType: request.body.linkType,
    ...(request.body.link && { link: request.body.link}),
    ...(request.body.scene_id && { scene_id: request.body.scene_id}),
    ...(request.body.project_id && { project_id: request.body.project_id}),
    active: request.body.active || false,
    ...(request.body.country && { country: request.body.country}),
  };
  if (requestFiles){
    UploadUnitplanFiles(requestFiles, scene.storagepath + 'coordinates/' + coordinate_id)
      .then((urlObject: { [key: string]: string }) => {
        coord.video = urlObject.video;
        const targetSceneId = request.body.masterSceneId;
        scene
          .createCoordinate(coord, targetSceneId)
          .then((sceneData) => {
            response.status(201).json({ status: 1, data: sceneData });

            // Invalidate master scene APIs (non-blocking)
            invalidateMasterSceneAPIs(organization_id).then((invalidateRes) => {
              logger.info('Master scene APIs invalidated successfully', { result: invalidateRes });
            }).catch((err) => {
              logger.error('Error invalidating master scene APIs', { error: err });
            });
          })
          .catch((error: Error) => {
            logger.error('Error while creating coordinates', {message: error});
            response
              .status(500)
              .json({ status: 0, error: 'Error while creating coordinates'+ error });
          });
      })
      .catch((error: Error) => {
        logger.error('Error while uploading video', {message: error});
        response.status(500).json({ status: 0, error: 'Error while uploading video'+ error });
      });
  } else {
    const targetSceneId = request.body.masterSceneId;
    scene
      .createCoordinate(coord, targetSceneId)
      .then((sceneData) => {
        response.status(201).json({ status: 1, data: sceneData });

        // Invalidate master scene APIs (non-blocking)
        invalidateMasterSceneAPIs(organization_id).then((invalidateRes) => {
          logger.info('Master scene APIs invalidated successfully', { result: invalidateRes });
        }).catch((err) => {
          logger.error('Error invalidating master scene APIs', { error: err });
        });
      })
      .catch((error: Error) => {
        logger.error('Error while creating coordinates', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error while creating coordinates'+ error });
      });
  }
}

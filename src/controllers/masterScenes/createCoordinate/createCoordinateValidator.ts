import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { coordinateLinkType } from '../../../types/masterScene';
import logger from '../../../config/logger';

interface UploadedFiles {
  video: Express.Multer.File[];
}

const createCoordinateValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.files);
  console.log(req.body);

  // Validate file structure if files are provided (file is optional)
  const files = req.files as UploadedFiles | undefined;
  // Only validate if video field exists in files (meaning a file was actually uploaded)
  if (files && 'video' in files) {
    // If video field exists but is empty, it's invalid
    if (!files.video || files.video.length === 0) {
      logger.error('Invalid file structure: video field is missing or empty when files are provided.');
      res.status(400).send({status: 0, error: 'Invalid file structure: video field is missing or empty when files are provided.'});
      return;
    }
  }

  // Basic required fields
  const requiredTextFields = [
    'lat',
    'lng',
    'name',
    'linkType',
    'masterSceneId',
    'country',
  ];

  const missingTextFields = requiredTextFields.filter(
    (field) => !(field in req.body),
  );

  if (missingTextFields.length > 0) {
    logger.error(`Missing fields: ${missingTextFields.join(', ')}`);
    res.status(400).send({status: 0, error: `Missing text fields: ${missingTextFields.join(', ')}`});
    return;
  }

  // Validate linkType value
  const linkType = req.body.linkType;
  if (!Object.values(coordinateLinkType).includes(linkType)) {
    logger.error(`Invalid linkType: ${linkType}`);
    res.status(400).send({status: 0, error: `Invalid linkType. Must be one of: ${Object.values(coordinateLinkType).join(', ')}`});
    return;
  }

  // Conditional validation based on linkType
  if (linkType === coordinateLinkType.PROJECT || linkType === coordinateLinkType.MASTER) {
    if (!req.body.scene_id) {
      logger.error('scene_id is required when linkType is project or master');
      res.status(400).send({status: 0, error: 'scene_id is required when linkType is project or master'});
      return;
    }
  }

  if (linkType === coordinateLinkType.PROJECT) {
    if (!req.body.project_id) {
      logger.error('project_id is required when linkType is project');
      res.status(400).send({status: 0, error: 'project_id is required when linkType is project'});
      return;
    }
  }

  if (linkType === coordinateLinkType.EXTERNAL) {
    if (!req.body.link) {
      logger.error('link is required when linkType is external');
      res.status(400).send({status: 0, error: 'link is required when linkType is external'});
      return;
    }
    if (typeof req.body.link !== 'string') {
      logger.error('link must be a string when linkType is external');
      res.status(400).send({status: 0, error: 'link must be a string when linkType is external'});
      return;
    }
  }

  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    console.log(errors);
    res.status(400).send({status: 0, error: errors.array()});
    return;
  }

  next();
};

export default createCoordinateValidate;

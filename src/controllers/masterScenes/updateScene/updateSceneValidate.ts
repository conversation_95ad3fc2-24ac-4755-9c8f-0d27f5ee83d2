import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { Direction } from '../../../types/direction';

const UpdateSceneValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('scene_id', 'Project Scene ID is required').notEmpty(),
  body('direction')
    .optional()
    .custom((value, { req }) => {
      // Direction field is not allowed for gsplat and rotatable_image scene types
      if (req.body.type === 'gsplat' || req.body.type === 'rotatable_image') {
        if (value !== null && value !== undefined && value !== '') {
          return false;
        }
        return true;
      }
      if (value === null || value === '') {
        return true;
      }
      return Object.values(Direction).includes(value);
    })
    .withMessage('Direction field is not allowed for gsplat and rotatable_image scene types'),
  body('north_direction')
    .optional()
    .custom((value) => {
      if (value === null || value === undefined) {
        return true;
      }
      if (typeof value !== 'object') {
        return false;
      }
      if (!value.position || typeof value.position !== 'object') {
        return false;
      }
      if (typeof value.position.x !== 'number' ||
          typeof value.position.y !== 'number' ||
          typeof value.position.z !== 'number') {
        return false;
      }
      if (!value.rotation || typeof value.rotation !== 'object') {
        return false;
      }
      if (typeof value.rotation.x !== 'number' ||
          typeof value.rotation.y !== 'number' ||
          typeof value.rotation.z !== 'number' ||
          typeof value.rotation.w !== 'number') {
        return false;
      }
      return true;
    })
    .withMessage('Invalid north_direction format. Must have position {x, y, z} and rotation {x, y, z, w}'),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default UpdateSceneValidate;

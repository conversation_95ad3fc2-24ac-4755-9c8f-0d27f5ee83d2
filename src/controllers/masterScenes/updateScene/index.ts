import {Request, Response} from 'express';
import { MasterSceneModule, invalidateMasterSceneAPIs } from '../../../modules/masterScene/index';
import logger from '../../../config/logger';
export default async function updateScene (request:Request, response:Response): Promise<void> {
  const masterScene = new MasterSceneModule(request.headers.organization as string);
  masterScene.updateScene(request.body.scene_id, request.body).then(async (res) => {
    response.send({status: 1, data: res});

    // Invalidate master scene APIs (non-blocking)
    invalidateMasterSceneAPIs(request.headers.organization as string, request.body.scene_id).then((invalidateRes) => {
      logger.info('Master scene APIs invalidated successfully', { result: invalidateRes });
    }).catch((err) => {
      logger.error('Error invalidating master scene APIs', { error: err });
    });
  }).catch((error) => {
    response.send({status: 0, message: error});
  });
}

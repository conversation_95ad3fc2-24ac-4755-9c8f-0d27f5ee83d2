import {Request, Response} from 'express';
import { deep_zoom_status } from '../../../types/projectScene';
import { MasterSceneModule, invalidateMasterSceneAPIs } from '../../../modules/masterScene/index';
import logger from '../../../config/logger';
export default async function updateSceneAPI (request:Request, response:Response): Promise<void> {
    interface updateScene {
        organization_id: string,
        project_id: string,
        scene_id: string
        type?: string,
        name?: string,
        active?: boolean,
        parent?: number,
        info_text?: string,
        building_id?: string,
        root?: boolean,
        clouds?: boolean,
        category?:string,
        position?:object,
        polar_angle?:object,
        distance?:object,
        auto_rotate?:boolean,
        highRes?:string,
        highResNight?:string,
        deep_zoom_status?:{
          type:string,
          enum:deep_zoom_status
        },
        deep_zoom_failed_info?:string,
        earth_position?: {
          x_axis:number,
          y_axis:number,
          z_axis:number
        };
        north_direction?: {
          position: {
            x: number;
            y: number;
            z: number;
          };
          rotation: {
            x: number;
            y: number;
            z: number;
            w: number;
          };
        } | null;
      }
    const reqbody:updateScene = request.body;
    const masterScene = new MasterSceneModule(reqbody.organization_id);
    masterScene.updateScene(reqbody.scene_id, request.body).then(async (res) => {
      response.send({status: 1, data: res});

      // Invalidate master scene APIs (non-blocking)
      invalidateMasterSceneAPIs(reqbody.organization_id, reqbody.scene_id).then((invalidateRes) => {
        logger.info('Master scene APIs invalidated successfully', { result: invalidateRes });
      }).catch((err) => {
        logger.error('Error invalidating master scene APIs', { error: err });
      });
    }).catch((error) => {
      response.send({status: 0, message: error});
    });
}

import { MailerModule } from '../../../modules/mailer';
import { UserModule } from '../../../modules/user';
import { OrganizationModule } from '../../../modules/organization';
import { Request, Response } from 'express';
import { mailUserData } from '../../../types/mailer';
import { SessionModule } from '../../../modules/sessions';
import { NotificationModule } from '../../../modules/notification';
import { UserDetailsWithFCM } from '../../../types/meetings';
import logger from '../../../config/logger';
export default async function sendInviteHost (
  request: Request,
  response: Response,
):Promise<void> {
  try {

    const mailer = new MailerModule();
    const session_id = request.body.session_id;
    const sessionModule = new SessionModule();
    const sessionData = await sessionModule.getSessionById(session_id);
    const notificationModule = new NotificationModule(sessionData.user_id);

    if (!sessionData) {
      logger.error('Session Data not found for host invite', { session_id });
      response.status(404).send({ status: 0, message: 'Session Data not found' });
      return;
    }

    const userModule = new UserModule();
    const userDetail = await userModule.GetUserDetails(sessionData.user_id) as UserDetailsWithFCM;
    const username = await userModule.getUsername(sessionData.user_id);
    const userEmail = await userModule.getUserEmail(sessionData.user_id);

    if (!username || !userEmail) {
      logger.error('User Data not found for host invite', { username, userEmail, user_id: sessionData.user_id });
      response.status(404).send({ status: 0, message: 'User Data not found' });
      return;
    }

    const organizationModule = new OrganizationModule();
    const organizationData = await organizationModule.GetOrganization(sessionData.organization_id);

    const userData: mailUserData = {
      username: username,
      useremail: userEmail,
    };

    const thread_id = await mailer.SendHostInviteMail(sessionData, organizationData, userData);

    if (!sessionData.thread_id && thread_id) {
      await sessionModule.UpdateThreadId(session_id, thread_id);
    }
    const tokens = userDetail.fcmToken;
    if (!tokens || (Array.isArray(tokens) && tokens.length === 0)) {
      response.status(200).json({
        status: 0,
        message: 'No FCM tokens available',
        error: 'User has no registered FCM tokens',
      });
      return;
    }

    const payload = {
      _id: sessionData.user_id,
      notification: {
        title: 'Invite Sent',
        body: 'Invite Sent Successfully',
      },
      data: {
        meeting_id: sessionData._id.toString(),
        type: 'invite_sent',
        user_id: sessionData.user_id,
        organization: sessionData.organization_id,
      },
      tokens,
    };
    notificationModule.sendNotification(payload);
    response.status(200).send({ status: 1, data: 'Invite Sent Successfully' });
  } catch (error) {
    response.status(500).send({ status: 0, message: error });
  }

}

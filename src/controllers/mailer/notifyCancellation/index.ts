import { LeadsModule } from '../../../modules/leads/index';
import { MailerModule } from '../../../modules/mailer';
import { OrganizationModule } from '../../../modules/organization';
import { UserModule } from '../../../modules/user';
import { Request, Response } from 'express';
import { mailUserData } from '../../../types/mailer';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
export default async function notifyCancellation (
  request: Request,
  response: Response,
):Promise<void> {
  try {
    const mailer = new MailerModule();
    const session_id = request.body.session_id;
    const sessionModule = new SessionModule();
    const sessionData = await sessionModule.getSessionById(session_id);

    if (!sessionData) {
      response.status(404).send({ status: 0, message: 'Session Data not found' });
      return;
    }
    const leadsModule = new LeadsModule(sessionData.organization_id);
    const leadsData = await leadsModule.GetLeadsBySessionId(session_id);
    if (!leadsData) {
      response.status(404).send({ status: 0, message: 'No Leads found for this session' });
      return;
    }

    const organizationModule = new OrganizationModule();
    const organizationData = await organizationModule.GetOrganization(sessionData.organization_id);

    const userModule = new UserModule();
    const username = await userModule.getUsername(sessionData.user_id);
    const userEmail = await userModule.getUserEmail(sessionData.user_id);
    if (!username || !userEmail) {
      response.status(404).send({ status: 0, message: 'User Data not found' });
      return;
    }
    const userData: mailUserData = {
      username: username,
      useremail: userEmail,
    };
    const hostReminderId = await mailer.notifyCancellationHost(sessionData, userData, organizationData);
    const guestReminderId = await mailer.notifyCancellationGuest(sessionData, leadsData, organizationData, userData);
    if ((hostReminderId && guestReminderId) || (hostReminderId && leadsData.length===0)) {
      response.status(200).send({ status: 1, data: 'Reminder Sent Successfully' });
    }
  } catch (error) {
    logger.error('Error in notifyCancellation', {message: error});
    response.status(500).send({ status: 0, message: error });
  }

}

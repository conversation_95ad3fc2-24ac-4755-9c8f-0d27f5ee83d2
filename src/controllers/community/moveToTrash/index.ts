import { ExtendedRequest } from '../../../types/extras';
import { communityModule, invalidateCommunityAPIs } from '../../../modules/community';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const community = new communityModule(project_id, organization_id);
  const community_id = request.body.community_id;
  const timeStamp = request.body.timeStamp;

  await community
    .moveToTrash(community_id, project_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });

      invalidateCommunityAPIs(organization_id, project_id).then((res) => {
        logger.info('Community APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating community APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving project communitys to trash: '+ error });
    });
}

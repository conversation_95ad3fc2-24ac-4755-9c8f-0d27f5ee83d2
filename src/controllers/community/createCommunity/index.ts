import { Response } from 'express';
import { community } from '../../../types/community';
import { communityModule, invalidateCommunityAPIs } from '../../../modules/community';
import mongoose from 'mongoose';
import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { FileRequest } from '../../../types/extras';

export async function createCommunity (
  request: FileRequest,
  response: Response,
): Promise<community | void> {
  const { name, project_id, category } = request.body;
  const organization_id = request.organization_id as string;
  if (!organization_id) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  const communityMod = new communityModule(project_id, organization_id);
  const requestFiles = request.files;
  console.log(requestFiles);
  const _id = new mongoose.Types.ObjectId();
  if (requestFiles) {
    const urlObject = await UploadUnitplanFiles(requestFiles, communityMod.storagepath+_id);
    console.log(urlObject);
    communityMod
      .CreateCommunity({
        _id: _id,
        name: name,
        project_id: project_id,
        thumbnail: urlObject.thumbnail,
        category: category,
        updated_at: new Date().toISOString(),
      })
      .then((communityData) => {
        response.status(201).json({ status: 1, data: communityData });

        invalidateCommunityAPIs(organization_id, project_id).then((res) => {
          logger.info('Community APIs invalidated successfully', { result: res });
        }).catch((err) => {
          logger.error('Error invalidating community APIs', { error: err });
        });
      })
      .catch((error: Error) => {
        logger.error('Internal Server Error', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error while creating the community' });
        console.error(error);
      });
  } else {
    communityMod
      .CreateCommunity({
        _id: _id,
        name: name,
        project_id: project_id,
        category: category,
        updated_at: new Date().toISOString(),
      })
      .then((communityData) => {
        response.status(201).json({ status: 1, data: communityData });

        invalidateCommunityAPIs(organization_id, project_id).then((res) => {
          logger.info('Community APIs invalidated successfully', { result: res });
        }).catch((err) => {
          logger.error('Error invalidating community APIs', { error: err });
        });
      })
      .catch((error: Error) => {
        logger.error('Internal Server Error', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error while creating the community' });
        console.error(error);
      });
  }
}

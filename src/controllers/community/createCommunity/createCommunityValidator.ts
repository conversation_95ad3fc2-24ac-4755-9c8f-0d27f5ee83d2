import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import logger from '../../../config/logger';
import { CommunityType } from '../../../types/community';
interface UploadedFiles {
  thumbnail: Express.Multer.File;
}

const CreateCommunityValidator = async  (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const files = req.files as UploadedFiles | undefined;
  if (!files || !files.thumbnail) {
    res
      .status(400)
      .json({ error: 'Community Image File is required.' });
  } else {
    const requiredTextFields = [
      'project_id',
      'name',
      'category',
    ];

    const missingTextFields = requiredTextFields.filter(
      (field) => !(field in req.body),
    );

    if (missingTextFields.length > 0) {
      res.status(400).json({
        error: `Missing text fields: ${missingTextFields.join(', ')}`,
      });
    } else {
      await body(
        'category',
        'Invalid type value. Please ensure that you are using a valid type value',
      )
        .isIn(Object.values(CommunityType))
        .run(req);
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        console.log(errors);
        logger.error(errors);

        res
          .status(400)
          .json({ error: errors });
      } else {
        next();
      }
    }
  }
};
export default CreateCommunityValidator;

import { validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import logger from '../../../config/logger';
import { CommunityType } from '../../../types/community';

const UpdateCommunityValidator = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  // List of required fields
  const requiredTextFields = ['project_id', 'community_id'];

  // Check for missing required fields
  const missingTextFields = requiredTextFields.filter((field) => !(field in req.body));

  if (missingTextFields.length > 0) {
    const errorMessage = `Missing text fields: ${missingTextFields.join(', ')}`;
    logger.error(errorMessage);
    res.status(400).json({ error: errorMessage });  // Return to stop further execution
  }

  // Validate the 'category' field
  if (req.body.category && !Object.values(CommunityType).includes(req.body.category)) {
    res.status(400).json({ error:
        'Invalid category value. Please make sure to send a valid category value.' });
    // Return to stop further execution
  }

  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.error('Validation errors:', { message: errors.array() });
    res.status(400).json({ errors: errors.array() });  // Return to stop further execution
  }

  // Proceed to the next middleware or route handler if everything is valid
  next();
};

export default UpdateCommunityValidator;

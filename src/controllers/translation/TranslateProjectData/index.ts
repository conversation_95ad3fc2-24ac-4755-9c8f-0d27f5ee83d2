import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import { staticKeywords } from '../../../datahelpers/translationData/static';
import { enumsToTranslate } from '../../../datahelpers/translationData/dynamic';
import { SupportedLanguages } from '../../../types/translation';
import logger from '../../../config/logger';
import { ProjectModule } from '../../../modules/projects';

export async function TranslateProjectData (request: ExtendedRequest, response: Response): Promise<void> {
  try {
    const organization_id = request.organization_id as string;
    const { project_id } = request.query;
    const projectModule = new ProjectModule(organization_id as string);
    const project = await projectModule.getProjectById(project_id  as string);
    const projectData = await projectModule.getProjectTranslationData(project_id as string);
    // Initialize the translation array
    const translationArray = [
      ...projectData,
    ];
    const supportedLanguages = project?.projectSettings?.ale?.supported_languages ?? [];

    const supportedLanguagesEnum: SupportedLanguages[] = supportedLanguages.map((lang) => {
      if (Object.values(SupportedLanguages).includes(lang as SupportedLanguages)) {
        return lang as SupportedLanguages;
      }
      throw new Error(`Unsupported language code: ${lang}`);
    });
    console.log(supportedLanguagesEnum);

    // Destructure into primaryLanguage and remainingLanguages
    const [primaryLanguage, ...remainingLanguages] = supportedLanguagesEnum;
    console.log(primaryLanguage, remainingLanguages);
    const sourceLanguageCode: SupportedLanguages = SupportedLanguages.EN;
    const translationModule = new TranslationModule(organization_id);
    const enumValues: string[] = enumsToTranslate.flatMap(({ enumType }) =>
      Object.values(enumType)
        .filter((value) => typeof value === 'string' && value !== null
        && value !== undefined && value !== '') // Ensure only string values are included
        .map((value) => (value)), // Incorrect mapping
    );
    const dataToTranslate = [
      ...staticKeywords,
      ...enumValues,
      ...translationArray,
    ];

    // Translate all data
    await Promise.all(
      dataToTranslate.map(async (text) => {
        await translationModule.Translate(sourceLanguageCode, primaryLanguage, text);
      }),
    );
    for (const targetLanguage of remainingLanguages) {
      try {
        if (targetLanguage !== primaryLanguage) {
          // Call the AddNewLanguage method for each remaining language
          await translationModule.AddNewLanguage(targetLanguage);

        }
      } catch (error) {
        // Log the error specific to the current target language
        logger.error('Error adding translation for language', {
          targetLanguage,
          error: error,
        });
      }
    }
    response.status(200).send({ status: 1, message: 'Project Data translated successfully' });
  } catch (error) {
    logger.error('Error in TranslateProjectData', error);
    response.status(500).send(error);
  }
}

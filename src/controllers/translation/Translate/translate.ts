import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import { SupportedLanguages } from '../../../types/translation';
import logger from '../../../config/logger';
export async function Translate (request: ExtendedRequest, response: Response):Promise<void>{

  const { sourceLanguageCode, targetLanguageCode, text} = request.query;
  const organization_id = request.organization_id as string;
  const translations = new TranslationModule(organization_id);
  if (sourceLanguageCode && targetLanguageCode && text){
    const translated = await translations.Translate(sourceLanguageCode as SupportedLanguages,
        targetLanguageCode as SupportedLanguages,
        text as string);
    response.status(200).send({ status: 1, data: translated });
  } else {
    logger.error('Error in request - Translate', sourceLanguageCode, targetLanguageCode, text);
    response.status(500).send('Error in request - Translate');
  }
}

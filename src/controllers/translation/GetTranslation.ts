import { Response } from 'express';
import { ExtendedRequest } from '../../types/extras';
import { TranslationModule } from '../../modules/translation';
import logger from '../../config/logger';

export async function GetTranslation (request: ExtendedRequest, response: Response):Promise<void>{

  const { text } = request.query;
  const organization_id = request.organization_id as string;
  const translations = new TranslationModule(organization_id);
  try {
    const translated = await translations.GetTranslation(text as string);
    response.status(200).send({ status: 1, data: translated });
  } catch (error) {
    logger.error('Error getting translation', {error});
    response.status(500).json({ message: 'Error getting translation', error: error });
  }
}

import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import { SupportedLanguages } from '../../../types/translation';
import logger from '../../../config/logger';

export async function UpdateTranslationById (request: ExtendedRequest, response: Response):Promise<void>{
  const organization_id = request.organization_id as string;
  const { translationId, targetLanguageCode, translation } = request.body;
  const translations = new TranslationModule(organization_id);
  if (targetLanguageCode){
    const translated = await translations.UpdateTranslationById(translationId,
        targetLanguageCode as SupportedLanguages,
        translation);
    response.status(200).send({ status: 1, data: translated });
  } else {
    logger.error('Error getting targetLanguageCode', targetLanguageCode);
    response.status(500).send('Error getting targetLanguageCode');
  }
}

import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import logger from '../../../config/logger';
import { SupportedLanguages } from '../../../types/translation';

export async function AddTranslation (request: ExtendedRequest, response: Response): Promise<void> {
  const translations = request.body.translations;
  const organization_id = request.organization_id as string;
  const { sourceLanguageCode, text } = request.query;

  try {
    const translations_module = new TranslationModule(organization_id);

    const storedTranslation = await translations_module
      .AddTranslation(translations, sourceLanguageCode as SupportedLanguages, text as string);

    response.status(200).send({
      status: 1,
      data: storedTranslation,
    });
  } catch (error) {
    logger.error('Translation store error', error);
    console.log(error);
    response.status(500).send({
      status: 0,
      message: 'Error storing translation',
    });
  }
}

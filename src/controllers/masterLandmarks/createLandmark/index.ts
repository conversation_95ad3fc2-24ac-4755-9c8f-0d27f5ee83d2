import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { masterLandmarkModule, invalidateMasterLandmarkAPIs } from '../../../modules/masterLandmark';
import { Types } from 'mongoose';
// Import multer from 'multer';
// Import fs from 'fs';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';

export default async function createLandmark (
  request: FileRequest,
  response: Response,
):Promise<void> {
  interface createLandmark {
    organization_id: string;
    name: string;
    distance: number;
    category: string;
    walk_timing: number;
    transit_timing: number;
    car_timing: number;
    description?: string;
  }
  const _id = new Types.ObjectId();

  const reqbody: createLandmark = request.body;
  const organization_id = request.headers.organization as string;
  const masterLandmark = new masterLandmarkModule(organization_id);
  if (!request.files) {
    // Handle Multer errors
    response.send({ status: 0, message: 'files not found' });
    return;
  }
  const thumbnailurl = await UploadUnitplanFiles(request.files, masterLandmark.storagepath+_id);
  masterLandmark
    .createLandmark({
      _id: _id,
      name: reqbody.name,
      distance: reqbody.distance,
      category: reqbody.category,
      walk_timing: reqbody.walk_timing,
      car_timing: reqbody.car_timing,
      transit_timing: reqbody.transit_timing,
      thumbnail: thumbnailurl.thumbnail,
      description: reqbody.description,
    })
    .then(async (res) => {
      response.send({ status: 1, data: res });

      // Invalidate master landmark APIs (non-blocking)
      invalidateMasterLandmarkAPIs(organization_id).then((invalidateRes) => {
        logger.info('Master landmark APIs invalidated successfully', { result: invalidateRes });
      }).catch((err) => {
        logger.error('Error invalidating master landmark APIs', { error: err });
      });
      // Await fs.promises.rm('masterLandmarks/', { recursive: true });
      return;
    })
    .catch((error) => {
      logger.error('Error in createLandMark', {message: error});
      response.send({ status: 0, message: error });
      return;
    });

  // Const upload = multer({ dest: 'masterLandmarks/' }).single('thumbnail');
  // Upload(request, response, async (err) => {

  // });
}

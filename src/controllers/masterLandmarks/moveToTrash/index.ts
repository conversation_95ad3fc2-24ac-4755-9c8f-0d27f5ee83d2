import { ExtendedRequest } from '../../../types/extras';
import { masterLandmarkModule, invalidateMasterLandmarkAPIs } from '../../../modules/masterLandmark';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const landmark = new masterLandmarkModule(organization_id);
  const landmark_id = request.body.landmark_id;
  const timeStamp = request.body.timeStamp;

  await landmark
    .moveToTrash(landmark_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });

      invalidateMasterLandmarkAPIs(organization_id).then((res) => {
        logger.info('Master landmark APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating master landmark APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving master landmarks to trash: '+ error });
    });
}

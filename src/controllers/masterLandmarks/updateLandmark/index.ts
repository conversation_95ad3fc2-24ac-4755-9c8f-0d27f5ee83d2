import {Response} from 'express';
import { FileRequest } from '../../../types/extras';
import { masterLandmarkModule, invalidateMasterLandmarkAPIs } from '../../../modules/masterLandmark';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';

export default async function updateLandmark (request:FileRequest, response:Response):Promise<void>{
  const organization_id = request.headers.organization as string;
  const landmark = new masterLandmarkModule(organization_id);
  const reqbody = request.body;

  // Handle file upload if thumbnail is provided
  const requestFiles = request.files as { [fieldname: string]: Express.Multer.File[] } | undefined;
  let thumbnailUrl: string | undefined;
  if (requestFiles && requestFiles.thumbnail) {
    try {
      const urlObject = await UploadUnitplanFiles(requestFiles, landmark.storagepath + reqbody.landmark_id);
      thumbnailUrl = urlObject.thumbnail;
    } catch (uploadError) {
      logger.error('Error uploading thumbnail in updateLandmark', {message: uploadError});
      response.send({status: 0, message: 'Error uploading thumbnail'});
      return;
    }
  }

  // Prepare update payload
  const updatePayload: any = {
    landmark_id: reqbody.landmark_id,
    ...(reqbody.name && { name: reqbody.name }),
    ...(reqbody.distance !== undefined && { distance: reqbody.distance }),
    ...(reqbody.category && { category: reqbody.category }),
    ...(reqbody.walk_timing !== undefined && { walk_timing: reqbody.walk_timing }),
    ...(reqbody.transit_timing !== undefined && { transit_timing: reqbody.transit_timing }),
    ...(reqbody.car_timing !== undefined && { car_timing: reqbody.car_timing }),
    ...(reqbody.description !== undefined && { description: reqbody.description }),
    ...(thumbnailUrl && { thumbnail: thumbnailUrl }),
  };

  landmark.updateLandmark(updatePayload).then((res) => {
    response.send({status: 1, data: res});

    invalidateMasterLandmarkAPIs(organization_id).then((invalidationResult) => {
      logger.info('Master landmark APIs invalidated successfully', { result: invalidationResult });
    }).catch((invalidationError) => {
      logger.error('Error invalidating master landmark APIs', { error: invalidationError });
    });
  })
    .catch((err) => {
      logger.error('Error in updateLandmark', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

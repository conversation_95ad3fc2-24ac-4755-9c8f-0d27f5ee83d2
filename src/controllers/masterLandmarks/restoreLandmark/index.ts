import { ExtendedRequest } from '../../../types/extras';
import { masterLandmarkModule, invalidateMasterLandmarkAPIs } from '../../../modules/masterLandmark';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreLandmark (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const landmark = new masterLandmarkModule(organization_id);
  const trash_id = request.body.trash_id;
  console.log('trash_id', trash_id);
  await landmark
    .restoreLandmark(organization_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Landmark got restored'});

      invalidateMasterLandmarkAPIs(organization_id).then((res) => {
        logger.info('Master landmark APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating master landmark APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in restoreLandmark', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while restoring Landmark: '+ error });
    });
}

import { Response } from 'express';
import { SidebarModule } from '../../../modules/sidebar';
import { ExtendedRequest } from './../../../types/extras';
import logger from '../../../config/logger';

export async function deleteOption (request: ExtendedRequest, response: Response): Promise<void> {
  const project_id = request.body.project_id;
  const customTourMod = new SidebarModule(project_id);
  customTourMod.deleteOption(request.body.option_id).then((message) => {
    response.status(200).json({status: 1, data: message});
  })
    .catch((err) => {
      logger.error('Error in deleteOption', {message: err});
      response.status(404).json({status: 0, error: err});
    });
}

import logger from '../../../config/logger';
import { SidebarModule, invalidateSidebarAPIs } from '../../../modules/sidebar';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export async function updateOptions (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.organization_id as string;
  const sidebarMod = new SidebarModule(project_id);
  sidebarMod.updateOptions(request.body)
    .then((res) => {
      response.status(201).json({ status: 1, data: res });

      // Invalidate sidebar APIs (non-blocking)
      invalidateSidebarAPIs(organization_id, project_id).then((invalidateRes) => {
        logger.info('Sidebar APIs invalidated successfully', { result: invalidateRes });
      }).catch((err) => {
        logger.error('Error invalidating sidebar APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error while updating sidebar option', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while updating sidebar option'+ error });
    });
}

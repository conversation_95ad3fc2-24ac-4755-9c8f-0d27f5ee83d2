import { ExtendedRequest } from '../../../types/extras';
import { SidebarModule } from '../../../modules/sidebar';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const sidebar = new SidebarModule(project_id);
  const sidebar_id = request.body.sidebar_id;
  const timeStamp = request.body.timeStamp;

  await sidebar
    .moveToTrash(sidebar_id, project_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving sidebar to trash: '+ error });
    });
}

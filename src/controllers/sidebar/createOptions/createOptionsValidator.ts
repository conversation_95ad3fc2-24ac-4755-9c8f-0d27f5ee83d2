import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { Type } from '../../../types/sidebar';

const createOptionsValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('name', 'Name is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('type', 'Type is required').notEmpty(),
  body('type', 'Invalid type value. Please ensure that you are using a valid type value').isIn(Object.values(Type)),
  body('scene_id').custom((value, { req }) => {
    if (req.body.type !== 'gallery' && !value && req.body.type !== 'unitplan'
      && req.body.type !== 'inventory' &&  req.body.type !== 'amenity' &&  req.body.type !== 'map' && !value && req.body.type !== 'custom') {
      throw new Error('Scene ID is required');
    }
    return true;
  }),
  body('icon_id', 'Icon is required').notEmpty(),
  body('link').custom((value, { req }) => {
    if (req.body.type === 'custom' && !req.body.scene_id && !value) {
      throw new Error('Link is required when type is custom and scene_id is not provided');
    }
    return true;
  }),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createOptionsValidate;

import mongoose from 'mongoose';
import { SidebarModule, invalidateSidebarAPIs } from '../../../modules/sidebar';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { Type, sidebarType } from '../../../types/sidebar';
import logger from '../../../config/logger';
export async function createOptions (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const project_id = request.body.project_id as string;
  const sidebarMod = new SidebarModule(project_id);
  const randomId = new mongoose.Types.ObjectId();
  if (request.body.type  !== Type.CUSTOM) {
    const sidebarOption : sidebarType = {
      _id: randomId,
      type: request.body.type,
      name: request.body.name,
      scene_id: request.body.scene_id,
      icon_id: request.body.icon_id,
      organization_id: organization_id,
    };
    sidebarMod.createOptions(sidebarOption)
      .then((res) => {
        response.status(201).json({ status: 1, data: res });

        // Invalidate sidebar APIs (non-blocking)
        invalidateSidebarAPIs(organization_id, project_id).then((invalidateRes) => {
          logger.info('Sidebar APIs invalidated successfully', { result: invalidateRes });
        }).catch((err) => {
          logger.error('Error invalidating sidebar APIs', { error: err });
        });
      })
      .catch((error: Error) => {
        logger.error('Error while creating sidebar option', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error while creating sidebar option'+ error });
      });
  } else {
    const sidebarOption : sidebarType = {
      _id: randomId,
      type: request.body.type,
      name: request.body.name,
      icon_id: request.body.icon_id,
      organization_id: organization_id,
      link: request.body.link,
    };
    sidebarMod.createOptions(sidebarOption)
      .then((res) => {
        response.status(201).json({ status: 1, data: res });

        // Invalidate sidebar APIs (non-blocking)
        invalidateSidebarAPIs(organization_id, project_id).then((invalidateRes) => {
          logger.info('Sidebar APIs invalidated successfully', { result: invalidateRes });
        }).catch((err) => {
          logger.error('Error invalidating sidebar APIs', { error: err });
        });
      })
      .catch((error: Error) => {
        logger.error('Error while creating sidebar option', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error while creating sidebar option'+ error });
      });
  }
}

import logger from '../../../config/logger';
import { AmenityModule, invalidateAmenityAPIs } from '../../../modules/amenity';
import { Amenity } from '../../../types/amenity';
import { Request, Response } from 'express';
export async function DeleteAmenity (
  request: Request,
  response: Response,
): Promise<Amenity | void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const amenity = new AmenityModule(project_id, organization_id);
  const amenity_id = request.body.amenity_id;
  await amenity
    .DeleteAmenity(amenity_id)
    .then((amenityData) => {
      response.status(201).json({ status: 1, data: amenityData });

      invalidateAmenityAPIs(organization_id, project_id).then((res) => {
        logger.info('Amenity APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating amenity APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error while deleting the amenity:', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting the amenity'+ error });
    });
}

import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { AmenityModule, invalidateAmenityAPIs } from '../../../modules/amenity';
import { Amenity } from '../../../types/amenity';
import { Request, Response } from 'express';
import mongoose from 'mongoose';
export async function CreateAmenity (
  request: Request,
  response: Response,
): Promise<Amenity | void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const amenity = new AmenityModule(project_id, organization_id);
  const requestFiles = request.files;
  const id = new mongoose.Types.ObjectId();
  // If(!organization_id)
  // {
  //   Response.status(400).send({status: 0, error: 'Organization header is missing.'});
  //   Return;
  // }
  if (requestFiles) {
    UploadUnitplanFiles(requestFiles, amenity.storagepath+id)
      .then((urlObject: { [key: string]: string }) => {
        const createAmenityData = {
          name: request.body.name,
          project_id: request.body.project_id,
          category: request.body.category,
          community_id: request.body.community_id,
          thumbnail: urlObject.thumbnail,
          description: request.body.description,
          media_type: request.body.media_type,
          tour_id: request.body.tour_id,
          embed_link: request.body.embed_link,
          file: urlObject.file,
          id: id,
        };
        amenity
          .CreateAmenity(createAmenityData)
          .then((amenityData) => {
            response.status(201).json({ status: 1, data: amenityData });

            invalidateAmenityAPIs(organization_id, project_id).then((res) => {
              logger.info('Amenity APIs invalidated successfully', { result: res });
            }).catch((err) => {
              logger.error('Error invalidating amenity APIs', { error: err });
            });
          })
          .catch((error: Error) => {
            logger.error('Error while creating the amenity', {message: error});
            response
              .status(500)
              .json({ status: 0, error: 'Error while creating the amenity'+ error });
          });
      }).catch((error) => {
        logger.error('Error uploading thumbnail', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error uploading thumbnail'+ error });
      });
  }
}

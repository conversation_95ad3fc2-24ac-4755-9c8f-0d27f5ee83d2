import { body, header, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const UpdateLinkValidator = [
  header('organization', 'Organization is required').notEmpty().isString(),
  header('accesstoken', 'Access Token is required').notEmpty().isString(),

  body('project_id', 'Project ID is required and must be a string').notEmpty().isString(),
  body('amenity_id', 'Tour ID is required and must be a string').notEmpty().isString(),
  body('link_id', 'Link ID is required and must be a string').notEmpty().isString(),

  body('position').optional().isObject().withMessage('Position must be an object'),
  body('position.x').optional().isString().withMessage('Position X coordinate must be a string'),
  body('position.y').optional().isString().withMessage('Position Y coordinate must be a string'),
  body('position.z').optional().isString().withMessage('Position Z coordinate must be a string'),

  body('text').optional().isString().withMessage('Text must be a string'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default UpdateLinkValidator;

import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import { Link } from '../../../types/amenity';
import { AmenityModule, invalidateAmenityAPIs } from '../../../modules/amenity';

export async function UpdateLink (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const organization_id = req.headers.organization as string;
    const project_id = req.body.project_id as string;
    const amenity_id = req.body.amenity_id as string;
    const link_id = req.body.link_id as string;

    const updateData: Partial<Link> = {};

    if (req.body.position) {
      updateData.position = {
        x: req.body.position.x,
        y: req.body.position.y,
        z: req.body.position.z,
      };
    }
    if (req.body.text) {
      updateData.text = req.body.text;
    }
    if (req.body.destination_img_id) {
      updateData.destination_img_id = req.body.destination_img_id;
    }

    const amenity = new AmenityModule(project_id, organization_id);
    const updatedAmenity = await amenity.UpdateLink(amenity_id, link_id, updateData);

    res.status(200).json({
      status: 1,
      message: 'Link updated successfully',
      data: updatedAmenity,
    });

    invalidateAmenityAPIs(organization_id, project_id).then((result) => {
      logger.info('Amenity APIs invalidated successfully', { result });
    }).catch((error) => {
      logger.error('Error invalidating amenity APIs', { error });
    });
  } catch (error) {
    logger.error('Error in UpdateLink', { error });
    res.status(500).json({
      status: 0,
      error: `Error updating link: ${error instanceof Error ? error.message : error}`,
    });
  }
}

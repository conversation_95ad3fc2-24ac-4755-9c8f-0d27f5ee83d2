import { ExtendedRequest } from '../../../types/extras';
import { AmenityModule, invalidateAmenityAPIs } from '../../../modules/amenity';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreAmenity (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const { project_id } = request.params;
  const amenity = new AmenityModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await amenity
    .restoreAmenity(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Amenity got restored'});

      invalidateAmenityAPIs(organization_id, project_id).then((res) => {
        logger.info('Amenity APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating amenity APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in restoreAmenity', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Amenity : '+ error });
    });
}

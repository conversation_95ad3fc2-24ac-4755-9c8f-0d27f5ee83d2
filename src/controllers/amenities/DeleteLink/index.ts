import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import { AmenityModule, invalidateAmenityAPIs } from '../../../modules/amenity';

export async function DeleteLink (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const project_id = req.body.project_id as string;
    const organization_id = req.headers.organization as string;
    const amenity = new AmenityModule(project_id, organization_id);
    const amenity_id = req.body.amenity_id as string;
    const link_id = req.body.link_id as string;

    const updatedAmenity = await amenity.DeleteLink(amenity_id, link_id);

    res.status(201).json({
      status: 1,
      message: 'Link deleted successfully',
      data: updatedAmenity,
    });

    invalidateAmenityAPIs(organization_id, project_id).then((result) => {
      logger.info('Amenity APIs invalidated successfully', { result });
    }).catch((error) => {
      logger.error('Error invalidating amenity APIs', { error });
    });
  } catch (error) {
    logger.error('Error in DeleteLink', { error });
    res.status(500).json({
      status: 0,
      error: `Error deleting link: ${error instanceof Error ? error.message : error}`,
    });
  }
}

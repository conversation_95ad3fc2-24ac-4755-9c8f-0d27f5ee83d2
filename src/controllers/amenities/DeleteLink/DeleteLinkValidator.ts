import { body, header, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const DeleteLinkValidator = [
  header('organization', 'Organization is required').notEmpty().isString(),
  header('accesstoken', 'Access Token is required').notEmpty().isString(),

  body('project_id', 'Project ID is required and must be a string').notEmpty().isString(),
  body('amenity_id', 'Amenity ID is required and must be a string').notEmpty().isString(),
  body('link_id', 'Link ID is required and must be a string').notEmpty().isString(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default DeleteLinkValidator;

// Import { Response } from 'express';
// Import { FileRequest } from '../../../types/extras';
// Import { AmenityModule } from '../../../modules/amenity';
// Import mongoose from 'mongoose';
// // Import multer from 'multer';
// Import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
// Import { MediaType } from '../../../types/amenity';
// Import logger from '../../../config/logger';

// Export async function CreateMedia (
//   Request: FileRequest,
//   Response: Response,
// ): Promise<object | void> {
//   Const organization_id = request.organization_id;
//   If (!organization_id) {
//     Response.send({ status: 0, error: 'Not authorized' });
//     Return;
//   }
//   Const project_id = request.body.project_id;
//   Const amenity_id = request.body.amenity_id;
//   Const amenity = new AmenityModule(project_id, organization_id);
//   If (!request.files && request.body.media_type !== MediaType.LINK) {
//     Response.send({ status: 0, error: 'file not found' });
//     Return;
//   }
//     If(request.files){
//     If (
//         Request.body.media_type !== MediaType.LINK && request.body.media_type !== MediaType.VIRTUAL_TOUR ) {
//     // const mediaUrl = await amenity.UploadFiles(amenity_id, request.files );
//       Const mediaUrl = await UploadUnitplanFiles(request.files, amenity.storagepath+amenity_id);
//       Console.log(mediaUrl);
//       Const media_id = new mongoose.Types.ObjectId();
//       Const mediaObj = {
//         _id: media_id,
//         Media_type: request.body.media_type,
//         Media_file: mediaUrl.media_file,
//       };
//       Amenity.CreateMedia(amenity_id, mediaObj).then((amenityData) => {
//         Response.status(201).json({ status: 1, data: amenityData });
//       }).catch((error) => {
//         Logger.error('Unable to create media:', {message: error});
//         Response.send({ status: 0, error: 'Unable to create media' + error });
//       });
//     } else {

//       Const media_id = new mongoose.Types.ObjectId();
//        Const mediaUrl = await UploadUnitplanFiles(request.files, amenity.storagepath+amenity_id);

//       Const mediaObj = {
//         _id: media_id,
//         Media_type: request.body.media_type,
//         Media_file:mediaUrl.media_file,
//         ...(request.body.media_type === MediaType.LINK && { link: request.body.link }),
//         ...(request.body.media_type === MediaType.VIRTUAL_TOUR && { tour_id: request.body.tour_id }),
//       };

//       Amenity.CreateMedia(amenity_id, mediaObj).then((amenityData) => {
//         Response.status(201).json({ status: 1, data: amenityData });
//       }).catch((error) => {
//         Logger.error('Unable to create media:', {message: error});
//         Response.send({ status: 0, error: 'Unable to create media' + error });
//       });
//     }
//   }
//   Const upload = multer({ dest: 'output/amenities/' }).single('file');
//   Upload(request, response, async (err) => {

//   });

// }

import { check, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { MediaType } from '../../../types/amenity';
import logger from '../../../config/logger';
interface UploadedFiles {
  media_file: Express.Multer.File[];
}

const CreateMediaValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.body.media_type);
  console.log(req.files);

  const files = req.files as UploadedFiles | undefined;
  if (req.body.media_type === MediaType.LINK  || req.body.media_type === MediaType.VIRTUAL_TOUR) {
    const requiredTextFields = ['project_id', 'amenity_id', 'media_type'];
    req.body.media_type === MediaType.LINK ? requiredTextFields.push('link'):requiredTextFields.push('tour_id');
    const missingTextFields = requiredTextFields.filter(
      (field) => !(field in req.body),
    );

    if (missingTextFields.length > 0) {
      res.status(400).json({
        error: `Missing text fields: ${missingTextFields.join(', ')}`,
      });
    } else {
      check('media_type', 'Invalid media type value. Please ensure that you are using a valid type value')
        .isIn(Object.values(MediaType)).run(req);
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        logger.error('Error:', {message: errors.array()});
        res.status(400).json({ error: errors.array() });
      } else {
        next();
      }
    }
  } else {
    if (!files || !files.media_file) {
      res
        .status(400)
        .json({ error: 'Amenity Media File is required.' });
    } else {
      const requiredTextFields = [
        'project_id',
        'amenity_id',
        'media_type',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        logger.error(`Missing text fields: ${missingTextFields.join(', ')}`);
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        check('media_type', 'Invalid media type value. Please ensure that you are using a valid type value')
          .isIn(Object.values(MediaType)).run(req);
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          logger.error('Error:', {message: errors});
        } else {
          next();
        }
      }
    }
  }
};
export default CreateMediaValidate;

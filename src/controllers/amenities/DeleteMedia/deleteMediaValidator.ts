import { Request, Response, NextFunction } from 'express';
import { validationResult, check, header } from 'express-validator';
import logger from '../../../config/logger';

const DeleteMediaValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  check('project_id', 'Project ID  is required').notEmpty(),
  check('amenity_id', 'Amenity ID  is required').notEmpty(),
  check('media_id', 'Media ID  is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error:', {message: errors.array() });
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default DeleteMediaValidate;

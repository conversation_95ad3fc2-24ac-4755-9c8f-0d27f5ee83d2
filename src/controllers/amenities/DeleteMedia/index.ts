import logger from '../../../config/logger';
import { AmenityModule, invalidateAmenityAPIs } from '../../../modules/amenity';
import { Amenity } from '../../../types/amenity';
import { Request, Response } from 'express';
export async function DeleteMedia (
  request: Request,
  response: Response,
): Promise<Amenity | void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const amenity_id = request.body.amenity_id;
  const media_id = request.body.media_id;
  const amenity = new AmenityModule(project_id, organization_id);
  await amenity
    .DeleteMedia(amenity_id, media_id)
    .then((deletedMedia) => {
      response.status(201).json({ status: 1, data: deletedMedia });

      invalidateAmenityAPIs(organization_id, project_id).then((res) => {
        logger.info('Amenity APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating amenity APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error while deleting the amenity media:', {message: error });
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting the amenity media'+ error });
    });
}

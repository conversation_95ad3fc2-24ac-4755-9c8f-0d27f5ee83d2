import { Request, Response, NextFunction } from 'express';
import { validationResult, param } from 'express-validator';
import logger from '../../../config/logger';

const AmenitySyncUpValidate = [
  param('project_id', 'Project ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error: ', {message: errors.array()});
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default AmenitySyncUpValidate;

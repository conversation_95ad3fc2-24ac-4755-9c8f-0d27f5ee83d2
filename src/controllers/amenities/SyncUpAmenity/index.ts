import logger from '../../../config/logger';
import { Request, Response } from 'express';
import { DataSyncUpModule } from '../../../modules/dataSyncUp';

export async function SyncUpAmenityData (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.params.project_id as string;
  const organization_id = request.headers.organization as string;

  const syncUp = new DataSyncUpModule();

  const result = await syncUp.AmenitySyncUp(organization_id, project_id);
  if (result) {
    response.status(200).json({ status: 1, message: result });
  } else {
    logger.error('Error in Sync Amenity', {message: result});
    response.status(400).json({ status: 0, error: result });
  }
}

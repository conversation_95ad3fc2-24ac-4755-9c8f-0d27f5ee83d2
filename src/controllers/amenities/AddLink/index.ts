import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import { AmenityModule, invalidateAmenityAPIs } from '../../../modules/amenity';

export async function AddLink (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const project_id = req.body.project_id as string;
    const organization_id = req.headers.organization as string;
    const amenity = new AmenityModule(project_id, organization_id);
    const amenity_id = req.body.amenity_id as string;
    const destination_img_id = req.body.destination_img_id as string;

    const linkData = {
      position: {
        x: req.body.position.x,
        y: req.body.position.y,
        z: req.body.position.z,
      },
      text: req.body.text,
      destination_img_id: destination_img_id,
    };

    const updatedAmenity = await amenity.AddLink(amenity_id, linkData);

    res.status(201).json({
      status: 1,
      message: 'Link added successfully',
      data: updatedAmenity,
    });

    invalidateAmenityAPIs(organization_id, project_id).then((result) => {
      logger.info('Amenity APIs invalidated successfully', { result });
    }).catch((error) => {
      logger.error('Error invalidating amenity APIs', { error });
    });
  } catch (error) {
    logger.error('Error in AddLink', { error });
    res.status(500).json({
      status: 0,
      error: `Error adding link: ${error instanceof Error ? error.message : error}`,
    });
  }
}

import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { AmenityModule, invalidateAmenityAPIs } from '../../../modules/amenity';
import { Request, Response } from 'express';

export async function UpdateAmenity (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  //   Pass organization_id in this ProjectModule
  const project = new AmenityModule(project_id, organization_id);
  const amenity_id = request.body.amenity_id as string;
  const requestFiles = request.files;
  if (requestFiles) {
    UploadUnitplanFiles(requestFiles, project.storagepath+amenity_id)
      .then((urlObject: { [key: string]: string }) => {
        const { ...updateFields } = request.body;
        updateFields.thumbnail = urlObject.thumbnail;
        updateFields.file = urlObject.file;
        project.UpdateAmenity(amenity_id, updateFields).then((amenityData) => {
          response.status(200).json({ status: 1, data: amenityData });

          invalidateAmenityAPIs(organization_id, project_id).then((res) => {
            logger.info('Amenity APIs invalidated successfully', { result: res });
          }).catch((err) => {
            logger.error('Error invalidating amenity APIs', { error: err });
          });
        }).catch((error) => {
          logger.error('Amenity not found ', {message: error});
          response.status(404).json({ status: 0, error: 'Amenity not found'+ error });
        });

      });
  } else {
    const { ...updateFields } = request.body;
    project.UpdateAmenity(amenity_id, updateFields).then((amenityData) => {
      response.status(200).json({ status: 1, data: amenityData });

      invalidateAmenityAPIs(organization_id, project_id).then((res) => {
        console.log(res);
      }).catch((err) => {
        console.log(err);
      });
    }).catch((error) => {
      logger.error('Amenity not found ', {message: error});
      response.status(404).json({ status: 0, error: 'Amenity not found'+ error });
    });
  }
}

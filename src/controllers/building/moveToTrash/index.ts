import { ExtendedRequest } from '../../../types/extras';
import { buildingModule, invalidateBuildingAPIs } from '../../../modules/building';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const building = new buildingModule(project_id, organization_id);
  const building_id = request.body.building_id;
  const timeStamp = request.body.timeStamp;

  await building
    .moveToTrash(building_id, project_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });

      invalidateBuildingAPIs(organization_id, project_id).then((res) => {
        logger.info('Building APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating building APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving project buildings to trash: '+ error });
    });
}

import { Response } from 'express';
import { buildingModule, invalidateBuildingAPIs } from '../../../modules/building';
import logger from '../../../config/logger';
import { FileRequest } from '../../../types/extras';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import mongoose from 'mongoose';
import { CreateBuildingType } from '../../../types/building';

export async function CreateBuilding (
  request: FileRequest,
  response: Response,
): Promise<CreateBuildingType | void> {
  const { name, type, total_floors, project_id, community_id} = request.body;
  const organization_id = request.organization_id as string;
  if (!organization_id) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }

  const building = new buildingModule(project_id, organization_id);
  const requestFiles = request.files;
  const _id = new mongoose.Types.ObjectId();

  const thumbnail = requestFiles
    ? (await UploadUnitplanFiles(requestFiles, building.storagepath + _id)).thumbnail
    : undefined;

  building
    .CreateNewBuilding({
      _id: _id,
      name: name,
      project_id: project_id,
      type: type,
      total_floors: total_floors,
      community_id: community_id,
      updated_at: '',
      ...(thumbnail && { thumbnail }),
    })
    .then((buildingData) => {
      response.status(201).json({ status: 1, data: buildingData });

      invalidateBuildingAPIs(organization_id, project_id).then((res) => {
        logger.info('Building APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating building APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Internal Server Error', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the building' });
      console.error(error);
    });
}

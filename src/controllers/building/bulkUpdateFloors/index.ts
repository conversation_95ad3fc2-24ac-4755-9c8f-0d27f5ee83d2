import logger from '../../../config/logger';
import { buildingModule, invalidateBuildingAPIs } from '../../../modules/building';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export default async function updateBulkAmenities (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const building_id = request.body.building_id as string;
  const organization_id = request.body.organization_id as string;
  const buildingMod = new buildingModule(project_id, organization_id);
  buildingMod.bulkUpdateFloors(request.body, building_id)
    .then((res) => {
      response.status(201).json({ status: 1, message: res });

      invalidateBuildingAPIs(organization_id, project_id).then((invalidationResult) => {
        logger.info('Building APIs invalidated successfully', { result: invalidationResult });
      }).catch((invalidationError) => {
        logger.error('Error invalidating building APIs', { error: invalidationError });
      });
    })
    .catch((error: Error) => {
      logger.error('Errors:', {message: error});
      response
        .status(500)
        .json({ status: 0, error: error});
    });
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import logger from '../../../config/logger';

const bulkUpdateFloorsValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('query', 'Query is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('building_id', 'Building ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Errors:', {message: errors.array()});
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default bulkUpdateFloorsValidate;

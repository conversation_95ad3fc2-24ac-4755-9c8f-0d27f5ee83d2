import { Response } from 'express';
import { buildingModule, invalidateBuildingAPIs } from '../../../modules/building';
import logger from '../../../config/logger';
import { FileRequest } from '../../../types/extras';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { updateBuildingType } from '../../../types/building';

export async function updateBuilding (
  request: FileRequest,
  response: Response,
): Promise<updateBuildingType | void> {
  const { building_id, name, project_id, type, community_id, total_floors } = request.body;
  const organization_id = request.organization_id as string;
  const building = new buildingModule(project_id, organization_id);
  const requestFiles = request.files;

  if (!organization_id) {
    response.status(401).json({ status: 0, error: 'Not authorized' });
    return;
  }

  if (!building_id) {
    response.status(400).json({ status: 0, error: 'No Building ID Found' });
    return;
  }

  try {
    const existingBuilding = await building.GetBuilding(building_id);

    if (!existingBuilding) {
      response.status(404).json({ status: 0, error: 'Building not found' });
      return;
    }

    const updatedData: updateBuildingType = {
      name: name || existingBuilding.name,
      project_id: project_id || existingBuilding.project_id,
      community_id: community_id || existingBuilding.community_id,
      type: type || existingBuilding.type,
      updated_at: new Date().toISOString(),
      total_floors: total_floors || existingBuilding.total_floors,
      floors: existingBuilding.floors,
    };

    if (requestFiles) {
      const urlObject = await UploadUnitplanFiles(requestFiles, building.storagepath + building_id);
      updatedData.thumbnail = urlObject.thumbnail || existingBuilding.thumbnail;
    }

    const updatedBuilding = await building.updateBuilding(building_id, updatedData);
    response.status(200).json({ status: 1, data: updatedBuilding });

    invalidateBuildingAPIs(organization_id, project_id).then((res) => {
      logger.info('Building APIs invalidated successfully', { result: res });
    }).catch((err) => {
      logger.error('Error invalidating building APIs', { error: err });
    });
  } catch (error) {
    logger.error('Internal Server Error', { message: error });
    response.status(500).json({ status: 0, error: 'Error while updating the building' });
    console.error(error);
  }
}

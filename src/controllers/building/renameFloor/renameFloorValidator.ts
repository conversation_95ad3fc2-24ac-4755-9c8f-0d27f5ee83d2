import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import logger from '../../../config/logger';

const renameFloorValidator = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('building_id', 'Building ID  is required').notEmpty(),
  body('floor_id', 'Floor ID  is required').notEmpty(),
  body('name', 'Name  is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error', {message: errors.array()});
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default renameFloorValidator;

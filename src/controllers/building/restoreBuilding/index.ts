import { ExtendedRequest } from '../../../types/extras';
import { buildingModule, invalidateBuildingAPIs } from '../../../modules/building';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreBuilding (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const building = new buildingModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await building
    .restoreBuilding(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Building got restored'});

      invalidateBuildingAPIs(organization_id, project_id).then((res) => {
        logger.info('Building APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating building APIs', { error: err });
      });
    })
    .catch((error: Error) => {
      logger.error('Error in restoreBuilding', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Building : '+ error });
    });
}

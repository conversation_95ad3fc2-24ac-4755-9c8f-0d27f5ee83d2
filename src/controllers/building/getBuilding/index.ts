import { Request, Response } from 'express';
import { buildingModule } from '../../../modules/building';
import logger from '../../../config/logger';

export async function GetBuilding (request: Request, response: Response): Promise<void> {
  try {
    const { building_id } = request.params;
    const {project_id} = request.body;
    const {organization_id} = request.body;
    const building = new buildingModule(project_id, organization_id);
    const buildingData = await building.GetBuilding(building_id);

    if (buildingData) {
      response.status(200).json({ status: 1, data: buildingData });
    } else {
      logger.error('Building not found');
      response.status(404).json({ status: 0, error: 'Building not found' });
    }
  } catch (error) {
    logger.error('Internal Server Error', {message: error});
    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

import {Response } from 'express';
import { buildingModule, invalidateBuildingAPIs } from '../../../modules/building';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';

export async function AddNewFloor (
  request:ExtendedRequest,
  response:Response,
):Promise<void>{
  const building_id = request.body.building_id;
  const floor_name = request.body.floor_name;
  const project_id= request.body.project_id;

  const organization_id = request.headers.organization as string;
  const building = new buildingModule(project_id, organization_id);
  building.AddNewFloor(building_id, floor_name).then((res) => {
    response.send({status: 1, data: res});

    invalidateBuildingAPIs(organization_id, project_id).then((invalidationResult) => {
      logger.info('Building APIs invalidated successfully', { result: invalidationResult });
    }).catch((invalidationError) => {
      logger.error('Error invalidating building APIs', { error: invalidationError });
    });
  })
    .catch((err) => {
      logger.error('Error', {message: err});
      response.status(400).send({status: 0, message: err});
    });
}

import { Request, Response } from 'express';
import { ProjectModule, invalidateProjectAPIs } from '../../../modules/projects';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
import { generateCustomFont, generateFontLink } from '../../../helpers/projects';
import { FontType } from '../../../types/projects';
export async function UpdateProjectSettings (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  //   Pass organization_id in this ProjectModule
  const project = new ProjectModule(organization_id);
  const { project_id, query, ...updateFields } = request.body;
  const updateObj = {
    ...updateFields,
  };
  const requestFiles = request.files;

  if (requestFiles && Object.keys(requestFiles).length) {
    UploadUnitplanFiles(requestFiles, project.storagepath+project_id)
      .then((urlObject: { [key: string]: string }) => {
        updateObj.welcome_video = urlObject.welcome_video,
        updateObj.welcome_thumbnail = urlObject.welcome_thumbnail;
        project.updateProjectSettings(project_id, query, updateObj).then((projectData) => {
          if (projectData) {
            response.status(200).json({ status: 1, data: projectData });

            invalidateProjectAPIs(organization_id, project_id).then((res) => {
              logger.info('Project APIs invalidated successfully', { result: res });
            }).catch((err) => {
              logger.error('Error invalidating project APIs', { error: err });
            });
          } else {
            logger.error('Project not found');
            response.status(404).json({ status: 0, error: 'Project not found' });
          }
        });
      });
  } else {
    if (query.theme && query.theme.font_type){
      const font_type = query.theme.font_type as FontType;
      if (font_type !== 'custom'){
        const fontLink =  await generateFontLink(font_type);
        updateObj.font_url = fontLink;
      }
    }

    if (query.theme && query.theme.font_url){
      const link = query.theme.font_url as string;
      const fontLink = await generateCustomFont(link);
      updateObj.font_url = fontLink;
    }

    project.updateProjectSettings(project_id, query, updateObj).then((projectData) => {
      if (projectData) {
        response.status(200).json({ status: 1, data: projectData });

        invalidateProjectAPIs(organization_id, project_id).then((res) => {
          logger.info('Project APIs invalidated successfully', { result: res });
        }).catch((err) => {
          logger.error('Error invalidating project APIs', { error: err });
        });
      } else {
        logger.error('Project not found');
        response.status(404).json({ status: 0, error: 'Project not found' });
      }
    });
  }
}

import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';

const GetProjectValidate = [
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
    }

    const hasAuthBearer = req.headers.authorization;
    const hasAccessToken = req.headers.accesstoken;
    const hasOrganization = req.headers.organization;

    if (!hasAuthBearer && (!hasAccessToken || !hasOrganization)) {
      res.status(401).json({
        message: 'Either a valid Authorization header or both Access Token and Organization ID are required' });
      return;
    }

    next(); // Proceed to the next middleware if either condition is met
  },
];

export default GetProjectValidate;

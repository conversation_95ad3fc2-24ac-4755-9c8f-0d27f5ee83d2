import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import { ProjectModule } from '../../../modules/projects';

/**
 * Controller for exporting project data
 * Handles HTTP request/response, delegates business logic to ProjectModule
 * Exports data as-is. Use ReplaceProjectId API to replace project IDs if needed.
 */
export async function ExportProject (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const { project_id } = request.params;
    const organization_id = request.organization_id as string;

    if (!project_id || !organization_id) {
      response.status(400).json({
        status: 0,
        error: 'Project ID and Organization ID are required',
      });
      return;
    }

    logger.info('ExportProject called', { project_id, organization_id });

    // Delegate to ProjectModule for business logic
    const projectModule = new ProjectModule(organization_id);
    const exportData = await projectModule.exportProjectData(project_id, organization_id);

    // Set response headers for JSON file download
    const fileName = `project_${project_id}_export_${Date.now()}.json`;
    response.setHeader('Content-Type', 'application/json');
    response.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

    // Send JSON response (data exported as-is)
    response.status(200).json({ status: 1, data: exportData });

    logger.info('ExportProject completed successfully', {
      project_id,
      organization_id,
      modules_exported: Object.keys(exportData.modules).length,
    });
  } catch (error) {
    logger.error('Error in ExportProject', {
      error,
      message: error instanceof Error ? error.message : 'Unknown error',
    });

    // Handle specific error cases
    if (error instanceof Error && error.message === 'Project not found') {
      response.status(404).json({
        status: 0,
        error: 'Project not found',
      });
      return;
    }

    response.status(500).json({
      status: 0,
      error: 'Failed to export project data',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

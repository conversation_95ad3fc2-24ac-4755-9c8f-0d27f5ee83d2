import { validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
// Import {ExperienceType, PropertyType, Theme } from '../../../types/projects';
// Interface UploadedFiles {
//   Project_thumbnail: Express.Multer.File[];
// }

const EditProjectValidate = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
//   Console.log(req.files);
//   Console.log(req.body);
  const requiredTextFields = [
    'id',
    // 'name',
    // 'property_type',
    // 'experience',
    // 'city',
    // 'country',
  ];
  const missingTextFields = requiredTextFields.filter(
    (field) => !(field in req.body),
  );

  if (missingTextFields.length > 0) {
    res.status(400).json({
      error: `Missing text fields: ${missingTextFields.join(', ')}`,
    });
  } else {
    // Await Promise.all([
    // Check('experience', 'Invalid project type value. Please ensure that you are using a valid type value')
    //   .isIn(Object.values(ExperienceType)).run(req),
    // Check('property_type', 'Invalid property type value. Please ensure that you are using a valid type value')
    //   .isIn(Object.values(PropertyType)).run(req),
    // ])
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();

  }

};

export default EditProjectValidate;

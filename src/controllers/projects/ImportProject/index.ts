import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import mongoose from 'mongoose';
import multer from 'multer';
import { ProjectModule } from '../../../modules/projects';
// Import { UnitModule } from '../../../modules/units';
import { buildingModule } from '../../../modules/building';
import { AmenityModule } from '../../../modules/amenity';
import { communityModule } from '../../../modules/community';
import { unitplanModule } from '../../../modules/unitplan';
import { AssetsModule } from '../../../modules/asset';
import { GalleryModule } from '../../../modules/gallery';
import { ModelModule } from '../../../modules/glbModel';
import { ProjectSceneModule } from '../../../modules/projectScene';
import { ProjectLandmarkModule } from '../../../modules/projectLandmark';
import { MiniMapModule } from '../../../modules/miniMap';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { customTourModule } from '../../../modules/customTour';
import { projectSceneType } from '../../../types/projectScene';
// Import { ProjectSVGModule } from '../../../modules/projectSVG';
import { Models } from '../../../types/extras';
import { projectsvgSchema } from '../../../schema/projectsvgSchema';
import { sidebarSchema } from '../../../schema/sidebarSchema';
import { unitSchema } from '../../../schema/UnitSchema';
// Import { AssetsSchema } from '../../../schema/assetsSchema';
// Import { gallerySchema } from '../../../schema/gallerySchema';
// Import { glbSchema } from '../../../schema/glbmodelSchema';
// Import { projectLandmarksSchema } from '../../../schema/projectLandmarksSchema';
// Import { amenityMiniMapSchema } from '../../../schema/miniMapSchema';
// Import { virtualTourSchema } from '../../../schema/virtualTourSchema';
// Import { customTourSchema } from '../../../schema/customTour';
// Import { CopyFirebaseItem } from '../../../helpers/moveStorageUpload';
import { admin, bucketName } from '../../../config/firebase';

/**
 * Multer configuration for JSON file upload
 * Uses memory storage to handle large JSON files (up to 100MB)
 */
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit for large JSON files
  },
  fileFilter: (_req, file, cb) => {
    if (file.mimetype === 'application/json' || file.originalname.endsWith('.json')) {
      cb(null, true);
    } else {
      cb(new Error('Only JSON files are allowed'));
    }
  },
}).single('file');

/**
 * Recursively replaces all occurrences of oldProjectId with newProjectId in the data
 * This function traverses the entire data structure and replaces any value (string or ObjectId)
 * that matches the oldProjectId with the newProjectId, while preserving organization_id
 * @param data - The data object to process
 * @param oldProjectId - The old project ID to replace
 * @param newProjectId - The new project ID to use
 * @param organizationId - The organization ID to preserve (never replace)
 * @returns The data with all project IDs replaced
 */
function replaceProjectIdInData (
  data: any,
  oldProjectId: string,
  newProjectId: string,
  organizationId: string,
): any {
  if (data === null || data === undefined) {
    return data;
  }

  // Handle arrays - recursively process each item
  if (Array.isArray(data)) {
    return data.map((item) => replaceProjectIdInData(item, oldProjectId, newProjectId, organizationId));
  }

  // Handle objects
  if (typeof data === 'object') {
    // Handle Mongoose ObjectId instances
    if (data instanceof mongoose.Types.ObjectId) {
      const idStr = data.toString();
      // Only replace if it matches the old project ID (not organization ID)
      return idStr === oldProjectId ? newProjectId : idStr;
    }

    const result: any = {};
    for (const [key, value] of Object.entries(data)) {
      // Handle null/undefined values
      if (value === null || value === undefined) {
        result[key] = value;
        continue;
      }

      // NEVER replace organization_id - preserve it as is
      if (key === 'organization_id' || key === 'source_organization_id') {
        result[key] = value;
        continue;
      }

      // Handle ObjectId values
      if (value instanceof mongoose.Types.ObjectId) {
        const idStr = value.toString();
        // Only replace if it's a project_id or _id field AND matches old project ID
        if ((key === 'project_id' || key === '_id') && idStr === oldProjectId) {
          result[key] = newProjectId;
        } else {
          result[key] = idStr;
        }
        continue;
      }

      // Handle string values - only replace in project_id or _id fields
      if (typeof value === 'string') {
        if (key === 'project_id' && value === oldProjectId) {
          // Always replace project_id fields
          result[key] = newProjectId;
        } else if (key === '_id' && value === oldProjectId) {
          // Replace _id if it matches the old project ID
          result[key] = newProjectId;
        } else if (key === 'source_project_id' && value === oldProjectId) {
          // Replace source_project_id in metadata
          result[key] = newProjectId;
        } else if (value === oldProjectId && key !== 'organization_id') {
          // Replace any other value that matches old project ID (but not organization_id)
          result[key] = newProjectId;
        } else {
          // Preserve all other string values, including organization_id
          result[key] = value;
        }
        continue;
      }

      // Handle arrays - recursively process
      if (Array.isArray(value)) {
        result[key] = replaceProjectIdInData(value, oldProjectId, newProjectId, organizationId);
        continue;
      }

      // Handle objects - recursively process
      if (typeof value === 'object') {
        result[key] = replaceProjectIdInData(value, oldProjectId, newProjectId, organizationId);
        continue;
      }

      // For all other types (numbers, booleans, etc.), keep as is
      result[key] = value;
    }
    return result;
  }

  // For primitive types (strings, numbers, booleans, etc.), return as is
  // We don't replace standalone strings to avoid accidentally replacing organization IDs
  return data;
}

/**
 * Extracts Firebase Storage path from a Firebase Storage URL
 */
function extractFirebasePath (url: string): string | null {
  if (!url || typeof url !== 'string') {
    return null;
  }

  if (url.includes('firebasestorage.googleapis.com')) {
    const match = url.match(/\/o\/(.+?)(\?|$)/);
    if (match) {
      try {
        return decodeURIComponent(match[1]);
      } catch (e) {
        // If decoding fails, return the raw path
        return match[1];
      }
    }
  }
  return null;
}

/**
 * Copies a directory recursively from Firebase Storage using native copy (much faster)
 * @param sourceDirPath - Source directory path in Firebase Storage
 * @param destDirPath - Destination directory path in Firebase Storage
 * @param urlMap - Map to track copied URLs
 */
async function copyFirebaseDirectory (
  sourceDirPath: string,
  destDirPath: string,
  urlMap: Map<string, string>,
): Promise<void> {
  try {
    const bucket = admin.storage().bucket(bucketName);

    // List all files in the source directory
    const [files] = await bucket.getFiles({ prefix: sourceDirPath });

    // Filter out directory markers
    const actualFiles = files.filter(
      (file) => file.name !== sourceDirPath && file.name !== `${sourceDirPath}/`,
    );

    if (actualFiles.length === 0) {
      logger.info('No files found in directory', { sourceDir: sourceDirPath });
      return;
    }

    logger.info('Copying directory files in parallel', {
      sourceDir: sourceDirPath,
      fileCount: actualFiles.length,
    });

    // Process files in parallel batches (10 at a time to avoid overwhelming Firebase)
    const batchSize = 10;
    for (let i = 0; i < actualFiles.length; i += batchSize) {
      const batch = actualFiles.slice(i, i + batchSize);

      await Promise.all(
        batch.map(async (file) => {
          try {
            // Get the relative path from the source directory
            const relativePath = file.name.substring(sourceDirPath.length);
            // Remove leading slash if present
            const cleanRelativePath = relativePath.startsWith('/') ? relativePath.substring(1) : relativePath;

            const destFilePath = `${destDirPath}/${cleanRelativePath}`;

            // Use Firebase's native copy method (much faster than download/upload)
            const destFile = bucket.file(destFilePath);
            await file.copy(destFile);

            // Generate URLs for both old and new locations
            const oldUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodeURIComponent(file.name)}?alt=media`;
            const newUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodeURIComponent(destFilePath)}?alt=media`;

            urlMap.set(oldUrl, newUrl);
          } catch (fileError) {
            logger.warn('Failed to copy file in directory', {
              file: file.name,
              error: fileError instanceof Error ? fileError.message : 'Unknown error',
            });
          }
        }),
      );

      logger.info('Copied batch of files', {
        batch: Math.floor(i / batchSize) + 1,
        totalBatches: Math.ceil(actualFiles.length / batchSize),
        progress: `${i + batch.length}/${actualFiles.length}`,
      });
    }

    logger.info('Completed copying directory', {
      sourceDir: sourceDirPath,
      destDir: destDirPath,
      totalFiles: actualFiles.length,
    });
  } catch (error) {
    logger.warn('Failed to copy Firebase directory', {
      sourceDir: sourceDirPath,
      destDir: destDirPath,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    // Continue processing even if directory copy fails
  }
}

/**
 * Fast copy function using Firebase Storage native copy (much faster than download/upload)
 */
async function copyFirebaseFileFast (
  sourcePath: string,
  destPath: string,
): Promise<string> {
  const bucket = admin.storage().bucket(bucketName);
  const sourceFile = bucket.file(sourcePath);
  const destFile = bucket.file(destPath);

  // Use Firebase's native copy method (much faster)
  await sourceFile.copy(destFile);

  // Generate the new URL
  const newUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodeURIComponent(destPath)}?alt=media`;
  return newUrl;
}

/**
 * Collects all Firebase URLs from data structure (non-recursive collection phase)
 */
function collectFirebaseUrls (
  data: any,
  oldProjectId: string,
  urlSet: Set<string> = new Set(),
): Set<string> {
  if (data === null || data === undefined) {
    return urlSet;
  }

  if (typeof data === 'string') {
    const firebasePath = extractFirebasePath(data);
    if (firebasePath && firebasePath.includes(oldProjectId) && !urlSet.has(data)) {
      urlSet.add(data);
    }
    return urlSet;
  }

  if (Array.isArray(data)) {
    for (const item of data) {
      collectFirebaseUrls(item, oldProjectId, urlSet);
    }
    return urlSet;
  }

  if (typeof data === 'object') {
    for (const value of Object.values(data)) {
      collectFirebaseUrls(value, oldProjectId, urlSet);
    }
    return urlSet;
  }

  return urlSet;
}

/**
 * Recursively finds and copies all Firebase Storage URLs to new project location
 * Handles .dzi files specially by also copying their associated _files directories
 * Returns a map of old URLs to new URLs
 * OPTIMIZED: Collects URLs first, then copies in parallel batches
 */
async function copyFirebaseFiles (
  data: any,
  oldProjectId: string,
  newProjectId: string,
  organizationId: string,
  urlMap: Map<string, string> = new Map(),
): Promise<Map<string, string>> {
  // Step 1: Collect all unique Firebase URLs first (fast, no I/O)
  logger.info('Step 1: Collecting Firebase URLs from data...');
  const urlSet = collectFirebaseUrls(data, oldProjectId);
  logger.info('Collected Firebase URLs', { count: urlSet.size });

  if (urlSet.size === 0) {
    return urlMap;
  }

  // Step 2: Process URLs in parallel batches
  const oldProjectPath = `CreationtoolAssets/${organizationId}/projects/${oldProjectId}`;
  const newProjectPath = `CreationtoolAssets/${organizationId}/projects/${newProjectId}`;

  const urlsToProcess: Array<{ url: string; path: string }> = [];
  const dziFiles: Array<{ url: string; path: string; newPath: string }> = [];

  // Prepare all URLs for processing
  for (const url of urlSet) {
    if (urlMap.has(url)) {
      continue;
    } // Already processed

    const firebasePath = extractFirebasePath(url);
    if (!firebasePath) {
      continue;
    }

    let relativePath = '';
    if (firebasePath.includes(oldProjectPath)) {
      const pathIndex = firebasePath.indexOf(oldProjectPath);
      relativePath = firebasePath.substring(pathIndex + oldProjectPath.length);
      if (relativePath.startsWith('/')) {
        relativePath = relativePath.substring(1);
      }
    } else if (firebasePath.includes(`projects/${oldProjectId}`)) {
      const pathIndex = firebasePath.indexOf(`projects/${oldProjectId}`);
      relativePath = firebasePath.substring(pathIndex + `projects/${oldProjectId}`.length);
      if (relativePath.startsWith('/')) {
        relativePath = relativePath.substring(1);
      }
    } else {
      const pathParts = firebasePath.split('/');
      const projectIndex = pathParts.findIndex((part) => part === oldProjectId);
      if (projectIndex >= 0 && projectIndex < pathParts.length - 1) {
        relativePath = pathParts.slice(projectIndex + 1).join('/');
      } else {
        relativePath = firebasePath.substring(firebasePath.lastIndexOf('/') + 1);
      }
    }

    const newDestinationPath = relativePath
      ? `${newProjectPath}/${relativePath}`
      : `${newProjectPath}/${firebasePath.substring(firebasePath.lastIndexOf('/') + 1)}`;

    urlsToProcess.push({ url, path: firebasePath });

    // Track .dzi files for special handling
    if (firebasePath.endsWith('.dzi')) {
      const dziBasePath = firebasePath.substring(0, firebasePath.length - 4);
      const dziFilesDir = `${dziBasePath}_files`;
      const newDziBasePath = newDestinationPath.substring(0, newDestinationPath.length - 4);
      const newDziFilesDir = `${newDziBasePath}_files`;
      dziFiles.push({ url, path: dziFilesDir, newPath: newDziFilesDir });
    }
  }

  logger.info('Prepared URLs for copying', {
    totalUrls: urlsToProcess.length,
    dziFiles: dziFiles.length,
  });

  // Step 3: Copy files in parallel batches (50 at a time for better throughput)
  const batchSize = 50;
  let processed = 0;

  for (let i = 0; i < urlsToProcess.length; i += batchSize) {
    const batch = urlsToProcess.slice(i, i + batchSize);

    await Promise.all(
      batch.map(async ({ url, path }) => {
        try {
          let relativePath = '';
          if (path.includes(oldProjectPath)) {
            const pathIndex = path.indexOf(oldProjectPath);
            relativePath = path.substring(pathIndex + oldProjectPath.length);
            if (relativePath.startsWith('/')) {
              relativePath = relativePath.substring(1);
            }
          } else if (path.includes(`projects/${oldProjectId}`)) {
            const pathIndex = path.indexOf(`projects/${oldProjectId}`);
            relativePath = path.substring(pathIndex + `projects/${oldProjectId}`.length);
            if (relativePath.startsWith('/')) {
              relativePath = relativePath.substring(1);
            }
          } else {
            const pathParts = path.split('/');
            const projectIndex = pathParts.findIndex((part) => part === oldProjectId);
            if (projectIndex >= 0 && projectIndex < pathParts.length - 1) {
              relativePath = pathParts.slice(projectIndex + 1).join('/');
            } else {
              relativePath = path.substring(path.lastIndexOf('/') + 1);
            }
          }

          const newDestinationPath = relativePath
            ? `${newProjectPath}/${relativePath}`
            : `${newProjectPath}/${path.substring(path.lastIndexOf('/') + 1)}`;

          // Use fast native copy instead of download/upload
          const newUrl = await copyFirebaseFileFast(path, newDestinationPath);
          urlMap.set(url, newUrl);
          processed++;
        } catch (error) {
          logger.warn('Failed to copy Firebase file', {
            path: path.substring(0, 100),
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }),
    );

    logger.info('Progress: Copied files', {
      processed,
      total: urlsToProcess.length,
      percentage: Math.round((processed / urlsToProcess.length) * 100),
    });
  }

  // Step 4: Copy .dzi _files directories in parallel
  if (dziFiles.length > 0) {
    logger.info('Copying .dzi _files directories...', { count: dziFiles.length });

    await Promise.all(
      dziFiles.map(async ({ path: dziFilesDir, newPath: newDziFilesDir }) => {
        try {
          await copyFirebaseDirectory(dziFilesDir, newDziFilesDir, urlMap);
        } catch (error) {
          logger.warn('Failed to copy .dzi _files directory', {
            source: dziFilesDir,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }),
    );

    logger.info('Completed copying .dzi _files directories', { count: dziFiles.length });
  }

  logger.info('Completed copying all Firebase files', {
    totalFiles: urlMap.size,
    regularFiles: urlsToProcess.length,
    dziDirectories: dziFiles.length,
  });

  return urlMap;
}

/**
 * Recursively replaces URLs in data using the URL map
 */
function replaceUrlsInData (data: any, urlMap: Map<string, string>): any {
  if (data === null || data === undefined) {
    return data;
  }

  if (typeof data === 'string') {
    return urlMap.get(data) || data;
  }

  if (Array.isArray(data)) {
    return data.map((item) => replaceUrlsInData(item, urlMap));
  }

  if (typeof data === 'object') {
    const result: any = {};
    for (const [key, value] of Object.entries(data)) {
      result[key] = replaceUrlsInData(value, urlMap);
    }
    return result;
  }

  return data;
}

/**
 * Controller for importing project data (replacing project IDs and saving to database)
 * Accepts exported JSON file upload or JSON in request body, organization_id from request
 * Generates a new project ID, replaces all project IDs, copies Firebase files, and imports all modules
 */
export async function ImportProject (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  // Handle file upload using multer
  upload(request, response, async (uploadError) => {
    if (uploadError) {
      logger.error('Error uploading file', { error: uploadError });
      response.status(400).json({
        status: 0,
        error: 'File upload error',
        message: uploadError.message,
      });
      return;
    }

    const organization_id = request.organization_id as string;

    // Get JSON data from file upload or request body (for backward compatibility)
    let exportData: any = null;

    if (request.file) {
      // Parse JSON from uploaded file
      try {
        const jsonText = request.file.buffer.toString('utf-8');
        exportData = JSON.parse(jsonText);
        logger.info('JSON file parsed successfully', {
          filename: request.file.originalname,
          size: `${(request.file.size / 1024 / 1024).toFixed(2)}MB`,
          organization_id,
        });
      } catch (parseError) {
        logger.error('Error parsing JSON file', { error: parseError });
        response.status(400).json({
          status: 0,
          error: 'Invalid JSON file',
          message: parseError instanceof Error ? parseError.message : 'Failed to parse JSON',
        });
        return;
      }
    } else if (request.body && Object.keys(request.body).length > 0 && request.body.export_metadata) {
      // Fallback to request body for backward compatibility (check if it looks like export data)
      exportData = request.body;
      logger.info('Using JSON data from request body', { organization_id });
    } else {
      response.status(400).json({
        status: 0,
        error: 'No file uploaded and no valid data in request body',
        message: 'Please upload a JSON file (field name: "file") or provide JSON data in request body',
      });
      return;
    }

    if (!exportData) {
      response.status(400).json({
        status: 0,
        error: 'Export data is required',
      });
      return;
    }

    if (!organization_id) {
      response.status(400).json({
        status: 0,
        error: 'Organization ID is required',
      });
      return;
    }

    // Get the old project ID from export metadata or project_data
    const oldProjectId =
      exportData.export_metadata?.source_project_id ||
      exportData.project_data?._doc?._id ||
      exportData.project_data?._id;

    if (!oldProjectId) {
      response.status(400).json({
        status: 0,
        error: 'Could not determine old project ID from export data',
      });
      return;
    }

    // Generate a new project ID
    const newProjectId = new mongoose.Types.ObjectId().toString();
    logger.info('ReplaceProjectId (Import) called', {
      oldProjectId,
      newProjectId,
      organization_id,
    });

    // Step 1: Copy all Firebase Storage files to new project location
    const startTime = Date.now();
    logger.info('Step 1: Copying Firebase Storage files to new location...', {
      oldProjectId,
      newProjectId,
      timestamp: new Date().toISOString(),
    });

    const urlMap = await copyFirebaseFiles(
      exportData,
      oldProjectId,
      newProjectId,
      organization_id,
    );

    const copyDuration = ((Date.now() - startTime) / 1000).toFixed(2);
    logger.info('Firebase files copied', {
      count: urlMap.size,
      duration: `${copyDuration}s`,
      timestamp: new Date().toISOString(),
    });

    // Step 2: Replace all project IDs in the data
    logger.info('Step 2: Replacing project IDs in data...');
    let updatedExportData = replaceProjectIdInData(
      exportData,
      oldProjectId,
      newProjectId,
      organization_id,
    );

    // Step 3: Replace all URLs with new Firebase Storage URLs
    logger.info('Step 3: Updating URLs to new Firebase Storage locations...');
    updatedExportData = replaceUrlsInData(updatedExportData, urlMap);

    // Use targeted JSON string replacement for project_id and _id fields
    try {
      const exportDataString = JSON.stringify(updatedExportData);
      // Escape special regex characters in the old project ID
      const escapedOldId = oldProjectId.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

      // Only replace in specific contexts to avoid affecting organization_id
      const patterns = [
        // Match "project_id":"oldId"
        new RegExp(`"project_id"\\s*:\\s*"${escapedOldId}"`, 'g'),
        // Match "source_project_id":"oldId"
        new RegExp(`"source_project_id"\\s*:\\s*"${escapedOldId}"`, 'g'),
      ];

      let updatedExportDataString = exportDataString;
      patterns.forEach((pattern) => {
        updatedExportDataString = updatedExportDataString.replace(pattern, (match) => {
          return match.replace(`"${oldProjectId}"`, `"${newProjectId}"`);
        });
      });

      updatedExportData = JSON.parse(updatedExportDataString);
    } catch (jsonError) {
      logger.warn('JSON string replacement fallback failed, using recursive function result only', {
        error: jsonError,
      });
    }

    // Explicitly ensure export_metadata.source_project_id is updated
    if (updatedExportData.export_metadata) {
      updatedExportData.export_metadata.source_project_id = newProjectId;
      // Preserve organization_id in metadata
      updatedExportData.export_metadata.source_organization_id = organization_id;
    }

    // Ensure project_data._id is set to the new project ID for database import
    if (updatedExportData.project_data) {
      if (updatedExportData.project_data._doc && updatedExportData.project_data._doc._id) {
        updatedExportData.project_data._doc._id = newProjectId;
      }
      if (updatedExportData.project_data._id) {
        updatedExportData.project_data._id = newProjectId;
      }
      // Explicitly preserve organization_id in project_data (never replace it)
      if (updatedExportData.project_data._doc) {
        updatedExportData.project_data._doc.organization_id = organization_id;
      }
      if (updatedExportData.project_data.organization_id) {
        updatedExportData.project_data.organization_id = organization_id;
      }
    }

    // Final safety check: Ensure organization_id is preserved everywhere
    const preserveOrganizationId = (obj: any): any => {
      if (obj === null || obj === undefined) {
        return obj;
      }
      if (Array.isArray(obj)) {
        return obj.map((item) => preserveOrganizationId(item));
      }
      if (typeof obj === 'object') {
        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          if (key === 'organization_id' || key === 'source_organization_id') {
            result[key] = organization_id;
          } else if (typeof value === 'object' && value !== null) {
            result[key] = preserveOrganizationId(value);
          } else {
            result[key] = value;
          }
        }
        return result;
      }
      return obj;
    };

    // Apply final safety check to ensure organization_id is never replaced
    updatedExportData = preserveOrganizationId(updatedExportData);

    // Now import all the data into the database
    logger.info('Starting import of project data', {
      oldProjectId,
      newProjectId,
      organization_id,
    });

    const importResults: any = {
      project: null,
      units: [],
      buildings: [],
      amenities: [],
      communities: [],
      unitplans: [],
      assets: [],
      gallery: [],
      models: [],
      scenes: [],
      landmarks: [],
      svgs: [],
      minimap: [],
      sidebar: [],
      virtual_tour: [],
      custom_tour: [],
    };

    try {
      // Import Project Data
      if (updatedExportData.project_data) {
        const projectModule = new ProjectModule(organization_id);
        const projectData = updatedExportData.project_data._doc || updatedExportData.project_data;

        // Ensure _id and organization_id are set correctly
        projectData._id = newProjectId;
        projectData.organization_id = organization_id;

        // Remove Mongoose internal fields
        delete projectData.$__;
        delete projectData.$isNew;

        // Migrate share_scenes from boolean to object format if needed
        if (
          projectData.projectSettings?.ale?.share_scenes &&
          typeof projectData.projectSettings.ale.share_scenes === 'boolean'
        ) {
          const shareScenesBoolean = projectData.projectSettings.ale.share_scenes;
          projectData.projectSettings.ale.share_scenes = {
            whatsapp: shareScenesBoolean,
            email: shareScenesBoolean,
            twitter: shareScenesBoolean,
          };
        } else if (
          projectData.projectSettings?.ale &&
          (!projectData.projectSettings.ale.share_scenes ||
            typeof projectData.projectSettings.ale.share_scenes !== 'object')
        ) {
          // Create empty object if share_scenes doesn't exist or is not an object
          projectData.projectSettings.ale.share_scenes = {
            whatsapp: false,
            email: false,
            twitter: false,
          };
        }

        /* eslint-disable dot-notation */
        const projectModel = projectModule['model'];
        const newProject = new projectModel(projectData);
        await newProject.save();
        importResults.project = newProject.toObject();
        logger.info('Project imported successfully', { project_id: newProjectId });
      }

      // Import Units
      if (updatedExportData.modules?.units && updatedExportData.modules.units.length > 0) {
        const unitsModel = mongoose.model(
          `${newProjectId}${Models._UNITS}`,
          unitSchema,
        );

        const unitDocs = updatedExportData.modules.units.map((unit:any) => {
          const unitData = unit._doc || unit;

          delete unitData.$__;
          delete unitData.$isNew;

          unitData.project_id = newProjectId;

          return unitData;
        });

        const savedUnits = await unitsModel.insertMany(unitDocs);

        importResults.units.push(...savedUnits.map((u) => u.toObject()));

        logger.info('Units imported successfully', { count: importResults.units.length });
      }

      // Import Buildings
      if (updatedExportData.modules?.buildings && updatedExportData.modules.buildings.length > 0) {
        const buildingModuleInstance = new buildingModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const buildingModel = buildingModuleInstance['model']; // Access the model directly

        const buildingDocs = updatedExportData.modules.buildings.map((building:any) => {
          const buildingData: any = building._doc || building;
          delete buildingData.$__;
          delete buildingData.$isNew;
          // Preserve the original _id from export
          buildingData.project_id = newProjectId;
          return buildingData;
        });

        const savedBuildings = await buildingModel.insertMany(buildingDocs);
        importResults.units.push(...savedBuildings.map((u) => u.toObject()));

        logger.info('Buildings imported successfully', { count: importResults.buildings.length });
      }

      // Import Unitplans
      if (updatedExportData.modules?.unitplans && updatedExportData.modules.unitplans.length > 0) {
        const unitplanModuleInstance = new unitplanModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const unitplanModel = unitplanModuleInstance['model']; // Access the model directly

        const unitplanDocs = updatedExportData.modules.unitplans.map((unitplan:any) => {
          const unitplanData: any = { ...unitplan };
          unitplanData.project_id = newProjectId;
          return unitplanData;
        });
        const savedUnitplans = await unitplanModel.insertMany(unitplanDocs);
        importResults.unitplans.push(...savedUnitplans.map((u) => u.toObject()));
        logger.info('Unitplans imported successfully', { count: importResults.unitplans.length });
      }

      // Import Amenities
      if (updatedExportData.modules?.amenities && updatedExportData.modules.amenities.length > 0) {
        const amenityModule = new AmenityModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const amenityModel = amenityModule['model']; // Access the model directly

        const amenityDocs = updatedExportData.modules.amenities.map((amenity:any) => {
          const amenityData: any = amenity._doc || amenity;
          delete amenityData.$__;
          delete amenityData.$isNew;
          // Preserve the original _id from export (use as 'id' for amenities)
          if (amenityData._id) {
            amenityData.id = amenityData._id;
          }
          amenityData.modified = new Date().toISOString();
          amenityData.project_id = newProjectId;
          return amenityData;
        });
        const savedAmenities = await amenityModel.insertMany(amenityDocs);
        importResults.amenities.push(...savedAmenities.map((u) => u.toObject()));
        logger.info('Amenities imported successfully', { count: importResults.amenities.length });
      }

      // Import Communities
      if (updatedExportData.modules?.communities && updatedExportData.modules.communities.length > 0) {
        const communityModuleInstance = new communityModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const communityModel = communityModuleInstance['model']; // Access the model directly

        const communityDocs = updatedExportData.modules.communities.map((community: any) => {
          const communityData: any = community._doc || community;
          delete communityData.$__;
          delete communityData.$isNew;
          // Preserve the original _id from export
          communityData.project_id = newProjectId;
          return communityData;
        });
        const savedCommunities = await communityModel.insertMany(communityDocs);
        importResults.communities.push(...savedCommunities.map((newCommunity) => newCommunity.toObject()));
        logger.info('Communities imported successfully', { count: importResults.communities.length });
      }

      // Import Project Scenes + Frames
      if (
        updatedExportData.modules?.scenes &&
        updatedExportData.modules.scenes.length > 0
      ) {
        const sceneModule = new ProjectSceneModule(newProjectId, organization_id);
        const sceneModel = sceneModule['model'];

        const scenesToInsert: any[] = [];
        const framesToInsert: any[] = [];

        // Split scenes vs frames
        for (const scene of updatedExportData.modules.scenes) {
          const sceneData: any = scene._doc || scene;

          delete sceneData.$__;
          delete sceneData.$isNew;

          sceneData.organization_id = organization_id;

          if (sceneData.type === projectSceneType.ROTATABLE_IMAGE_FRAME) {
            framesToInsert.push(sceneData);
          } else {
            scenesToInsert.push(sceneData);
          }
        }

        // 1️⃣ Insert non-frame scenes first
        const insertedScenes = scenesToInsert.length
          ? await sceneModel.insertMany(scenesToInsert)
          : [];

        // Map old -> new ids (if preserved they'll be identical, but safe)
        const sceneIdMap = new Map<string, string>();

        insertedScenes.forEach((scene: any, idx: number) => {
          const oldId = scenesToInsert[idx]._id?.toString();
          if (oldId) {
            sceneIdMap.set(oldId, scene._id.toString());
          }
          importResults.scenes.push(scene.toObject());
        });

        // 2️⃣ Prepare frames with remapped parent
        const preparedFrames = framesToInsert.map((frame: any) => {
          const oldParentId = frame.parent?.toString();

          if (oldParentId && sceneIdMap.has(oldParentId)) {
            frame.parent = sceneIdMap.get(oldParentId);
          }

          return frame;
        });

        // 3️⃣ Insert frames
        const insertedFrames = preparedFrames.length
          ? await sceneModel.insertMany(preparedFrames)
          : [];

        insertedFrames.forEach((frame: any) => {
          importResults.scenes.push(frame.toObject());
        });

        logger.info('Scenes imported successfully', {
          scenes: insertedScenes.length,
          frames: insertedFrames.length,
          total: insertedScenes.length + insertedFrames.length,
        });
      }

      // Import SVGs
      if (updatedExportData.modules?.svgs && updatedExportData.modules.svgs.length > 0) {
        const svgModel = mongoose.model(
          `${newProjectId}${Models._SVGS}`,
          projectsvgSchema,
        );

        const svgDocs = updatedExportData.modules.svgs.map((svg: any) => {
          const svgData = { ...svg };

          return svgData;
        });
        const savedSVG = await svgModel.insertMany(svgDocs);
        importResults.svgs.push(...savedSVG.map((newSVG) => newSVG.toObject()));
        logger.info('SVGs imported successfully', { count: importResults.svgs.length });
      }

      // Import Assets
      if (updatedExportData.modules?.assets && updatedExportData.modules.assets.length > 0) {
        const assetsModuleInstance = new AssetsModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const assetsModel = assetsModuleInstance['model']; // Access the model directly

        for (const asset of updatedExportData.modules.assets) {
          const assetData: any = asset._doc || asset;
          delete assetData.$__;
          delete assetData.$isNew;
          // Preserve the original _id from export
          assetData.project_id = newProjectId;

          // Create asset with the original _id
          const newAsset = new assetsModel(assetData);
          await newAsset.save();
          importResults.assets.push(newAsset.toObject());
        }
        logger.info('Assets imported successfully', { count: importResults.assets.length });
      }

      // Import Gallery
      if (updatedExportData.modules?.gallery && updatedExportData.modules.gallery.length > 0) {
        const galleryModuleInstance = new GalleryModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const galleryModel = galleryModuleInstance['model']; // Access the model directly

        const galleryDocs = updatedExportData.modules.gallery.map((galleryItem:any) => {
          const galleryData: any = galleryItem._doc || galleryItem;
          delete galleryData.$__;
          delete galleryData.$isNew;
          // Preserve the original _id from export
          if (galleryData._id) {
            galleryData.id = galleryData._id;
          }
          galleryData.modified = new Date().toISOString();
          return galleryData;
        });

        const savedGalleries = await galleryModel.insertMany(galleryDocs);
        importResults.gallery.push(...savedGalleries.map((g) => g.toObject()));
        logger.info('Gallery imported successfully', { count: importResults.gallery.length });
      }

      // Import Models (GLB)
      if (updatedExportData.modules?.models && updatedExportData.modules.models.length > 0) {
        const modelModuleInstance = new ModelModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const modelModel = modelModuleInstance['model']; // Access the model directly

        const glbModelDocs = updatedExportData.modules.models.map((glb:any) => {
          const modelData: any = glb._doc || glb;
          delete modelData.$__;
          delete modelData.$isNew;

          return modelData;
        });
        const savedGlbModels = await modelModel.insertMany(glbModelDocs);
        importResults.models.push(...savedGlbModels.map((g) => g.toObject()));
        logger.info('Models imported successfully', { count: importResults.models.length });
      }

      // Import Project Landmarks
      if (updatedExportData.modules?.landmarks && updatedExportData.modules.landmarks.length > 0) {
        const landmarkModuleInstance = new ProjectLandmarkModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const landmarkModel = landmarkModuleInstance['model']; // Access the model directly

        const landmarkDocs = updatedExportData.modules.landmarks.map((landmark:any) => {
          const landmarkData: any = landmark._doc || landmark;
          delete landmarkData.$__;
          delete landmarkData.$isNew;

          return landmarkData;
        });
        const savedLandmarks = await landmarkModel.insertMany(landmarkDocs);
        importResults.models.push(...savedLandmarks.map((g) => g.toObject()));
        logger.info('Landmarks imported successfully', { count: importResults.landmarks.length });
      }

      // Import MiniMap
      if (updatedExportData.modules?.minimap && updatedExportData.modules.minimap.length > 0) {
        const minimapModuleInstance = new MiniMapModule(newProjectId, organization_id);
        /* eslint-disable dot-notation */
        const minimapModel = minimapModuleInstance['model']; // Access the model directly

        const minimapDocs = updatedExportData.modules.minimap.map((minimap:any) => {
          const minimapData: any = minimap._doc || minimap;
          delete minimapData.$__;
          delete minimapData.$isNew;

          return minimapData;
        });
        const savedMinimaps = await minimapModel.insertMany(minimapDocs);
        importResults.models.push(...savedMinimaps.map((g) => g.toObject()));
        logger.info('MiniMap imported successfully', { count: importResults.minimap.length });
      }

      // Import Sidebar
      if (updatedExportData.modules?.sidebar && updatedExportData.modules.sidebar.length > 0) {
        const sidebarModel = mongoose.model(
          `${newProjectId}${Models._SIDEBAR}`,
          sidebarSchema,
        );

        const sidebarDocs = updatedExportData.modules.sidebar.map((sidebarItem:any) => {
          const sidebarData: any = sidebarItem._doc || sidebarItem;
          delete sidebarData.$__;
          delete sidebarData.$isNew;

          return sidebarData;
        });
        const savedSideBars = await sidebarModel.insertMany(sidebarDocs);
        importResults.sidebar.push(...savedSideBars.map((g) => g.toObject()));
        logger.info('Sidebar imported successfully', { count: importResults.sidebar.length });
      }

      // Import Virtual Tour
      if (updatedExportData.modules?.virtual_tour && updatedExportData.modules.virtual_tour.length > 0) {
        const virtualTourModuleInstance = new VirtualTourModule(organization_id, newProjectId);
        /* eslint-disable dot-notation */
        const virtualTourModel = virtualTourModuleInstance['model']; // Access the model directly

        const virtualTourDocs = updatedExportData.modules.virtual_tour.map((virtualTour:any) => {
          const virtualTourData: any = virtualTour._doc || virtualTour;
          delete virtualTourData.$__;
          delete virtualTourData.$isNew;
          // Preserve the original _id from export
          virtualTourData.organization = organization_id;
          virtualTourData.project_id = newProjectId;

          return virtualTourData;
        });
        const savedVirtualTours = await virtualTourModel.insertMany(virtualTourDocs);
        importResults.virtual_tour.push(...savedVirtualTours.map((g) => g.toObject()));
        logger.info('Virtual Tours imported successfully', { count: importResults.virtual_tour.length });
      }

      if (
        updatedExportData.modules?.custom_tour &&
        updatedExportData.modules.custom_tour.length > 0
      ) {
        // Build old -> new virtual tour id map
        const tourIdMap = new Map<string, string>();

        if (
          updatedExportData.modules.virtual_tour &&
          importResults.virtual_tour.length > 0
        ) {
          const oldTours = updatedExportData.modules.virtual_tour;
          const newTours = importResults.virtual_tour;

          if (oldTours.length === newTours.length) {
            for (let i = 0; i < oldTours.length; i++) {
              const oldTourId = (
                oldTours[i]._doc?._id || oldTours[i]._id
              )?.toString();

              const newTourId = newTours[i]._id?.toString();

              if (oldTourId && newTourId) {
                tourIdMap.set(oldTourId, newTourId);
              }
            }
          }
        }

        // Group custom tour images by OLD tour id
        const customTourByOldId = new Map<string, any[]>();

        for (const customTourItem of updatedExportData.modules.custom_tour) {
          const customTourData: any =
            customTourItem._doc || customTourItem;

          const oldTourId =
            customTourData.tour_id ||
            customTourData._tour_id;

          if (!oldTourId) {
            continue;
          }

          if (!customTourByOldId.has(oldTourId)) {
            customTourByOldId.set(oldTourId, []);
          }

          customTourByOldId.get(oldTourId)!.push(customTourData);
        }

        // 3️⃣ Insert custom tours per mapped tour
        for (const [oldTourId, newTourId] of tourIdMap.entries()) {
          const images = customTourByOldId.get(oldTourId);

          if (!images || images.length === 0) {
            continue;
          }

          const customTourModuleInstance = new customTourModule(
            organization_id,
            newTourId,
            newProjectId,
          );

          const customTourModel =
            customTourModuleInstance['model'];

          const preparedDocs = images.map((img) => {
            const imageData: any = img._doc || img;

            delete imageData.$__;
            delete imageData.$isNew;

            // Ensure new FK
            imageData.tour_id = newTourId;
            imageData.project_id = newProjectId;

            return imageData;
          });

          const insertedImages = await customTourModel.insertMany(
            preparedDocs,
          );

          importResults.custom_tour.push(
            ...insertedImages.map((d) => d.toObject()),
          );
        }

        logger.info('Custom Tours imported successfully', {
          count: importResults.custom_tour.length,
        });
      }

      logger.info('ReplaceProjectId (Import) completed successfully', {
        oldProjectId,
        newProjectId,
        organization_id,
        importResults: Object.keys(importResults).map((key) => ({
          module: key,
          count: Array.isArray(importResults[key]) ? importResults[key].length : importResults[key] ? 1 : 0,
        })),
      });

      response.status(200).json({
        status: 1,
        message: 'Project imported successfully',
        data: {
          project_id: newProjectId,
          organization_id,
          importResults,
        },
        metadata: {
          oldProjectId,
          newProjectId,
          organization_id,
        },
      });
    } catch (error) {
      // Safely get oldProjectId and newProjectId for error logging
      let errorOldProjectId = 'unknown';
      let errorNewProjectId = 'unknown';

      try {
        errorOldProjectId = (exportData?.export_metadata?.source_project_id ||
                            exportData?.project_data?._doc?._id ||
                            exportData?.project_data?._id) || 'unknown';
        errorNewProjectId = newProjectId || 'unknown';
      } catch {
        // Ignore errors when trying to get IDs for logging
      }

      logger.error('Error in ImportProject', {
        error,
        message: error instanceof Error ? error.message : 'Unknown error',
        oldProjectId: errorOldProjectId,
        newProjectId: errorNewProjectId,
        organization_id: organization_id || 'unknown',
      });

      response.status(500).json({
        status: 0,
        error: 'Failed to import project',
        message: error instanceof Error ? error.message : 'Unknown error',
        oldProjectId: errorOldProjectId,
        newProjectId: errorNewProjectId,
        organization_id: organization_id || 'unknown',
      });
    }
  }); // Close multer upload callback
}

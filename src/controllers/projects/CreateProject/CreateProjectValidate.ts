import { check, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import {ExperienceType, PropertyType, Theme } from '../../../types/projects';
interface UploadedFiles {
  project_thumbnail: Express.Multer.File[];
}

const CreateProjectValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.files);
  console.log(req.body);

  const files = req.files as UploadedFiles | undefined;

  if (files) {
    if (!files.project_thumbnail) {
      res
        .status(400)
        .json({ error: 'Both lowRes and highRes image fields are required.' });
    } else {
      const requiredTextFields = [
        'name',
        'property_type',
        'experience',
        'city',
        'country',
      ];
      if (req.body.theme === Theme.CUSTOM) {
        requiredTextFields.push('primary', 'secondary');
      }

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        check('experience')
          .custom((experienceArray) => {
            if (!Array.isArray(experienceArray)) {
              throw new Error('Experience must be an array');
            }
            const validValues = Object.values(ExperienceType);
            if (!experienceArray.every((value) => validValues.includes(value))) {
              throw new Error('Invalid project type value. Please ensure that all items are valid types.');
            }
            return true;
          })
          .run(req);
        check('property_type', 'Invalid property type value. Please ensure that you are using a valid type value')
          .isIn(Object.values(PropertyType)).run(req);
        check('theme', 'Invalid project theme value. Please ensure that you are using a valid theme value')
          .isIn(Object.values(Theme)).run(req);

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default CreateProjectValidate;

import { Request, Response } from 'express';
import { ProjectModule, invalidateProjectAPIs } from '../../../modules/projects';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';

export async function UploadProjectSettingFiles (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  //   Pass organization_id in this ProjectModule
  const project = new ProjectModule(organization_id);
  const project_id = request.body.project_id;
  const requestFiles = request.files;
  if (requestFiles === undefined ){
    response.send('Error reading file:');
    return;
  }
  UploadUnitplanFiles(requestFiles, project.storagepath+project_id+'/settings').then((urlObject) => {
    const updateObj = {
      branding_logo: urlObject.branding_logo,
      branding_logo_dark: urlObject.branding_logo_dark,
      thumbnail: urlObject.thumbnail,
      file: urlObject.file,
      hologram_project_logo: urlObject.hologram_project_logo, // Hologram app
      welcome_video: urlObject.welcome_video,
      welcome_thumbnail: urlObject.welcome_thumbnail,
      font_url: urlObject.font_url,
    };
    if (updateObj) {
      response.status(200).json({ status: 1, data: updateObj });

      invalidateProjectAPIs(organization_id, project_id).then((res) => {
        logger.info('Project APIs invalidated successfully', { result: res });
      }).catch((err) => {
        logger.error('Error invalidating project APIs', { error: err });
      });
    } else {
      logger.error('Project not found');
      response.status(404).json({ status: 0, error: 'Project not found' });
    }
  });

}

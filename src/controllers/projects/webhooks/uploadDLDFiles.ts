import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
import { admin } from '../../../config/firebase';

export async function UploadDLDFiles (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const requestFiles = request.files;

    // Check if files are provided
    if (requestFiles === undefined) {
      response.status(400).json({ status: 0, error: 'Files are required.' });
      return;
    }

    // Generate documentId if not provided, or use the provided one
    // This allows uploading files for existing projects or creating new ones
    const documentId = admin.firestore().collection('DLD_POC_PROJECTS').doc().id;

    // Define storage path
    const storagePath = `DLD_POC_PROJECTS/${documentId}`;

    // Upload files to Firebase Storage and get URLs
    const urlObject: { [key: string]: string } = await UploadUnitplanFiles(
      requestFiles,
      storagePath,
    );

    logger.info('Files uploaded successfully', { documentId, urlObject });

    // Return success response with file URLs
    response.status(200).json({
      status: 1,
      message: 'Files uploaded successfully',
      data: {
        project_id: documentId,
        fileUrls: urlObject,
      },
    });
  } catch (error) {
    logger.error('Error in UploadDLDFiles:', { message: error });
    response.status(500).json({
      status: 0,
      error: 'Error uploading files',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

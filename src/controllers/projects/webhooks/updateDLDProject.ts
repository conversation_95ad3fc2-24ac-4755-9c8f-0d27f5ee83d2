import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import { admin } from '../../../config/firebase';

export async function UpdateDLDProject (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const requestBody = request.body;
    const projectId = requestBody.project_id  as string | undefined;

    // Validate that project_id is provided
    if (!projectId) {
      logger.error('UpdateDLDProject: project_id is required');
      response.status(400).json({
        status: 0,
        error: 'project_id is required',
      });
      return;
    }

    // Check if document exists
    const firestoreRef = admin.firestore().collection('DLD_POC_PROJECTS').doc(projectId);
    const docSnapshot = await firestoreRef.get();

    if (!docSnapshot.exists) {
      logger.error('UpdateDLDProject: Project not found', { projectId });
      response.status(404).json({
        status: 0,
        error: 'Project not found',
      });
      return;
    }

    // Prepare update data - exclude project_id from update data
    const updateData = { ...requestBody };
    delete updateData.project_id;

    // Update the document
    await firestoreRef.update({
      ...updateData,
      updatedAt: new Date(),
    });

    // Get the updated document
    const updatedDoc = await firestoreRef.get();
    const updatedData = {
      project_id: updatedDoc.id,
      ...updatedDoc.data(),
    };

    logger.info('UpdateDLDProject: Document updated successfully', { projectId });

    response.status(200).json({
      status: 1,
      message: 'Project updated successfully',
      data: updatedData,
    });
  } catch (error) {
    logger.error('Error in UpdateDLDProject:', { message: error });
    response.status(500).json({
      status: 0,
      error: 'Error updating project in Firestore',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

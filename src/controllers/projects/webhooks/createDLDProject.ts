import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import { admin } from '../../../config/firebase';

export async function CreateDLDProject (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const requestBody = request.body;

    // Use provided documentId or generate a new one
    // This allows linking with uploaded files by using the same documentId
    const documentId = requestBody.project_id || admin.firestore().collection('DLD_POC_PROJECTS').doc().id;

    // Prepare data to save in Firestore
    // FileUrls should be provided in request body if files were uploaded separately
    const firestoreData = {
      ...requestBody,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Save to Firestore
    const firestoreRef = admin.firestore().collection('DLD_POC_PROJECTS').doc(documentId);

    // Check if document exists to decide between set and update
    const docSnapshot = await firestoreRef.get();

    if (docSnapshot.exists) {
      // Update existing document (preserve existing fields, merge new ones)
      await firestoreRef.update({
        ...requestBody,
        updatedAt: new Date(),
      });
      logger.info('Firestore document updated successfully', { documentId });
    } else {
      // Create new document
      await firestoreRef.set(firestoreData);
      logger.info('Firestore document created successfully', { documentId });
    }

    // Return success response
    // Include project_id in response only if it was NOT provided in request body
    const responseData = requestBody.project_id
      ? { ...requestBody }
      : { ...requestBody, project_id: documentId };

    response.status(200).json({
      status: 1,
      message: 'Data saved successfully',
      data: responseData,
    });
  } catch (error) {
    logger.error('Error in CreateDLDProject:', { message: error });
    response.status(500).json({
      status: 0,
      error: 'Error saving data to Firestore',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

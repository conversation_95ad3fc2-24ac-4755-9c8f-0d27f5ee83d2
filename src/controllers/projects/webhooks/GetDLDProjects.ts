import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import { admin } from '../../../config/firebase';

// DLD Project filters configuration
const DLD_PROJECT_FILTERS = {
  'Area': {
    'filters': [
      {
        'filterName': 'Construction Status',
        'key': 'construction status',
        'type': 'button',
        'parameters': {
          'options': [
            'Any',
            'Ready to Move',
            'Under Construction',
            'Off-Plan',
          ],
          'gridSize': 2,
        },
      },
      {
        'filterName': 'Project Type',
        'key': 'status',
        'type': 'button',
        'parameters': {
          'options': [
            'Any',
            'Ready to Move',
            'Under Construction',
            'Off-Plan',
          ],
          'gridSize': 2,
        },
      },
      {
        'filterName': 'Purchase Type',
        'key': 'purchase type',
        'type': 'button',
        'parameters': {
          'options': [
            'Rent',
            'Leese',
            'Purchase',
          ],
          'gridSize': 2,
        },
      },
      {
        'filterName': 'Bed',
        'key': 'bedroomType',
        'type': 'button',
        'parameters': {
          'options': [
            'Any',
            'Studio',
            '1 BR',
            '2 BR',
            '3 BR',
            '4 BR',
            'More than 5BR',
          ],
          'gridSize': 2,
        },
      },
      {
        'filterName': 'Area',
        'key': 'area',
        'type': 'slider',
        'parameters': {
          'min': 2208.11,
          'max': 17078.56,
          'stepSize': 100,
          'unit': 'Sq.Ft.',
        },
      },
      {
        'filterName': 'Price/Sqft',
        'key': 'price',
        'type': 'slider',
        'parameters': {
          'min': 1051,
          'max': 2131,
          'stepSize': 10,
          'unit': 'AED/Sqft',
        },
      },
      {
        'filterName': 'Bedrooms',
        'key': 'bedroomRange',
        'type': 'slider',
        'parameters': {
          'min': 1,
          'max': 5,
          'stepSize': 1,
          'unit': 'BR',
        },
      },
      {
        'filterName': 'Dubai Land Department Area',
        'key': 'Location',
        'type': 'button',
        'parameters': {
          'options': [
            'Downtown Dubai',
            'Bur Dubai',
            'Dubai Marina',
          ],
          'gridSize': 2,
        },
      },
    ],
    'Projects': [
      {
        'ProjectName': 'Emaar Burj Vista',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Apartment',
        'ProjectThumbnail': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'Type': 'Residential',
        'area': 2736,
        'areaRange': {
          'min': 790,
          'max': 4327,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '4 BR',
        'bedroomRange': {
          'min': 100,
          'max': 1400,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 3113000,
        'currency': 'AED',
        'price / Sqft': 2736,
        'consturction status': 'Ready to Move',
        'purchase type': 'Rent',
        'Project Params': {
          'Type': 'EXE_URL',
          'EXE_URL': 'Projects/MetahumanChat/MetahumanChat.exe',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.19910443411755',
            'Longitude': '55.27137898296408',
          },
        },
      },
      {
        'ProjectName': 'Damac-Canal Crown',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Apartment',
        'ProjectThumbnail': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'Type': 'Residential',
        'area': 2736,
        'areaRange': {
          'min': 790,
          'max': 4327,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '4 BR',
        'bedroomRange': {
          'min': 100,
          'max': 1400,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 3113000,
        'currency': 'AED',
        'price / Sqft': 2736,
        'consturction status': 'Ready to Move',
        'purchase type': 'Rent',
        'Project Params': {
          'Type': 'Weblite_URL',
          'EXE_URL': 'Projects/MetahumanChat/MetahumanChat.exe',
          'Weblite_URL': 'https://view.propvr.tech/yRnIS3/projectscene/671b6858f5b8a7a04c0744e0/67eba791cfba4cb32e133485',
          '3DModel': {
            'URL': '',
            'Latitude': '25.19910443411755',
            'Longitude': '55.27137898296408',
          },
        },
      }, {
        'ProjectName': 'Damac-Altitude',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Apartment',
        'ProjectThumbnail': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'Type': 'Residential',
        'area': 2736,
        'areaRange': {
          'min': 790,
          'max': 4327,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '4 BR',
        'bedroomRange': {
          'min': 100,
          'max': 1400,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 3113000,
        'currency': 'AED',
        'price / Sqft': 2736,
        'consturction status': 'Ready to Move',
        'purchase type': 'Rent',
        'Project Params': {
          'Type': 'Weblite_URL',
          'EXE_URL': '',
          'Weblite_URL': 'https://view.propvr.tech/yRnIS3/projectscene/676d20e249b9cc1bdec97de8/67ebab1fcfba4cb32e13422a',
          '3DModel': {
            'URL': '',
            'Latitude': '25.19910443411755',
            'Longitude': '55.27137898296408',
          },
        },
      },
      {
        'ProjectName': 'Dunya Tower',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Apartment',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Residential',
        'area': 823,
        'areaRange': {
          'min': 790,
          'max': 4327,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '1 BR',
        'bedroomRange': {
          'min': 100,
          'max': 1400,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 1760000,
        'currency': 'AED',
        'price / Sqft': 823,
        'consturction status': 'Ready to Move',
        'purchase type': 'Rent',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.194867265376136',
            'Longitude': '55.28336134063488',
          },
        },
      },
      {
        'ProjectName': 'Al Kharbash Tower',
        'Location': 'Bur Dubai',
        'ProjectType': 'Apartment',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Residential',
        'area': 1313,
        'areaRange': {
          'min': 790,
          'max': 1313,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '2 BR',
        'bedroomRange': {
          'min': 100,
          'max': 1313,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 1760000,
        'currency': 'AED',
        'price / Sqft': 1313,
        'consturction status': 'Ready to Move',
        'purchase type': 'Rent',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.207140129165275',
            'Longitude': '55.27117919948787',
          },
        },
      },
      {
        'ProjectName': 'Al Goze Building',
        'Location': 'Bur Dubai',
        'ProjectType': 'Office Space',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Commercial',
        'area': 3032,
        'areaRange': {
          'min': 790,
          'max': 3032,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': ' BR',
        'bedroomRange': {
          'min': 100,
          'max': 3032,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 1760000,
        'currency': 'AED',
        'price / Sqft': 1313,
        'consturction status': 'Ready to Move',
        'purchase type': 'Rent',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '',
            'Longitude': '',
          },
        },
      },
      {
        'ProjectName': 'LIV LUX Apartments',
        'Location': 'Dubai Marina',
        'ProjectType': 'Apartments',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Residential',
        'area': 3306,
        'areaRange': {
          'min': 748,
          'max': 3306,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '4 BR',
        'bedroomRange': {
          'min': 746,
          'max': 3306,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 2099000,
        'currency': 'AED',
        'price / Sqft': 3306,
        'consturction status': 'Ready to Move',
        'purchase type': '',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.08350851811635',
            'Longitude': '55.14164750994879',
          },
        },
      },
      {
        'ProjectName': 'Westside Marina',
        'Location': 'Dubai Marina',
        'ProjectType': 'Apartments',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Residential',
        'area': 1466,
        'areaRange': {
          'min': 895,
          'max': 1466,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '2 BR',
        'bedroomRange': {
          'min': 895,
          'max': 1466,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 2099000,
        'currency': 'AED',
        'price / Sqft': 1466,
        'consturction status': 'Ready to Move',
        'purchase type': '',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.07001301193252',
            'Longitude': '55.1333902848092',
          },
        },
      },
      {
        'ProjectName': 'Emaar Palace Downtown',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Studio',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Residential',
        'area': 549,
        'areaRange': {
          'min': 895,
          'max': 1466,
        },
        'status': 'Ready to Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': 'Studio',
        'bedroomRange': {
          'min': 895,
          'max': 1466,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 1760000,
        'currency': 'AED',
        'price / Sqft': 3259,
        'consturction status': 'Ready to Move',
        'purchase type': '',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.11372',
            'Longitude': '55.16326',
          },
        },
      },
      {
        'ProjectName': 'Emaar Forte 2',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Apartment',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Residential',
        'area': 846,
        'areaRange': {
          'min': 846,
          'max': 2409,
        },
        'status': 'Under Construction',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '1,2,3,4 BR',
        'bedroomRange': {
          'min': 895,
          'max': 1466,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 2135888,
        'currency': 'AED',
        'price / Sqft': 2606,
        'consturction status': 'Under Construction',
        'purchase type': '',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.11447',
            'Longitude': '55.16097',
          },
        },
      },
      {
        'ProjectName': 'Omniyat Opus',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Apartment',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Residential',
        'area': 846,
        'areaRange': {
          'min': 806,
          'max': 6078,
        },
        'status': 'Reay To Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '1,2,3 BR',
        'bedroomRange': {
          'min': 895,
          'max': 1466,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 3204965,
        'currency': 'AED',
        'price / Sqft': 3982,
        'consturction status': 'Ready To Move',
        'purchase type': '',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.188669',
            'Longitude': '55.267064',
          },
        },
      },
      {
        'ProjectName': 'Mashreq Bank Office Tower',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Office Space',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Commercial',
        'area': 6213,
        'areaRange': {
          'min': 846,
          'max': 6213,
        },
        'status': 'Ready To Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '',
        'bedroomRange': {
          'min': 895,
          'max': 1466,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': 2135888,
        'currency': 'AED',
        'price / Sqft': 2606,
        'consturction status': 'Ready To Move',
        'purchase type': '',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.**************',
            'Longitude': '55.***************',
          },
        },
      },
      {
        'ProjectName': 'Mashreq Bank Office Tower',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Office Space',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Commercial',
        'area': 6213,
        'areaRange': {
          'min': 846,
          'max': 6213,
        },
        'status': 'Ready To Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '',
        'bedroomRange': {
          'min': 895,
          'max': 1466,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': null,
        'currency': 'AED',
        'price / Sqft': null,
        'consturction status': 'Ready To Move',
        'purchase type': '',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.**************',
            'Longitude': '55.***************',
          },
        },
      },
      {
        'ProjectName': 'Dara Vizir Downtown',
        'Location': 'Downtown Dubai',
        'ProjectType': 'Residential',
        'ProjectThumbnail': '/Game/DLD/Textures/e367d070b9e3b76e8ce30b0de670ee5f24ad8c11.e367d070b9e3b76e8ce30b0de670ee5f24ad8c11',
        'Type': 'Apartment',
        'area': 1600,
        'areaRange': {
          'min': 846,
          'max': 1600,
        },
        'status': 'Ready To Move',
        'ProjectImage': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
        'bedroomType': '2 BR',
        'bedroomRange': {
          'min': 895,
          'max': 1466,
        },
        'Occupied Status': {
          'Available': '30 % Available',
          'Sold': '70 % Sold',
        },
        'price': null,
        'currency': 'AED',
        'price / Sqft': null,
        'consturction status': 'Ready To Move',
        'purchase type': '',
        'Project Params': {
          'Type': 'none',
          'EXE_URL': '',
          'Weblite_URL': 'https://s3.eu-west-1.amazonaws.com/damac-inv/otp/DIFJ1SD241FJ0170.jpg',
          '3DModel': {
            'URL': '',
            'Latitude': '25.201175576510625',
            'Longitude': '55.280683515124714',
          },
        },
      },
    ],
  },
};

export async function GetDLDProjects (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const projectId = request.query.project_id as string | undefined;

    // If project_id is provided, get a single project
    if (projectId) {
      const projectDoc = await admin.firestore()
        .collection('DLD_POC_PROJECTS')
        .doc(projectId)
        .get();

      if (!projectDoc.exists) {
        logger.error('DLD Project not found', { projectId });
        response.status(404).json({
          status: 0,
          error: 'Project not found',
        });
        return;
      }

      const projectData = {
        project_id: projectDoc.id,
        ...projectDoc.data(),
      };

      response.status(200).json({
        status: 1,
        data: projectData,
      });
      return;
    }

    // Get all projects
    const projectsSnapshot = await admin.firestore()
      .collection('DLD_POC_PROJECTS')
      .get();

    // Get hardcoded projects from DLD_PROJECT_FILTERS
    const hardcodedProjects = DLD_PROJECT_FILTERS.Area.Projects || [];

    // Convert Firestore documents to array
    const firestoreProjects: unknown[] = [];
    projectsSnapshot.forEach((doc) => {
      firestoreProjects.push({
        project_id: doc.id,
        ...doc.data(),
      });
    });

    // Combine hardcoded projects with Firestore projects
    const allProjects = [...hardcodedProjects, ...firestoreProjects];

    response.status(200).json({
      status: 1,
      data: {
        Area: {
          filters: DLD_PROJECT_FILTERS.Area.filters,
          Projects: allProjects,
        },
      },
    });
  } catch (error) {
    logger.error('Error in GetDLDProjects:', { message: error });
    response.status(500).json({
      status: 0,
      error: 'Error retrieving projects',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

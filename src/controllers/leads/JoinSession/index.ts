import { Request, Response } from 'express';
import { LeadsModule } from '../../../modules/leads';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
export async function JoinSession (
  request: Request,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = await session.getOrganizationId(request.body.session_id);
  const leads = new LeadsModule(organization_id);
  const joinsession_data = {
    session_id: request.body.session_id,
    lead_id: request.body.lead_id,
  };
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }

  leads
    .JoinSession(joinsession_data)
    .then((lead) => {
      response.send({ status: 1, data: lead });
    })
    .catch((error) => {
      logger.error('Error while updating session', {message: error});
      response.send({ status: 0, error: 'Error while updating session' + error });
    });
}

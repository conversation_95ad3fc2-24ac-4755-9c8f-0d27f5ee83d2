import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import { LeadIndustryType, LeadProductInterest, LeadSource, LeadStatus, LeadType } from '../../../types/leads';

const UpdateLeadValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('lead_id', 'Lead Id is required').notEmpty(),
  body('session_id', 'Session ID is required').optional(),
  body('type', 'Invalid Lead Type value. Please ensure that you are using a valid type value')
    .isIn(Object.values(LeadType)).optional(),
  body('type', 'Lead Type is required').optional(),
  body('source', 'Invalid source value. Please ensure that you are using a valid source value')
    .isIn(Object.values(LeadSource)).optional(),
  body('lead_industry_type', 'Invalid Lead Industry type. Please ensure that you are using a valid Lead Industry type')
    .isIn(Object.values(LeadIndustryType)).optional(),
  body('lead_product_interest', 'Lead product interest. Please ensure that you are using a valid Lead product interest')
    .isIn(Object.values(LeadProductInterest)).optional(),
  body('status', 'Invalid Status value. Please ensure that you are using a valid status value').optional()
    .isIn(Object.values(LeadStatus)),
  body('name', 'Lead Name is required').optional(),
  body('email', 'Email is invalid').isEmail().optional(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default UpdateLeadValidate;

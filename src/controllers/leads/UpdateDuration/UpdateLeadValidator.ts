import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const updateLeadDurationValidate = [
  body('session_id', 'Session ID is required').notEmpty(),
  body('lead_id', 'Lead ID is required').notEmpty(),
  body('duration_minutes', 'Duration is required').notEmpty(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default updateLeadDurationValidate;

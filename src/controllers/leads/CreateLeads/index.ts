import { LeadsModule, invalidateLeadAPIs } from '../../../modules/leads';
import { LeadStatus, Leads } from '../../../types/leads';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { SessionModule } from '../../../modules/sessions';
import { Session } from '../../../types/session';
import { TaskModule } from '../../../modules/tasks';
import logger from '../../../config/logger';
// Function sendMail (organization_id: string, sessionData: Session, leadData:Leads) {
//   Return new Promise((resolve, reject) => {
//     Const project = new ProjectModule(organization_id);
//     Const user =new UserModule();
//     User.getUsername(sessionData.user_id).then((username) => {
//       Project.getProjectById(sessionData.project_id).then((projectData) => {
//         If (!projectData) {
//           Throw new Error('Project Data not found');
//         }
//         Const projectname = projectData?.name;
//         Const subject = `Join Our Virtual Property Showcase and Discover ${projectname}`;
//         Const message = `<p>Dear ${leadData.name},
//       <br></br><br></br>
//       We would like to invite you to our virtual showcase meeting for property that I am representing.
//       The meeting will take place on
//       ${new Date(leadData.start_time).toUTCString()}. To attend the meeting,
//       You can click on the link provided below at the scheduled time. You can join the meeting
//       From the comfort of your home or office,
//       And there's no need to download any software or create an account.
//       You are free to use any device, but we strongly suggest you to use desktop or laptop with
//       Latest chrome browser for best experience.
//       <br></br><br></br>
//       Link: Link: <a href=${sessionData.invite_link+'?lead_id='+leadData._id}>Click here to join </a>
//       <br></br><br></br>
//       Look forward to showcasing the property to you.
//       <br></br>
//       Best regards,
//       <br></br>
//       ${username || 'PropVR' }</p>`;
//         Const inviteData = {
//           ToAddress: [leadData.email],
//           FromAddress: '<EMAIL>',
//           Message: message,
//           Subject: subject,
//           Project_name: projectname? projectname : '',
//           First_name: leadData.name,
//           Invite_link: sessionData.invite_link+'?lead_id='+leadData._id,
//           User_name: username? username : '',
//           Start: leadData.start_time,
//         };
//         Const mailsender = new SendgridModule();
//         Mailsender.SendMail(inviteData).then(() => {
//           Resolve({ status: 1, data: leadData });
//         }).catch((error) => {
//           Reject(error);
//         });

//       }).catch((err) => {
// Logger.error('Error while finding project name:', {message: err});
//         Reject(new Error('Error while finding project name: ' + err));
//       });
//     }).catch((error) => {
//  Logger.error('Error while fetching username: ', {message: error});
//       Reject(new Error('Error while fetching username: ' + error));
//     });
//   });
// }

export async function CreateLead (
  request: ExtendedRequest,
  response: Response,
): Promise<Leads | void> {
  const organization_id = (request.organization_id || request.body.organization_id) as string;
  const sessionId= request.body.session_id as string;
  const user_id = request.IsAuthenticated?.uid as string;
  let newLead;
  let sessionData: Session;
  if (request.body.type === 'unit' && !request.body.unit_id) {
    logger.error('No unit_id found for lead interested type-unit ');
    response
      .status(500)
      .json({ status: 0, error: 'No unit_id found for lead interested type-unit' });
  }
  if (sessionId) {
    const session = new SessionModule();
    sessionData = await session.getSessionById(sessionId);
    if (!sessionData) {
      response
        .status(500)
        .json({ status: 0, error: 'Session ID not found' });
    }
    if (organization_id !== sessionData.organization_id) {
      response
        .status(500)
        .json({ status: 0, error: 'Organization ID Mismatch' });
    }
    newLead = {
      session_id: sessionId,
      organization_id: organization_id,
      name: request.body.name,
      phone_number: request.body.phone_number,
      email: request.body.email,
      duration_minutes: sessionData.duration_minutes,
      joining_time: new Date().toISOString(),
      project_id: sessionData.project_id || undefined,
      type: request.body.type, // Storing type from request in authenticated requests
      unit_id: request.body.type !== 'project' ? request.body.unit_id : undefined,
      lead_source: request.body.source,
      lead_product_interest: request.body.lead_product_interest,
      lead_industry_type: request.body.lead_industry_type,
      start_time: sessionData.start,
      user_id: sessionData.user_id,
      lead_status: request.body.lead_status || LeadStatus.NEW,
      next_interaction_time: new Date().toISOString(),
      last_interaction_time: new Date().toISOString(),
      end_time: sessionData.end_time,
      lead_creation_time: new Date().toISOString(),
    };
  } else {
    newLead = {
      session_id: null,
      organization_id: organization_id,
      name: request.body.name,
      phone_number: request.body.phone_number,
      email: request.body.email,
      duration_minutes: null,
      joining_time: new Date().toISOString(),
      project_id: request.body.project_id || undefined,
      type: request.body.type,
      unit_id: request.body.type !== 'project' ? request.body.unit_id : undefined,
      lead_source: request.body.source,
      lead_product_interest: request.body.lead_product_interest,
      lead_industry_type: request.body.lead_industry_type,
      start_time: null,
      user_id: user_id,
      lead_status: request.body.lead_status || LeadStatus.NEW,
      next_interaction_time: new Date().toISOString(),
      last_interaction_time: new Date().toISOString(),
      end_time: null,
      lead_creation_time: new Date().toISOString(),
    };
  }
  const leads = new LeadsModule(organization_id);
  const task = new TaskModule();
  await leads
    .CreateLead(newLead)
    .then(async (leadData: Leads| void) => {
      if (leadData && sessionId){
      // Send invite mail
        // SendMail(organization_id, sessionData, leadData)
        //   .then(() => {
        //     Response
        //       .status(200)
        //       .json({ status: 1, data: leadData });
        //   })
        //   .catch((error) => {
        //     // Handle error

        //     Response
        //       .status(500)
        //       .json({ status: 0, error: 'Error while creating the lead'+ error });
        //   });
        const invite_url = process.env.BASE_URL+'mailer/sendGuestInvite';
        const invitationPayload = {
          session_id: leadData.session_id,
          leadData: leadData,
        };
        const inviteMailTime = new Date();
        inviteMailTime.setMinutes(inviteMailTime.getMinutes() + 1);
        const inviteMailTimeISO = inviteMailTime.toISOString();
        const inviteTaskId = leadData._id+'_invite';
        const inviteheaders = {};
        const queueName = 'mails';
        // Task_id will be leadId_invite for guests

        task.createTask(queueName, invite_url, invitationPayload, inviteMailTimeISO,
          inviteTaskId, inviteheaders).then(() => {
          response
            .status(200)
            .json({ status: 1, data: leadData });

          // Invalidate lead APIs (non-blocking)
          invalidateLeadAPIs().then((res) => {
            logger.info('Lead APIs invalidated successfully', { result: res });
          }).catch((err) => {
            logger.error('Error invalidating lead APIs', { error: err });
          });

        }).catch((error) => {
          // Handle error
          logger.error('Error while setting up invite mail task ', {message: error});
          response
            .status(500)
            .json({ status: 0, error: 'Error while setting up invite mail task'+ error });
        });

      } else {
        response
          .status(200)
          .json({ status: 1, data: leadData });

        // Invalidate lead APIs (non-blocking)
        invalidateLeadAPIs().then((res) => {
          logger.info('Lead APIs invalidated successfully', { result: res });
        }).catch((err) => {
          logger.error('Error invalidating lead APIs', { error: err });
        });
      }
    })
    .catch((error: Error) => {
      logger.error('Error while creating the lead ', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the lead'+ error });
    });
}

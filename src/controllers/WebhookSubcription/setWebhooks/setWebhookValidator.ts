import { check, body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const setWebhookValidate = [
  body('_id', 'ID is required')
    .notEmpty()
    .withMessage('ID cannot be empty')
    .isString()
    .withMessage('ID must be a string'),

  body('organization_id', ' organization Id is required')
    .notEmpty()
    .withMessage('organization Id cannot be empty')
    .isString()
    .withMessage('organization Id must be a string'),

  body('name')
    .optional()
    .notEmpty()
    .withMessage('Name cannot be empty')
    .isString()
    .withMessage('Name must be a string'),

  body('targetUrl')
    .optional()
    .notEmpty()
    .withMessage('Target URL cannot be empty')
    .isString()
    .withMessage('Target URL must be a string')
    .isURL()
    .withMessage('Target URL must be a valid URL'),

  // ListOfEvents must have at least one item
  body('listOfEvents')
    .optional()
    .notEmpty()
    .withMessage('List of events cannot be empty')
    .isArray()
    .withMessage('List of events must be an array')
    .custom((listOfEvents) => {
      if (!Array.isArray(listOfEvents) || listOfEvents.length === 0) {
        throw new Error('List of events must contain at least one event');
      }
      return true;
    }),

  check('listOfEvents')
    .optional()
    .isArray()
    .withMessage('listOfEvents must be an array')
    .custom((listOfEvents, { req }) => {
      if (!Array.isArray(listOfEvents)) {
        throw new Error('listOfEvents must be an array');
      }

      if (listOfEvents.length === 0) {
        throw new Error('listOfEvents cannot be empty');
      }

      // Validate each event in the array
      for (let i = 0; i < listOfEvents.length; i++) {
        const event = listOfEvents[i];

        if (typeof event !== 'string') {
          throw new Error(`listOfEvents[${i}] must be a string`);
        }

        // Check if event is empty or only whitespace
        const trimmedEvent = event.trim();
        if (trimmedEvent.length === 0) {
          throw new Error(`listOfEvents[${i}] cannot be an empty string`);
        }

        // Check if event contains commas (indicating comma-separated format)
        if (event.includes(',')) {
          throw new Error(
            `listOfEvents[${i}] contains commas. Use separate array elements like ['event1', 'event2'] instead of ['event1, event2']`,
          );
        }
      }

      if (Array.isArray(listOfEvents)) {
        const hasUnitEvent = listOfEvents.some((event: string) =>
          typeof event === 'string' && event.trim().startsWith('unit_'),
        );

        if (hasUnitEvent) {
          const rules = req.body.rules;
          if (!rules || typeof rules !== 'object') {
            throw new Error('rules is required when listOfEvents contains unit_ events');
          }
          if (!('allowed_projects' in rules)) {
            throw new Error('rules.allowed_projects is required when listOfEvents contains unit_ events');
          }
          if (!Array.isArray(rules.allowed_projects)) {
            throw new Error('rules.allowed_projects must be an array');
          }
          if (rules.allowed_projects.length === 0) {
            throw new Error('rules.allowed_projects cannot be empty when listOfEvents contains unit_ events');
          }
        }
      }
      return true;
    }),

  check('rules')
    .optional()
    .isObject()
    .withMessage('Rules must be an object')
    .custom((rules) => {
      if (rules && typeof rules === 'object') {
        if ('allowed_projects' in rules && !Array.isArray(rules.allowed_projects)) {
          throw new Error('rules.allowed_projects must be an array');
        }
      }
      return true;
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ status: 0, errors: errors.array() });
      return;
    }
    next();
  },
];

export default setWebhookValidate;

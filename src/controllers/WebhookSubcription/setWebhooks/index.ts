import { Request, Response } from 'express';
import { WebhookSubcriptionModule } from '../../../modules/webhookSubcription';
import logger from '../../../config/logger';
import { webhookData } from '../../../types/webhooks';
import { ProjectModule } from '../../../modules/projects';

export async function SetWebhook (request: Request, response: Response): Promise<webhookData | void> {
  const organization_id = request.body.organization_id as string;
  const webhook = new WebhookSubcriptionModule(organization_id);
  const project = new ProjectModule(organization_id);

  try {
    if (request.body.rules?.allowed_projects){
      const projectData = await project.GetListOfAllProjects();
      if (Object.keys(projectData).length > 0){
        const listOfAllProjects = Object.keys(projectData);
        const projectsInPayload= request.body.rules.allowed_projects;

        const invalidprojects = projectsInPayload.filter((proj: string) => {
          return !listOfAllProjects.includes(proj);
        });

        if (invalidprojects.length > 0) {
          throw new Error(`Invalid project IDs in allowed_projects: ${invalidprojects.join(', ')}`);
        }
      }
    }

    const body = request.body;
    webhook.setWebhook(body).then((res) => {
      response.status(200).json({ status: 1, data: res });
    })
      .catch((err) => {
        logger.error('Error in setWebhooks', {message: err});
        const errorMessage = err instanceof Error ? err.message : String(err);
        if (errorMessage.includes('Document doesnot exist')) {
          response.status(404).json({ status: 0, error: errorMessage });
        }  else {
          response.status(500).json({ status: 0, error: errorMessage });
        }
      });

  } catch (err){
    logger.error('Error in setWebhooks payload', {message: err});
    const errorMessage = err instanceof Error ? err.message : String(err);
    response.status(404).json({ status: 0, error: errorMessage });
  }

  return;
}

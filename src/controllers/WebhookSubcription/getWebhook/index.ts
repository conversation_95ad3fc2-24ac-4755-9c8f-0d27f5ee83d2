
import { Request, Response } from 'express';
import { WebhookSubcriptionModule } from '../../../modules/webhookSubcription';
import logger from '../../../config/logger';

export async function GetWebhook (
  request:Request,
  response:Response,
):Promise<void>{
  const { organization_id } = request.params;
  const eventType = request.query.eventType as string | undefined; // Optional query parameter
  const webhook = new WebhookSubcriptionModule(organization_id);

  try {
    const webhookList = await webhook.getWebhook(eventType);
    response.status(200).json({ status: 1, data: webhookList });
  } catch (err){
    logger.error('Error in getWebhook');
    response.status(500).json({ status: 0, message: 'Error while fetching the webhooks', error: err instanceof Error ? err.message : String(err)});
  }
}

import { Request, Response} from 'express';
import logger from '../../../config/logger';
import { WebhookSubcriptionModule } from '../../../modules/webhookSubcription';

export async function DeleteWebhook (
  request:Request,
  response:Response,
):Promise<void>{
  const organization_id = request.body.organization_id as string;
  const webhook_id = request.body._id as string;

  if (!webhook_id){
    response.status(400).json({
      status: 0,
      error: 'Missing required field: _id',
    });
    return;
  }
  const webhook = new WebhookSubcriptionModule(organization_id);

  webhook.deleteWebhook(webhook_id).then((res) => {
    if (res) {
      response.status(200).json({ status: 1, data: res });
    }
  })
    .catch((error) => {
      logger.error('Error in deleteWebhook', { message: error });
      const errorMessage = error instanceof Error ? error.message : String(error);
      response.status(500).json({
        status: 0,
        error: `Error while deleting the webhook: ${errorMessage}`,
      });
    });

}

import { Request, Response } from 'express';
import { WebhookEventsModule } from '../../../modules/webhooksEvents';
import { WebhookEventsData } from '../../../types/webhooks';
import logger from '../../../config/logger';

export async function ExecuteEvents (
  request: Request,
  response: Response,
): Promise<void> {
  try {
    const { _id, organization_id } = request.body;

    if (!_id || !organization_id) {
      response.status(400).json({
        status: 0,
        error: 'Missing required fields: _id and organization_id',
      });
      return;
    }

    const webhookEvent = new WebhookEventsModule(organization_id);

    const payload: WebhookEventsData = {
      _id: _id,
      webhook_id: request.body.webhook_id,
      organization_id: organization_id,
      eventType: request.body.eventType,
      targetUrl: request.body.targetUrl,
      data: request.body.data,
      log: request.body.log || [],
    };

    const result = await webhookEvent.executeEvents(payload);
    if (result){
      response.status(200).json({
        status: 1,
        message: 'Event executed successfully',
      });
    }
  } catch (error) {
    logger.error('Error in ExecuteEvents controller', { message: error });
    const errorMessage = error instanceof Error ? error.message : String(error);

    response.status(500).json({
      status: 0,
      error: `Error while executing the event: ${errorMessage}`,
    });
  }
}

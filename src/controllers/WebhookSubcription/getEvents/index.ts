import { Request, Response } from 'express';
import { WebhookEventsModule } from '../../../modules/webhooksEvents';
import logger from '../../../config/logger';

export async function GetEvents (
  request : Request,
  response : Response,
):Promise<void>{
  const organization_id = request.body.organization_id;
  const targetUrl = request.body.targetUrl;
  const webhookEvents = new WebhookEventsModule(organization_id);

  try {
    const webhookList = await webhookEvents.getEvents(organization_id, targetUrl);
    response.status(200).json({ status: 1, data: webhookList });
  } catch (err){
    logger.error('Error in getWebhook');
    response.status(500).json({ status: 0, message: 'Error while fetching the webhooks', error: err instanceof Error ? err.message : String(err)});
  }
}

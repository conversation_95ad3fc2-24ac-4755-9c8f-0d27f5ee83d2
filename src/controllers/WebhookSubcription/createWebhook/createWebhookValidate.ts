import { Request, Response, NextFunction } from 'express';
import { validationResult, body, check } from 'express-validator';

const createWebhookValidate = [
  body('organization_id', 'Organization ID is required')
    .notEmpty()
    .withMessage('Organization ID cannot be empty')
    .isString()
    .withMessage('Organization ID must be a string'),

  body('name', 'Name is required')
    .notEmpty()
    .withMessage('Name cannot be empty')
    .isString()
    .withMessage('Name must be a string'),

  body('targetUrl', 'Target URL is required')
    .notEmpty()
    .withMessage('Target URL cannot be empty')
    .isString()
    .withMessage('Target URL must be a string')
    .isURL()
    .withMessage('Target URL must be a valid URL'),

  check('rules')
    .optional()
    .isObject()
    .withMessage('Rules must be an object')
    .custom((rules) => {
      if (rules && typeof rules === 'object') {
        if ('allowed_projects' in rules) {
          if (!Array.isArray(rules.allowed_projects)) {
            throw new Error('rules.allowed_projects must be an array');
          }
        }
      }
      return true;
    }),

  check('listOfEvents')
    .optional()
    .isArray()
    .custom((listOfEvents, { req}) => {
      if (!Array.isArray(listOfEvents)) {
        throw new Error('listOfEvents must be an array');
      }

      // Check if array is empty
      if (listOfEvents.length === 0) {
        throw new Error('listOfEvents cannot be empty');
      }

      // Validate each event in the array
      for (let i = 0; i < listOfEvents.length; i++) {
        const event = listOfEvents[i];

        // Check if event is a string
        if (typeof event !== 'string') {
          throw new Error(`listOfEvents[${i}] must be a string`);
        }

        // Check if event is empty or only whitespace
        const trimmedEvent = event.trim();
        if (trimmedEvent.length === 0) {
          throw new Error('listOfEvents cannot have an empty string');
        }

        // Check if event contains commas (indicating comma-separated format)
        if (event.includes(',')) {
          throw new Error(
            `listOfEvents[${i}] contains commas. Use separate array elements like ['event1', 'event2'] instead of ['event1, event2']`,
          );
        }
      }

      if (Array.isArray(listOfEvents)) {
        const hasUnitEvent = listOfEvents.some((event: string) =>
          typeof event === 'string' && event.trim().startsWith('unit_'),
        );

        if (hasUnitEvent) {
          const rules = req.body.rules;
          if (!('allowed_projects' in rules)) {
            throw new Error('rules.allowed_projects is required when listOfEvents contains unit_ events');
          }
          if (!Array.isArray(rules.allowed_projects)) {
            throw new Error('rules.allowed_projects must be an array');
          }
          if (rules.allowed_projects.length === 0) {
            throw new Error('rules.allowed_projects cannot be empty when listOfEvents contains unit_ events');
          }
        }
      }
      return true;
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ status: 0, errors: errors.array() });
      return;
    }
    next();
  },
];

export default createWebhookValidate;

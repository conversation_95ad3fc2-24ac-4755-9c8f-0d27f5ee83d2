import { Request, Response } from 'express';
import { WebhookSubcriptionModule } from '../../../modules/webhookSubcription';
import  mongoose from 'mongoose';
import logger from '../../../config/logger';
import { webhookData } from '../../../types/webhooks';

export async function CreateWebhook (
  request: Request,
  response: Response,
): Promise<webhookData | void> {

  const organization_id = request.body.organization_id as string;
  const webhook = new WebhookSubcriptionModule(organization_id);

  try {
    const _id = new mongoose.Types.ObjectId();
    const data = {
      _id: _id,
      ...request.body,
    };

    const result = await webhook.createWebhook(data);

    response.status(201).json({ status: 1, message: 'Webhook created successfully', data: result });
  } catch (error) {
    logger.error('Error in createWebhook', { message: error });
    const errorMessage = error instanceof Error ? error.message : String(error);
    response.status(500).json({ status: 0, error: `Error while creating the webhook: ${errorMessage}` });
  }
}

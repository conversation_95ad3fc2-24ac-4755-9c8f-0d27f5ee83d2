import { TaskModule } from '../../../modules/tasks';
import logger from '../../../config/logger';

export async function triggerWebhook<T> (
  eventType: string,
  targetUrl: string,
  organization_id: string,
  data: T,
  taskId : string,
): Promise<object | null> {
  if (!eventType || !targetUrl || !organization_id || data === undefined || data === null || !taskId) {
    logger.error('Missing required fields in triggerWebhook', {
      eventType,
      targetUrl,
      organization_id,
      taskId,
      hasData: data !== undefined && data !== null,
    });
    throw new Error('Missing required fields: eventType, targetUrl, organization_id, data');
  }

  try {
    const task = new TaskModule();
    const queueName = 'Webhooks';

    const webhookMessage = {
      event: eventType,
      data: data,
      timestamp: new Date().toISOString(),
    };

    await task.createTask(
      queueName,
      targetUrl,
      webhookMessage,
      new Date().toISOString(),
      taskId,
      {},
    );

    logger.info('Webhook queued', {
      eventType,
      organization_id,
      targetUrl,
      taskId,
    });

    return { success: true, taskId: taskId};
  } catch (err) {
    logger.error('Error queuing webhook', {
      error: err instanceof Error ? err.message : String(err),
      eventType,
      organization_id,
    });
    return { success: false, taskId: taskId, error: err instanceof Error ? err.message : String(err) };
  }
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const createEventValidate = [
  body('organization_id', 'Organization ID is required')
    .notEmpty()
    .withMessage('Organization ID cannot be empty')
    .isString()
    .withMessage('Organization ID must be a string'),

  body('webhook_id', 'webhook ID is required')
    .notEmpty()
    .withMessage('webhook ID cannot be empty')
    .isString()
    .withMessage('webhook ID must be a string'),

  body('eventType', 'eventType is required')
    .notEmpty()
    .withMessage('eventType cannot be empty')
    .isString()
    .withMessage('eventType must be a string'),

  body('payload', 'payload is required')
    .notEmpty()
    .withMessage('payload cannot be empty')
    .custom((value) => {
      if (value === null || value === undefined) {
        throw new Error('payload cannot be null or undefined');
      }
      return true;
    }),

  (req: Request, res :Response, next : NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()){
      res.status(400).json({ status: 0, errors: errors.array()});
    }
    next();
  },
];

export default createEventValidate;

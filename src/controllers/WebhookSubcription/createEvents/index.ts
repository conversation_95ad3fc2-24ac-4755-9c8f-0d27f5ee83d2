import { Request, Response } from 'express';
import { WebhookEventsModule } from '../../../modules/webhooksEvents';
import { WebhookEventsData } from '../../../types/webhooks';
import logger from '../../../config/logger';

export async function CreateEvents (
  request:Request,
  response : Response,
):Promise<WebhookEventsData|void>{

  const organization_id = request.body.organization_id as string;
  const webhookEvent = new WebhookEventsModule(organization_id);

  try {
    const logs: Array<{
      response: object;
      message: string;
      time: string;
    }> = [];

    const payload = {
      _id: request.body._id,
      webhook_id: request.body.webhook_id,
      organization_id: request.body.organization_id,
      eventType: request.body.eventType,
      targetUrl: request.body.targetUrl,
      data: request.body.payload,
      log: logs,
    };

    const result = await webhookEvent.createEvents(payload as WebhookEventsData) as WebhookEventsData | null;
    if (result){
      response.status(200).json({status: 1, data: result});
    }
    return;

  } catch (error){
    logger.error('Error in createEvents', { message: error });
    const errorMessage = error instanceof Error ? error.message : String(error);
    response.status(500).json({ status: 0, error: `Error while creating the events: ${errorMessage}` });
  }

}

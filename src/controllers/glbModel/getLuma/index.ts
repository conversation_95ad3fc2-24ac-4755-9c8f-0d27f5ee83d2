import { Request, Response } from 'express';
import { ModelModule } from '../../../modules/glbModel';
import logger from '../../../config/logger';

export async function GetLuma (
  request: Request,
  response: Response,
): Promise<void> {
  try {

    const project_id = request.body.project_id;
    const model_id = request.body.id;
    const organization_id = request.headers.organization as string;
    const model = new ModelModule(project_id, organization_id);

    const lumaModel = await model.getLumaModel(model_id);
    response.status(200).send({ status: 1, data: lumaModel });
  } catch (error) {
    logger.error(' Error in getLuma', {message: error});
    response.status(500).send('Server Error');
  }
}

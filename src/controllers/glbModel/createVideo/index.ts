import { Request, Response } from 'express';
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg';
import ffmpeg from 'fluent-ffmpeg';
import path from 'path';
import { ModelModule } from '../../../modules/glbModel';
import fetch, {Headers} from 'node-fetch';
import fs from 'fs';
import mongoose from 'mongoose';
import { glbmodel } from '../../../types/glbModels';
import axios from 'axios';
import logger from '../../../config/logger';
import https from 'https';
import url from 'url';

ffmpeg.setFfmpegPath(ffmpegInstaller.path);

interface artifactData{
    url:string,
    type:string
}

async function deleteFile (filePath:string) {
  fs.unlink(filePath, (err) => {
    if (err) {
      logger.error(`Error deleting file at ${filePath}:`, {message: err});
      console.error(`Error deleting file at ${filePath}:`, err);
    } else {
      logger.info(`Successfully deleted file at ${filePath}`);

    }
  });
}

// Async function OptimizeBlender (project_id:string, organization_id:string, model_id:string, luma_url:string){
//     Console.log("OptimizeBlender")
//     Return new Promise((resolve, reject) => {
//       Console.log(project_id, organization_id, model_id, luma_url);
//       Const myHeaders = new Headers();
//       MyHeaders.append('Content-Type', 'application/json');

//       Const raw = JSON.stringify({
//         'name': 'chair',
//         'lumaUrl': luma_url,
//         'project_id': project_id,
//         'organization_id': organization_id,
//         'model_id': model_id,
//       });

//       Const requestOptions = {
//         Method: 'POST',
//         Headers: myHeaders,
//         Body: raw,
//       };

//       Console.log("/convertglb")
//       Fetch(process.env.LUMA_API_URL+'/convertgltf', requestOptions)
//         .then((response) => {
//           Console.log('response');
//           Console.log(response);
//           Return response.json();
//         }).then((responseData:blenderResponse) => {
//           If (responseData.status===1){
//             Console.log(responseData.output);
//             Resolve(responseData.output);
//           }
//         })
//         .catch((error) => {
//           Console.log('catch');
//           Console.error(error);
//           Reject();
//         });
//     });
//   }

async function OptimizeBlender (project_id:string, organization_id:string, model_id:string, luma_url:string) {
  console.log('-------------------------Calling OptimizeBlender-----------------------------------');

  return new Promise((resolve, reject) => {
    console.log(project_id);
    console.log( organization_id);
    console.log(model_id);
    console.log(luma_url);

    const raw = {
      'name': 'chair',
      'lumaUrl': luma_url,
      'project_id': project_id,
      'organization_id': organization_id,
      'model_id': model_id,
    };

    console.log(process.env.LUMA_API_URL + '/convertgltf');

    const requestOptions = {
      method: 'POST',
      url: process.env.LUMA_API_URL + '/convertgltf',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      data: raw,
      // Timeout: 100000, // Optional: set a timeout for the request
    };

    console.log('/convertgltf request options:', requestOptions);

    axios(requestOptions)
      .then((response) => {
        console.log('response:', response);
        const responseData = response.data;
        console.log('responseData:', responseData);
        if (responseData.status === 1) {
          console.log(responseData.output);
          resolve(responseData.output);
        } else {
          console.log('Non-success status:', responseData.status);
          reject(new Error('Non-success status received'));
        }
      })
      .catch((error) => {
        logger.error('Error', {message: error});
        reject(error);
      });
  });
}

// Create Capture
async function createCapture (name: string) {
  const myHeaders = new Headers();
  myHeaders.append('Authorization', process.env.LUMA_API_KEY as string);
  myHeaders.append('Content-Type', 'application/x-www-form-urlencoded');

  const urlencoded = new URLSearchParams();
  urlencoded.append('title', name);
  urlencoded.append('camModel', 'normal');

  const requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: urlencoded,
    redirect: 'follow' as RequestRedirect,
  };

  try {
    const response = await fetch(
      'https://webapp.engineeringlumalabs.com/api/v2/capture',
      requestOptions,
    );

    if (response.ok) {
      const result = await response.json();
      console.log('In CreateCapture', result);
      const source = result.signedUrls.source;
      const slug = result.capture.slug;
      return { source, slug };
    }
    throw new Error('Invalid response from createCapture');
  } catch (error) {
    logger.error('Error in createCapture', {message: error});

    throw error;
  }
}

// Upload Capture For Video Files Only
async function UploadCapture (sourceURL: string, paths: string, contentType:string) {
  const myHeaders = new Headers();
  myHeaders.append('Content-Type', contentType);
  const fileData = await fs.readFileSync(paths);
  const requestOptions = {
    method: 'PUT',
    headers: myHeaders,
    body: fileData,
    redirect: 'follow' as RequestRedirect,
  };

  try {
    const response = await fetch(sourceURL, requestOptions);
    if (response.ok) {
      return response.status;
    }
    throw new Error('Invalid response from UploadCapture');
  } catch (error) {
    logger.error('Error in UploadCapture', {message: error});

    throw error;
  }
}

// Upload Captures For Zip Files Only
async function UploadCaptures (sourceURL: string, paths:string, contentType:string) {
  const fileStream = fs.createReadStream(paths);
  const parsedUrl = url.parse(sourceURL);
  const options = {
    hostname: parsedUrl.hostname,
    path: parsedUrl.path,
    method: 'PUT',
    headers: {
      'Content-Type': contentType, // Change based on your file type
    },
  };
  return new Promise((resolve, reject) => {
    try {
      const req = https.request(options, (res) => {
        console.log(`STATUS: ${res.statusCode}`);
        const statusCode = res.statusCode ?? 500;
        res.on('data', (chunk) => {
          console.log(`BODY: ${chunk}`);
        });

        res.on('end', () => {
          if (statusCode >= 200 && statusCode < 300) {
            resolve(res.statusCode);
          } else {
            reject(new Error(`Upload failed with status code: ${res.statusCode}`));
          }
        });
      });

      req.on('error', (e) => {
        console.error(`Problem with request: ${e.message}`);
        reject(e);
      });
      fileStream.pipe(req);

      fileStream.on('end', () => {
        req.end();
      });

    } catch (error) {
      logger.error('Error in Upload Captures Zip: ', {message: error});
      reject(error);
    }
  });
}

// Trigger Capture
async function TriggerCapture (slug: string) {
  const myHeaders = new Headers();
  myHeaders.append('Authorization', process.env.LUMA_API_KEY as string);

  const requestOptions = {
    method: 'POST',
    headers: myHeaders,
    redirect: 'follow' as RequestRedirect,
  };

  try {
    const response = await fetch(
      `https://webapp.engineeringlumalabs.com/api/v2/capture/${slug}`,
      requestOptions,
    );
    if (response.ok) {
      const result = await response.json();
      return result;
    }
    throw new Error('Invalid response from TriggerCapture');
  } catch (error) {
    logger.error('Error in TriggerCapture', {message: error});

    throw error;
  }
}

// Get Capture
async function getCapture (slug: string) {
  const myHeaders = new Headers();
  myHeaders.append('Authorization', process.env.LUMA_API_KEY as string);

  const requestOptions = {
    method: 'GET',
    headers: myHeaders,
    redirect: 'follow' as RequestRedirect,
  };

  try {
    const response = await fetch(
      `https://webapp.engineeringlumalabs.com/api/v2/capture/${slug}`,
      requestOptions,
    );
    console.log('Response status ', response.status);
    if (response.status===200) {
      console.log(response.status);
      console.log('Inside GetCapture response.ok');
      const result = await response.json();
      return result;
    }
    console.log(`Invalid response from GetCapture for the slug ${slug}`);

    throw new Error('Invalid response from GetCapture');

  } catch (error) {
    logger.error('Error in GetCapture', {message: error});
    throw error;
  }
}

// Check Progress
async function CheckProgress (projectId:string, orgId:string, modelId:string, slug:string){
  console.log('check progress calling');
  const interval = setInterval(async () => {
    try {
      const outputFromGetCapture = await getCapture(slug);
      const progress = outputFromGetCapture.latestRun.progress;
      const status = outputFromGetCapture.latestRun.status;

      const project_id = projectId;
      const organization_id = orgId;
      const model = new ModelModule(project_id, organization_id);
      let lumaData = {
        model_id: modelId,
        progress: progress,
        status: 'Uploading',
        url: '',
        thumbnail: '',
      };
      await model.updateProgress(lumaData);
      // Console.log('STATUS ', status);
      if ((progress===100 || status==='finished') && (status!=='failed' || status!=='dispatched')){
        // Console.log('progress');
        // Console.log(progress);
        // Console.log('finsied');
        // Console.log(status);
        clearInterval(interval);
        console.log('Interval clear');
        const artifacts:{[key:string]:artifactData} = outputFromGetCapture.latestRun.artifacts;
        const glbData = Object.values(artifacts).find((artifactData:artifactData) => {
          return artifactData && artifactData.type === 'textured_mesh_glb';
        });
        const thumbnail = Object.values(artifacts).find((artifactData:artifactData) => {
          return artifactData && artifactData.type === 'preview_360';
        });
        const Url = glbData ? glbData.url:'';
        const thumbnailUrl = thumbnail?thumbnail.url:'';
        console.log('---------------------------------URL-------------------------------');
        console.log(Url);
        console.log('---------------------------------THUMB-------------------------------');
        console.log(thumbnail);
        // OptimizeBlender(project_id, organization_id, modelId, url).then(async (data) => {

        // }).catch((error) => {
        //   Console.log('Error in Optimizse Blender ', error);
        //   Throw new Error(`Error from Optimizise Blender ${error}`);
        // });
        lumaData = {
          model_id: modelId,
          progress: 100,
          status: 'Blender processing',
          url: Url as string,
          thumbnail: thumbnailUrl as string,
        };
        console.log(lumaData);
        console.log('==========================FinishProgress Luma Process=======================');
        await model.updateProgress(lumaData);
        await OptimizeBlender(projectId, organization_id, modelId, Url);
      } else if (status==='failed'){
        console.log('Failed');
        throw new Error(outputFromGetCapture.errorReason);
        clearInterval(interval);
      }
    } catch (error){
      logger.error('Check Progress catch', {message: error});

      clearInterval(interval);
    }
  }, 300000);
}

async function downloadFile (fileUrl: string, outputLocationPath: string) {
  console.log(fileUrl);
  const response = await fetch(fileUrl);

  if (!response.ok) {
    throw new Error(`Failed to download file, status ${response.status}`);
  }
  console.log(outputLocationPath);
  const fileStream = fs.createWriteStream(outputLocationPath);

  return new Promise((resolve, reject) => {
    response.body.on('error', (err) => {
      reject(err);
    }).pipe(fileStream);

    fileStream.on('finish', () => {
      resolve(true);
    });

    fileStream.on('error', (err) => {
      logger.error('Error:', {message: err});
      reject(err);
    });
  });

}

export async function CreateVideo (
  request: Request,
  response: Response,
):Promise<void>{
  console.log('sdkfmldmflsdmflksdn');
  const name = request.body.filename;
  const fileUrl = request.body.fileUrl;
  console.log(request.body);

  try {
    const project_id = request.body.project_id;
    const organization_id = request.headers.organization as string;
    // Check if the directory exists, and create it if it doesn't
    if (!fs.existsSync('output')) {
      console.log('Output');
      fs.mkdirSync('output', { recursive: true });
    }

    const outputZip = path.join('./output', `${name}`+'_'+new Date().getTime()+'.zip');
    const outputMP4 = path.join('./output', `${name}`+'_'+new Date().getTime()+'.mp4');
    const outputMOV = path.join('./output', `${name}`+'_'+new Date().getTime()+'.mov');
    const { source: sourceURL, slug } = await createCapture(name);
    const model = new ModelModule(project_id, organization_id);

    const lumaModelData = {
      _id: new mongoose.Types.ObjectId(),
      name: name,
      status: 'uploading',
      luma_id: slug,
      progress: 5,
      type: 'luma',
      url: '',
      description: '',
      thumbnail: '',
      meshes: {},
    };
    const initialCaptureData:glbmodel | null = await model.createLumaModel(lumaModelData);
    console.log('slugId '+slug);
    return new Promise((resolve, reject) => {
      if (request.body.type==='Video'){
        downloadFile(fileUrl, outputMP4).then((pathWay) => {
          console.log('Path ', pathWay);
          ffmpeg(outputMP4)
            .output(outputMOV)
            .on('end', async () => {
              console.log('conversion completed', outputMOV);

              // Const file = request.files as UploadedFiles | undefined;

              const uploadStatus = await UploadCapture(sourceURL, outputMOV, 'video/quicktime');
              console.log('Upload Status:', uploadStatus);

              const captureData = await TriggerCapture(slug);
              console.log(captureData);

              if (initialCaptureData){
                const modelId:string = initialCaptureData._id.toString();
                await CheckProgress(project_id, organization_id, modelId, slug);
                deleteFile(outputMP4);
                deleteFile(outputMOV);
                resolve();
                response.status(200).send({
                  status: 1,
                  _id: initialCaptureData._id.toString(),
                  lumaId: slug,
                });
              }

              // Const lumaModelData = {
              //   _id: new mongoose.Types.ObjectId(),
              //   Name: name,
              //   Status: 'uploading',
              //   Luma_id: slug,
              //   Progress: 0,
              //   Type: 'luma',
              //   Url: '',
              //   Description: '',
              //   Thumbnail: '',
              //   Meshes: {},
              // };

              // Model.createLumaModel(lumaModelData).then(async (data:glbmodel | void) => {
              //   If (data && data._id){
              //     Const modelId:string = data._id.toString();
              //     Await CheckProgress(project_id, organization_id, modelId, slug);
              //     DeleteFile(outputMP4);
              //     DeleteFile(outputMOV);
              //     Resolve();
              //     Response.status(200).send({
              //       Status: 1,
              //       _id: data._id,
              //       LumaId: slug,
              //     });
              //   }
              // });

            })
            .on('error', (err) => {
              logger.error('Error during conversion:', {message: err});
              reject();
            })
            .run();
        });
      } else {
        downloadFile(fileUrl, outputZip).then(async () => {
          const uploadStatus = await UploadCaptures(sourceURL, outputZip, 'application/zip');
          console.log('Upload Status:', uploadStatus);

          const captureData = await TriggerCapture(slug);
          console.log(captureData);

          if (initialCaptureData){
            const modelId:string = initialCaptureData._id.toString();
            console.log('ModelID '+modelId);
            await CheckProgress(project_id, organization_id, modelId, slug);
            deleteFile(outputZip);
            resolve();
            response.status(200).send({
              status: 1,
              _id: modelId,
              lumaId: slug,
            });
          }
        });
      }
    });
  } catch (error) {
    logger.error('Error in CreateLuma', {message: error});
    response.status(500).send({status: 0, message: 'Server Error'});
  }

}

import { glbmodel } from '../../../types/glbModels';
import { Request, Response } from 'express';
import { ModelModule } from '../../../modules/glbModel';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';

export async function UpdateLuma (
  request: Request,
  response: Response,
): Promise<glbmodel | void> {
  const requestFiles = request.files;
  const requestData = request.body;

  const project_id = requestData.project_id as string;
  const organization_id = requestData.organization_id as string;
  const model = new ModelModule(project_id, organization_id);

  let updateLumaData: { model_id: string; download_url: string;status: string } | undefined;

  try {
    // Console.log(requestData);
    // Console.log(requestFiles);

    if (requestFiles) {
      console.log(requestFiles);
      const urlObject = await UploadUnitplanFiles(
        requestFiles,
        model.storagepath + requestData.model_id,
      );

      updateLumaData = {
        model_id: requestData.model_id,
        download_url: urlObject.file,
        status: requestData.status,
      };

      console.log('Finished uploadUnitplanFiles');
    }

    if (updateLumaData) {
      const UpdatingLumaDownloadUrl = await model.updateDownloadUrl(
        updateLumaData,
      );

      console.log('Finished updating DB');
      response.send({
        status: 1,
        data: UpdatingLumaDownloadUrl,
      });
    } else {
      response.status(400).send('No files to upload');
    }
  } catch (error) {
    logger.error('Error in UpdateLuma', {message: error});

    response.status(500).send('Server Error in UpdateLuma');
  }
}

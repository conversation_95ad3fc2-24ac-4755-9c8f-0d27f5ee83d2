import { Request, Response } from 'express';
import axios from 'axios';
import { ModelModule } from '../../../modules/glbModel';
import logger from '../../../config/logger';

export async function OptimizeBlender (
  request: Request,
  response: Response):Promise<void> {
  console.log('OptimizeBlender');
  try {
    const project_id = request.body.projectId;
    const organization_id = request.body.organizationId;
    const model_id = request.body.modelId;
    const luma_url = request.body.lumaUrl;
    const model = new ModelModule(project_id, organization_id);
    console.log(project_id, organization_id, model_id, luma_url);

    const raw = {
      'name': 'chair',
      'lumaUrl': luma_url,
      'project_id': project_id,
      'organization_id': organization_id,
      'model_id': model_id,
    };

    const requestOptions = {
      method: 'POST',
      url: process.env.LUMA_API_URL + '/convertgltf',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      data: raw,
    };

    console.log('/convertgltf request options:', requestOptions);

    axios(requestOptions)
      .then(async (responseData) => {
        console.log('response:', responseData);
        if (responseData.status === 200) {
          let lumaData = {
            model_id: model_id,
            status: 'Uploading',
            progress: 99,
            url: '',
          };
          lumaData = {
            model_id: model_id,
            progress: 100,
            status: 'Finished',
            url: luma_url as string,
          };
          console.log(lumaData);
          console.log('==========================FinishProgress=======================');
          await model.updateProgress(lumaData);
          response.status(200).send({status: 1, message: 'Model baked successfully'});
        } else {
          console.log('Non-success status:', responseData.status);
          // Reject(new Error('Non-success status received'));
          response.status(500).send({status: 0, message: 'Error in baking'});
        }
      })
      .catch((error) => {
        if (error.code === 'ECONNABORTED') {
          logger.error('Request timeout');
          console.error('Request timeout');
        } else {
          logger.error('Request error', {message: error});
          console.error('Request error:', error.message);
        }
      });

  } catch (error) {
    logger.error('Error in optimizing blender', {message: error});
    response.status(500).send({status: 0, message: 'Error in optimizing blender'});
  }
}

import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
interface UploadedFiles {
    texture_thumbnail: Express.Multer.File[];
    actual_texture_thumbnail: Express.Multer.File[];
}

const createTextureValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;

  if (files) {
    if (!files.texture_thumbnail || !files.actual_texture_thumbnail) {
      res.status(400).send({status: 0, error: 'Texture Thumbnail images field is required.'});
    } else {
      const requiredTextFields = [
        'name',
        'mesh_id',
        'project_id',
        'model_id',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).send({status: 0, error: `Missing text fields: ${missingTextFields.join(', ')}`});
      } else {
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).send({status: 0, error: 'Invalid file structure in the request.'});
  }
};

export default createTextureValidate;

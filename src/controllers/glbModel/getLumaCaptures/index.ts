import { glbmodel } from '../../../types/glbModels';
import { Request, Response } from 'express';
import { ModelModule } from '../../../modules/glbModel';
import logger from '../../../config/logger';

export async function getLumaCaptures (
  request: Request,
  response: Response,
): Promise<glbmodel | void> {
  try {
    const requestData = request.body;
    console.log('id', requestData);

    const project_id = requestData.project_id;
    const organization_id = request.headers.organization as string;

    // Console.log(project_id);
    const model = new ModelModule(project_id, organization_id);
    // Console.log('before getting db');

    const lumaModel = await model.getLumaModelsWithTypeLuma();

    // Console.log('after getting db');
    response.status(200).send({ status: 1, data: lumaModel });
  } catch (error) {
    logger.error('Unable to retrieve data', {message: error});
    response.status(400).send('Unable to retrieve data');
  }
}

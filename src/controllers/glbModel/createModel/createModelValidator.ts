import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
interface UploadedFiles {
  thumbnail: Express.Multer.File[];
  file: Express.Multer.File[];
}

const createModelValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.files);
  console.log(req.body);

  const files = req.files as UploadedFiles | undefined;

  if (files) {
    if (!files.thumbnail || !files.file) {
      res.status(400).send({status: 0, error: 'Thumbnail and file image field is required.'});
    } else {
      const requiredTextFields = [
        'project_id',
        'name',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).send({status: 0, error: `Missing text fields: ${missingTextFields.join(', ')}`});
      } else {
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).send({status: 0, error: 'Invalid file structure in the request.'});
  }
};

export default createModelValidate;

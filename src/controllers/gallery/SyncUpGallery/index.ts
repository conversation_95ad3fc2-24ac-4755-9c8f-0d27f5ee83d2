import logger from '../../../config/logger';
import { Request, Response } from 'express';
import { DataSyncUpModule } from '../../../modules/dataSyncUp';
import { invalidateGalleryAPIs } from '../../../modules/gallery';

export async function SyncUpGalleryData (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.params.project_id as string;
  const organization_id = request.headers.organization as string;

  const syncUp = new DataSyncUpModule();

  const result = await syncUp.GallerySyncUp(organization_id, project_id);
  if (result) {
    response.status(200).json({ status: 1, message: result });

    invalidateGalleryAPIs(organization_id, project_id).then((res) => {
      logger.info('Gallery APIs invalidated successfully', { result: res });
    }).catch((err) => {
      logger.error('Error invalidating gallery APIs', { error: err });
    });
  } else {
    logger.error('Error in Sync Gallery', {message: result});
    response.status(400).json({ status: 0, error: result });
  }
}

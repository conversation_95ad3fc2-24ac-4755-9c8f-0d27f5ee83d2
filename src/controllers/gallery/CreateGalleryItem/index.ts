import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { galleryItem } from '../../../types/gallery';
import { GalleryModule, invalidateGalleryAPIs } from '../../../modules/gallery';
import { MediaType } from '../../../types/amenity';
import logger from '../../../config/logger';

export async function createGalleryItem (
  request: Request,
  response: Response,
): Promise<galleryItem | void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const gallery = new GalleryModule(project_id, organization_id);
  const id = new mongoose.Types.ObjectId();
  if (!request.files && request.body.type !== MediaType.LINK) {
    logger
      .error('file not found:');
    response.send({ status: 0, error: 'file not found' });
    return;
  }
  if (request.files && request.body.type !== MediaType.LINK && request.body.type !== MediaType.VIRTUAL_TOUR) {
    UploadUnitplanFiles(request.files, gallery.storagepath+id)
      .then((urlObject: { [key: string]: string }) => {
        const createGalleryItemData = {
          id: id,
          name: request.body.name,
          category: request.body.category,
          type: request.body.type,
          url: urlObject.file,
          thumbnail: urlObject.thumbnail,
        };
        gallery.CreateGalleryItem(createGalleryItemData)
          .then((galleryItemData) => {
            response.status(201).json({ status: 1, data: galleryItemData });

            invalidateGalleryAPIs(organization_id, project_id).then((res) => {
              logger.info('Gallery APIs invalidated successfully', { result: res });
            }).catch((err) => {
              logger.error('Error invalidating gallery APIs', { error: err });
            });
          })
          .catch((error: Error) => {
            logger
              .error('Error while creating the gallery item:', {message: error});
            response
              .status(500)
              .json({ status: 0, error: 'Error while creating the gallery item'+ error });
          });
      });
  } else {
    const createGalleryItemData = {
      id: id,
      name: request.body.name,
      category: request.body.category,
      type: request.body.type,
      link: request.body.link,
      ...(request.body.type === MediaType.LINK && { link: request.body.link }),
      ...(request.body.type === MediaType.VIRTUAL_TOUR && { tour_id: request.body.tour_id }),
    };
    gallery.CreateGalleryItem(createGalleryItemData)
      .then((galleryItemData) => {
        response.status(201).json({ status: 1, data: galleryItemData });

        invalidateGalleryAPIs(organization_id, project_id).then((res) => {
          logger.info('Gallery APIs invalidated successfully', { result: res });
        }).catch((err) => {
          logger.error('Error invalidating gallery APIs', { error: err });
        });
      })
      .catch((error: Error) => {
        logger
          .error('Error while creating the gallery item:', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error while creating the gallery item'+ error });
      });
  }
}

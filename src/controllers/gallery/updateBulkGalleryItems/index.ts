import logger from '../../../config/logger';
import { GalleryModule, invalidateGalleryAPIs } from '../../../modules/gallery';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export async function updateBulkGalleryItems (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const galleryMod = new GalleryModule(project_id, organization_id);
  galleryMod.updateBulkGalleryItems(request.body)
    .then((res) => {
      response.status(201).json({ status: 1, message: res });

      invalidateGalleryAPIs(organization_id, project_id).then((invalidationResult) => {
        logger.info('Gallery APIs invalidated successfully', { result: invalidationResult });
      }).catch((invalidationError) => {
        logger.error('Error invalidating gallery APIs', { error: invalidationError });
      });
    })
    .catch((error: Error) => {
      logger
        .error('Error in updateBulkGalleryItems', {message: error});
      response
        .status(500)
        .json({ status: 0, error: error});
    });
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, query, header } from 'express-validator';

const getGalleryValidator = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  query('project_id', 'Project ID is required').notEmpty(),
  query('category').optional().isString(),
  query('search').optional().isString(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default getGalleryValidator;

import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { projectSVGType } from '../../../types/projectSVG';

interface UploadedFiles {
  svgFile: Express.Multer.File[];
}

const CreateSVGValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  if (files) {
    // If (!files.svgFile) {
    //   Res.status(400).json({ error: 'SVGFile field is required.' });
    // } else {
    const requiredTextFields = ['type', 'scene_id', 'project_id'];

    const missingTextFields = requiredTextFields.filter(
      (field) => !(field in req.body),
    );

    if (missingTextFields.length > 0) {
      res.status(400).json({
        error: `Missing text fields: ${missingTextFields.join(', ')}`,
      });
    } else {
      body(
        'type',
        'Invalid type value. Please ensure that you are using a valid type value',
      )
        .isIn(Object.values(projectSVGType))
        .run(req);
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        console.log(errors);
      } else {
        next();
      }
    }
    // }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default CreateSVGValidate;

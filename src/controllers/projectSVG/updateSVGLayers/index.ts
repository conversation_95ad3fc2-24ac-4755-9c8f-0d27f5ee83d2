import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { ProjectSVGModule } from '../../../modules/projectSVG';
import { invalidateSceneAPIs } from '../../../modules/projectScene';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
export async function updateSVGLayers (
  request: FileRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const projectSVG = new ProjectSVGModule(request.body.project_id, organization_id);
  let urlobject;
  if (request.files){
    urlobject =await UploadUnitplanFiles(request.files, projectSVG.storagepath+request.body.layer_id);
  }
  projectSVG.createLayers(request.body, request.body.layer_id, urlobject?.svgFile).then((res) => {
    response.send({status: 1, data: res});

    // Invalidate project scene APIs (non-blocking)
    invalidateSceneAPIs(organization_id, request.body.project_id).then((invalidateRes) => {
      logger.info('Project scene APIs invalidated successfully', { result: invalidateRes });
    }).catch((err) => {
      logger.error('Error invalidating project scene APIs', { error: err });
    });
  })
    .catch((err) => {
      logger.error('Error in updateupdateSVGLayersLayers', {message: err});
      response.send({status: 0, meassage: err});
    });

}

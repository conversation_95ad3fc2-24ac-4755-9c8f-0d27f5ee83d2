import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';

export default function editSVGValidator (req: Request, res: Response, next: NextFunction): void {
  // Validation rules
  const validationRules = [
    body('svg_id')
      .notEmpty()
      .withMessage('svg_id is required')
      .isMongoId()
      .withMessage('svg_id must be a valid MongoDB ObjectId'),

    body('minZoomLevel')
      .optional()
      .isNumeric()
      .withMessage('minZoomLevel must be a number')
      .isFloat({ min: 0, max: 100 })
      .withMessage('minZoomLevel must be between 0 and 100'),

    body('maxZoomLevel')
      .optional()
      .isNumeric()
      .withMessage('maxZoomLevel must be a number')
      .isFloat({ min: 0, max: 100 })
      .withMessage('maxZoomLevel must be between 0 and 100'),

    body('applyOnLayers') // For deepzoom functionality
      .optional()
      .isBoolean()
      .withMessage('applyOnLayers must be a boolean'),

    body('project_id')
      .notEmpty()
      .withMessage('project_id is required')
      .isString()
      .withMessage('project_id must be a string'),

    // Custom validation to ensure at least one zoom level is provided
    body().custom((value) => {
      if (value.minZoomLevel === undefined && value.maxZoomLevel === undefined) {
        throw new Error('At least one zoom level (minZoomLevel or maxZoomLevel) must be provided');
      }
      return true;
    }),

    // Custom validation to ensure minZoomLevel is less than maxZoomLevel if both are provided
    body().custom((value) => {
      if (value.minZoomLevel !== undefined && value.maxZoomLevel !== undefined) {
        if (value.minZoomLevel >= value.maxZoomLevel) {
          throw new Error('minZoomLevel must be less than maxZoomLevel');
        }
      }
      return true;
    }),
  ];

  // Run validation
  Promise.all(validationRules.map((rule) => rule.run(req)))
    .then(() => {
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        const errorMessages = errors.array().map((error) => error.msg);
        res.status(400).send({
          status: 0,
          error: 'Validation failed',
          details: errorMessages,
        });
        return;
      }

      next();
    })
    .catch(next);
}

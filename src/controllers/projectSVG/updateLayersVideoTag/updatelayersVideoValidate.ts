
import { Request, Response, NextFunction } from 'express';

// Interface UploadedFiles {
//   Video_tag:Express.Multer.File[];
// }

const updateLayersVideoValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
//   Const files = req.files as UploadedFiles | undefined;
//   If (!files?.videoTag) {
//     Res.status(400).json({ error: 'videoTag field is required.' });
//   }

  const requiredTextFields = ['svg_id', 'scene_id', 'project_id', 'layer_id'];
  const missingTextFields = requiredTextFields.filter(
    (field) => !(field in req.body),
  );

  if (missingTextFields.length > 0) {
    res.status(400).json({
      error: `Missing text fields: ${missingTextFields.join(', ')}`,
    });
  }

  next();

};

export default updateLayersVideoValidate;

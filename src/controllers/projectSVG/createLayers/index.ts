import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { ProjectSVGModule } from '../../../modules/projectSVG';
import { invalidateSceneAPIs } from '../../../modules/projectScene';
import mongoose from 'mongoose';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
export async function createLayers (
  request: FileRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const projectSVG = new ProjectSVGModule(request.body.project_id, organization_id);
  const randomId = new mongoose.Types.ObjectId().toString();
  let urlobject;
  if (request.files){
    urlobject =await UploadUnitplanFiles(request.files, projectSVG.storagepath+randomId);
  }
  projectSVG.createLayers(request.body, randomId, urlobject?.svgFile).then((res) => {
    response.send({status: 1, data: res});

    // Invalidate project scene APIs (non-blocking)
    invalidateSceneAPIs(organization_id, request.body.project_id).then((invalidateRes) => {
      logger.info('Project scene APIs invalidated successfully', { result: invalidateRes });
    }).catch((err) => {
      logger.error('Error invalidating project scene APIs', { error: err });
    });
  })
    .catch((err) => {
      logger.error('Error in createLayers', {message: err});
      response.send({status: 0, meassage: err});
    });

}

import {Response} from 'express';
import { FileRequest } from '../../../types/extras';
import {ProjectSceneModule, invalidateSceneAPIs} from '../../../modules/projectScene';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { updateProjectSceneObj } from '../../../types/projectScene';
import logger from '../../../config/logger';
import { Types } from 'mongoose';
import { EnvVar } from '../../../types/projectSVG';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
export default async function updateSceneFiles (request:FileRequest, response:Response): Promise<void> {
  const reqbody = request.body;
  const organization_id = request.headers.organization as string;
  const projectScene = new ProjectSceneModule(reqbody.project_id, organization_id);
  const _id = new Types.ObjectId();
  const storagePath = (projectScene.storagepath + _id) as string;
  console.log('-->', request.body);
  if (!request.files) {
    // Handle Multer errors
    response.send('Error reading SVG file:');
    return;
  }
  const requestFiles = request.files;
  console.log('requestFiles ->', requestFiles);
  if (requestFiles === undefined ){
    response.send('Error reading SVG file:');
    return;
  }
  console.log('requestFiles ->', requestFiles);
  UploadUnitplanFiles(requestFiles, projectScene.storagepath+reqbody.scene_id)
    .then((urlObject:{ [key: string]: string }) => {
      console.log('urlObject ->', urlObject);
      const payload: updateProjectSceneObj = {
        lowRes: urlObject.lowRes?urlObject.lowRes:undefined,
        highRes: urlObject.highRes?urlObject.highRes:undefined,
        lowResNight: urlObject.lowResNight?urlObject.lowResNight:undefined,
        highResNight: urlObject.highResNight?urlObject.highResNight:undefined,
        video: urlObject.video?urlObject.video:undefined,
        info_icon: urlObject.info_icon?urlObject.info_icon:undefined,
      };
      projectScene.getScene(reqbody.scene_id).then(async (scene) => {
        try {
          if (scene.type === 'deep_zoom') {
            if ((reqbody.file_url || reqbody.file_url_night) && storagePath && reqbody.scene_id && organization_id && reqbody.project_id){

              const handleLumaCloudRunJob = async (url:string, file_type:string) => {
                const envVars : EnvVar[]  = [
                  { name: 'project_id', value: reqbody.project_id },
                  { name: 'organization_id', value: organization_id },
                  { name: 'scene_id', value: reqbody.scene_id },
                  { name: 'storagepath', value: storagePath },
                  { name: 'url', value: url },
                  {name: 'scene_type', value: 'project'},
                  {name: 'file_type', value: file_type},
                ];
                const jobId = 'luma-blender-jobs';
                const runJob = new cloudRunJobModule;
                runJob.runJobExecution(jobId, envVars);
              };
              if (reqbody.file_url){
                payload.file_url = reqbody.file_url;
                handleLumaCloudRunJob(reqbody.file_url, 'day_file');
              }

              if (reqbody.file_url_night && reqbody.file_url_night!=='null'){
                payload.file_url_night = reqbody.file_url_night;
                handleLumaCloudRunJob(reqbody.file_url_night, 'night_file');
              } else {
                console.log('-> else ', reqbody.file_url_night);
                payload.file_url_night = undefined;
              }
            } else {
              throw new Error('Env Variables not set');
            }
          }
          const res = await projectScene.UpdateSceneFiles(reqbody.scene_id, payload, scene.type);
          response.send({ status: 1, message: 'updateSceneFiles Successful', data: res });

          invalidateSceneAPIs(organization_id, reqbody.project_id).then((invalidationResult) => {
            logger.info('Project scene APIs invalidated successfully', { result: invalidationResult });
          }).catch((invalidationError) => {
            logger.error('Error invalidating project scene APIs', { error: invalidationError });
          });
        } catch (error) {
          logger.error('Error in updateSceneFiles', { message: error });
          response.send({ status: 0, message: error });
        }
      });

      // ProjectScene.UpdateSceneFiles(reqbody.scene_id, payload).then(async (res) => {
      //   Response.send({status: 1, message: 'updateSceneFiles Successfull', data: res});
      // }).catch((error) => {
      //   Logger.error('Error in updateSceneFiles', {message: error});
      //   Response.send({status: 0, message: error});
      // });
      // Const envVars : EnvVar[]  = [
      //   { name: 'project_id', value: reqbody.project_id },
      //   { name: 'scene_id', value: reqbody.scene_id },
      //   { name: 'storagepath', value: storagePath },
      //   { name: 'url', value: reqbody.file_url },
      // ];
      // Const jobId = 'luma-blender-jobs';
      // Const runJob = new cloudRunJobModule;
      // RunJob.runJobExecution(jobId, envVars);
    });
}

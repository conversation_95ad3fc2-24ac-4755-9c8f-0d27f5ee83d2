import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { projectSceneType } from '../../../types/projectScene';
import { Direction } from '../../../types/direction';
import logger from '../../../config/logger';

interface UploadedFiles {
  lowRes: Express.Multer.File[];
  highRes: Express.Multer.File[];
  info_icon: Express.Multer.File[];
  video: Express.Multer.File[];
  file: Express.Multer.File[];
}

const CreateSceneValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  if (files) {
    // Console.log('type');
    // Console.log(req.body);

    // Console.log('reqBody', req.body.type);

    if (
      req.body.type !== 'gsplat' &&
      req.body.type !== 'rotatable_image' &&
      req.body.type !== 'deep_zoom' &&
      (!files.lowRes || !files.highRes)
    ) {
      res
        .status(400)
        .json({ error: 'Both lowRes, HighRes fields are required.' });
    } else {
      let requiredTextFields;
      if (req.body.type === 'rotatable_image_frame') {
        requiredTextFields = ['type', 'project_id'];
      } else {
        requiredTextFields = [
          'type',
          'name',
          'active',
          'project_id',
        ];
      }

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        body(
          'type',
          'Invalid type value. Please ensure that you are using a valid type value',
        )
          .isIn(Object.values(projectSceneType))
          .run(req);
        body('direction')
          .optional()
          .custom((value) => {
            // Direction field is not allowed for gsplat and rotatable_image scene types
            if (req.body.type === 'gsplat' || req.body.type === 'rotatable_image') {
              if (value !== null && value !== undefined && value !== '') {
                return false;
              }
              return true;
            }
            if (value === null || value === '') {
              return true;
            }
            return Object.values(Direction).includes(value);
          })
          .withMessage('Direction field is not allowed for gsplat and rotatable_image scene types')
          .run(req);
        body('north_direction')
          .optional()
          .custom((value) => {
            if (value === null || value === undefined) {
              return true;
            }
            if (typeof value !== 'object') {
              return false;
            }
            if (!value.position || typeof value.position !== 'object') {
              return false;
            }
            if (typeof value.position.x !== 'number' ||
                typeof value.position.y !== 'number' ||
                typeof value.position.z !== 'number') {
              return false;
            }
            if (!value.rotation || typeof value.rotation !== 'object') {
              return false;
            }
            if (typeof value.rotation.x !== 'number' ||
                typeof value.rotation.y !== 'number' ||
                typeof value.rotation.z !== 'number' ||
                typeof value.rotation.w !== 'number') {
              return false;
            }
            return true;
          })
          .withMessage('Invalid north_direction format. Must have position {x, y, z} and rotation {x, y, z, w}')
          .run(req);
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          logger.error('Error in validator', {message: errors});
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default CreateSceneValidate;

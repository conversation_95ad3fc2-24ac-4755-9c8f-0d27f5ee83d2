import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
// Import multer from 'multer';
import { ProjectSceneModule, invalidateSceneAPIs } from '../../../modules/projectScene';
import logger from '../../../config/logger';
import mongoose, { Types } from 'mongoose';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { ProjectSVGModule } from '../../../modules/projectSVG';
// Import { getHighRes } from '../../../helpers/getHighRes';
import { deep_zoom_status } from '../../../types/projectScene';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
import { EnvVar } from '../../../types/projectSVG';

// Const fileFields = [
//   { name: 'lowRes' },
//   { name: 'highRes' },
//   { name: 'info_icon' },
// ];

export default async function createScene (
  request: FileRequest,
  response: Response,
): Promise<void> {
  interface createScene {
    organization_id: string;
    type: string;
    name: string;
    active: boolean;
    parent: string;
    info_text?: string;
    project_id: string;
    building_id: string;
    root: boolean;
    clouds: boolean;
    gsplat_link: string;
    category: string;
    position?: string;
    polar_angle?: string;
    distance?: string;
    auto_rotate?: boolean;
    path?: string;
    file_url?:string;
    file_url_night?:string;
    order?:number;
    deep_zoom_status?:string;
    minZoomLevel?:number;
    maxZoomLevel?:number;
    width:number;
    height:number;
    direction?: string | null;
    north_direction?: {
      position: {
        x: number;
        y: number;
        z: number;
      };
      rotation: {
        x: number;
        y: number;
        z: number;
        w: number;
      };
    } | null;
  }
  // Interface background {
  //   Low_resolution: string;
  //   High_resolution: string;
  // }

  const _id = new Types.ObjectId();

  const reqbody: createScene = request.body;
  const organization_id = request.headers.organization as string;
  const masterScene = new ProjectSceneModule(
    reqbody.project_id,
    organization_id,
  );
  const randomId = new mongoose.Types.ObjectId();
  const projectSVG = new ProjectSVGModule(reqbody.project_id, organization_id);

  if (!request.files) {
  // Handle Multer errors
    response.send('Error reading SVG file:');
    return;
  }
  const requestFiles = request.files;
  if (requestFiles === undefined) {
    response.send('Error reading SVG file:');
    return;
  }
  // Console.log('files', requestFiles.file[0].path);
  // Const ImageFile = requestFiles;
  // Console.log(ImageFile?.file)
  let file_url = '' as string;
  let file_url_night = '' as string;
  const storagePath = (masterScene.storagepath + _id) as string;
  if (reqbody.type === 'deep_zoom') {

    try {

      if (reqbody.file_url){
        file_url = reqbody.file_url;
      }
      if (reqbody.file_url_night){
        file_url_night = reqbody.file_url_night;
      }

      // Const outputFrom_getHighres: background | null = await getHighRes(
      //   StoragePath,
      //   RequestFiles,
      //   File_url,
      // );
      let filesToUpload: Express.Multer.File | Express.Multer.File[] |
      { [fieldname: string]: Express.Multer.File[]; } = [];

      Object.values(requestFiles).forEach((filesArray: Express.Multer.File[]) => {
        filesArray.forEach( (file: Express.Multer.File) => {

          if (file.fieldname === 'lowRes' || file.fieldname === 'lowResNight') {

            // Wrap the file in an object with the field name as key
            filesToUpload = {...filesToUpload, [file.fieldname]: [file] };
          }
        });
      });

      UploadUnitplanFiles(filesToUpload, storagePath).then(
        async (urlObject: { [key: string]: string }) => {

          await masterScene
            .createScene({
              _id: _id,
              organization_id: reqbody.organization_id,
              type: reqbody.type,
              name: reqbody.name,
              viewbox: {
                width: reqbody.width,
                height: reqbody.height,
              },
              background: {
                low_resolution: urlObject.lowRes,
                ...(urlObject.lowResNight && {low_resolution_night: urlObject.lowResNight}),
              },
              deep_zoom_status: deep_zoom_status.NOT_STARTED,
              active: reqbody.active,
              parent: reqbody.parent,
              ...(reqbody.info_text && {info_text: reqbody.info_text}),
              category: reqbody.category,
              clouds: reqbody.clouds,
              building_id: reqbody.building_id,
              minZoomLevel: reqbody.minZoomLevel?reqbody.minZoomLevel:1,
              maxZoomLevel: reqbody.maxZoomLevel?reqbody.maxZoomLevel:10,
              // Direction field is not allowed for gsplat and rotatable_image scene types
              direction: reqbody.direction || null,
              north_direction: reqbody.north_direction || null,
            })
            .then((res) => {
              response.send({
                status: 1,
                message: 'Created Scene Succesfully',
                data: res,
              });

              invalidateSceneAPIs(organization_id, reqbody.project_id).then((invalidationResult) => {
                logger.info('Project scene APIs invalidated successfully', { result: invalidationResult });
              }).catch((invalidationError) => {
                logger.error('Error invalidating project scene APIs', { error: invalidationError });
              });
            });

          const handleLumaCloudRunJob = async (url:string, file_type:string) => {
            const scene_id = _id as unknown as string;
            const envVars : EnvVar[]  = [
              { name: 'project_id', value: reqbody.project_id },
              { name: 'organization_id', value: reqbody.organization_id },
              { name: 'scene_id', value: scene_id },
              { name: 'storagepath', value: storagePath },
              { name: 'url', value: url },
              {name: 'scene_type', value: 'project'},
              {name: 'file_type', value: file_type},
            ];
            const jobId = 'luma-blender-jobs';
            const runJob = new cloudRunJobModule;
            runJob.runJobExecution(jobId, envVars);
          };
          handleLumaCloudRunJob(file_url, 'day_file');
          reqbody.file_url_night && handleLumaCloudRunJob(file_url_night, 'night_file');

        },
      );

    } catch (error){
      if (error instanceof Error){
        response
          .status(400)
          .send({ status: 0, error: 'Error in CreateScene Deep_zoom', message: error.message });
      }

    }
  } else {
    UploadUnitplanFiles(requestFiles, masterScene.storagepath + _id).then(
      (urlObject: { [key: string]: string }) => {
        console.log('urlObject', urlObject);

        masterScene
          .createScene({
            _id: _id,
            organization_id: reqbody.organization_id,
            type: reqbody.type,
            name: reqbody.name,
            viewbox: {
              width: reqbody.width,
              height: reqbody.height,
            },
            ...(reqbody.order && {order: reqbody.order}),
            background: {
              low_resolution: urlObject.lowRes,
              high_resolution: urlObject.highRes,
              ...(urlObject.lowResNight && {low_resolution_night: urlObject.lowResNight}),
              ...(urlObject.highResNight && {high_resolution_night: urlObject.highResNight}),
            },
            active: reqbody.active,
            info_icon: urlObject.info_icon,
            parent: reqbody.parent,
            ...(reqbody.info_text && {info_text: reqbody.info_text}),
            video: urlObject.video,
            clouds: reqbody.clouds,
            root: reqbody.root,
            gsplat_link: urlObject.gsplat,
            category: reqbody.category,
            building_id: reqbody.building_id,
            position: reqbody.position
              ? JSON.parse(reqbody.position as string)
              : undefined,
            polar_angle: reqbody.polar_angle
              ? JSON.parse(reqbody.polar_angle as string)
              : undefined,
            distance: reqbody.distance
              ? JSON.parse(reqbody.distance as string)
              : undefined,
            auto_rotate: reqbody.auto_rotate,
            // Direction field is not allowed for gsplat and rotatable_image scene types
            direction: (reqbody.type === 'gsplat' || reqbody.type === 'rotatable_image')
              ? null
              : (reqbody.direction || null),
            north_direction: reqbody.north_direction || null,
          })
          .then((res) => {
            const sendResponseAndInvalidate = () => {
              response.send({ status: 1, data: res });
              invalidateSceneAPIs(organization_id, reqbody.project_id).then((invalidationResult) => {
                logger.info('Project scene APIs invalidated successfully', { result: invalidationResult });
              }).catch((invalidationError) => {
                logger.error('Error invalidating project scene APIs', { error: invalidationError });
              });
            };

            if (res && reqbody.type === 'gsplat') {
              projectSVG
                .createSVG({
                  _id: randomId,
                  scene_id: res._id,
                  layers: {},
                  type: reqbody.type,
                })
                .then(() => {
                  sendResponseAndInvalidate();
                });
            } else if (res && reqbody.type === 'rotatable_image_frame') {
              masterScene
                .UpdateScene(reqbody.parent, { frame_id: res._id })
                .then(() => {
                  sendResponseAndInvalidate();
                });
            } else {
              sendResponseAndInvalidate();
            }
          })
          .catch((error) => {
            console.log(error);
            response.send({ status: 0, message: error });
          });
      },
    );
  }

  // Const upload = multer({ dest: 'masterScenes/' }).fields(fileFields);
  // Upload(request, response, (err) => {

  // });
}

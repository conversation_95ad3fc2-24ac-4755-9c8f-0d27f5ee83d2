import {Request, Response} from 'express';
import { deep_zoom_status } from '../../../types/projectScene';
import { ProjectSceneModule, invalidateSceneAPIs } from '../../../modules/projectScene';
import logger from '../../../config/logger';

export default async function updateSceneAPI (request:Request, response:Response): Promise<void> {
    interface updateScene {
        organization_id: string,
        project_id: string,
        scene_id: string
        type?: string,
        name?: string,
        active?: boolean,
        parent?: number,
        info_text?: string,
        building_id?: string,
        root?: boolean,
        clouds?: boolean,
        category?:string,
        position?:object,
        polar_angle?:object,
        distance?:object,
        auto_rotate?:boolean,
        highRes?:string,
        highResNight?:string,
        deep_zoom_status?:{
          type:string,
          enum:deep_zoom_status
        },
        deep_zoom_failed_info?:string,
        north_direction?: {
          position: {
            x: number;
            y: number;
            z: number;
          };
          rotation: {
            x: number;
            y: number;
            z: number;
            w: number;
          };
        } | null;
    }
    const reqbody:updateScene = request.body;
    const projectScene = new ProjectSceneModule(reqbody.project_id, reqbody.organization_id);
    projectScene.UpdateScene(reqbody.scene_id, request.body).then(async (res) => {
      response.send({status: 1, data: res});

      invalidateSceneAPIs(reqbody.organization_id, reqbody.project_id).then((invalidationResult) => {
        logger.info('Project scene APIs invalidated successfully', { result: invalidationResult });
      }).catch((invalidationError) => {
        logger.error('Error invalidating project scene APIs', { error: invalidationError });
      });
    }).catch((error) => {
      response.send({status: 0, message: error});
    });
}

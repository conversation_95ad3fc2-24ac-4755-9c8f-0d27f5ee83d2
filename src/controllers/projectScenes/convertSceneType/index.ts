import {Request, Response} from 'express';
import {ProjectSceneModule, invalidateSceneAPIs} from '../../../modules/projectScene';
import {ProjectSVGModule} from '../../../modules/projectSVG';

import logger from '../../../config/logger';
import { convertSceneTypePayload, deep_zoom_status } from '../../../types/projectScene';
import { coordinatesObjectReference, EnvVar } from '../../../types/projectSVG';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
export default async function convertSceneType (request:Request, response:Response): Promise<void> {
  console.log('convertSceneType');
  const reqbody:convertSceneTypePayload = request.body;
  const organization_id = request.headers.organization as string;
  const projectScene = new ProjectSceneModule(reqbody.project_id, organization_id);
  const projectSVG = new ProjectSVGModule(
    request.body.project_id,
    organization_id,
  );
  const storagePath = (projectScene.storagepath + reqbody.scene_id) as string;

  await projectScene.UpdateScene(reqbody.scene_id, {
    deep_zoom_status: deep_zoom_status.NOT_STARTED,
    type: reqbody.toType,
    high_resolution_copy: reqbody.action==='copy'?reqbody.high_resolution:undefined,
  }).then(async () => {
    projectSVG.convertType(reqbody.scene_id).then(async (res) => {
      if (res){
        const message = await projectSVG.layersConversion(res as {[key:string]: {layers:coordinatesObjectReference} });
        response.send({status: 1, data: message});

        invalidateSceneAPIs(organization_id, reqbody.project_id).then((invalidationResult) => {
          logger.info('Project scene APIs invalidated successfully', { result: invalidationResult });
        }).catch((invalidationError) => {
          logger.error('Error invalidating project scene APIs', { error: invalidationError });
        });
      }
    });
  }).catch((error) => {
    logger.error('Error in updateScene', {message: error});
    response.send({status: 0, message: error});
  });
  const envVars : EnvVar[]  = [
    { name: 'project_id', value: reqbody.project_id },
    { name: 'organization_id', value: organization_id },
    { name: 'scene_id', value: reqbody.scene_id },
    { name: 'storagepath', value: storagePath },
    { name: 'url', value: reqbody.high_resolution },
    { name: 'scene_type', value: 'project' },
  ];
  const jobId = 'luma-blender-jobs';
  const runJob = new cloudRunJobModule;
  runJob.runJobExecution(jobId, envVars);
}

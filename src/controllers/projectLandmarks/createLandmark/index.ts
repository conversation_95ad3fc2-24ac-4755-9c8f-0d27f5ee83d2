import {Response} from 'express';
import { FileRequest } from '../../../types/extras';
import {ProjectLandmarkModule, invalidateProjectLandmarkAPIs} from '../../../modules/projectLandmark';
import { Types } from 'mongoose';
// Import multer from 'multer';
// Import fs from 'fs';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';

export default async function createLandmark (request:FileRequest, response:Response):Promise<void> {
    interface createLandmark {
        project_id:string,
        name:string,
        distance?: number,
        category:string,
        walk_timing?:number,
        transit_timing?:number,
        car_timing?:number,
        lat:number,
        long:number,
        description:string
    }

    const reqbody:createLandmark = request.body;
    const organization_id = request.headers.organization as string;
    const projectLandmark = new ProjectLandmarkModule(reqbody.project_id, organization_id);
    const id = new Types.ObjectId();
    if (!request.files) {
      // Handle Multer errors
      response.send('Error reading file: ');
      return;
    }
    // Const thumbnailurl =request.files !== undefined
    //   ? await UploadUnitplanFiles(request.files,'')
    //   :'';

    console.log(request.files);
    const urlObject =await UploadUnitplanFiles(request.files, projectLandmark.storagepath+id);

    projectLandmark.createLandmark(
      {
        _id: id,
        name: reqbody.name,
        ...(reqbody.distance && {distance: reqbody.distance}),
        category: reqbody.category,
        ...(reqbody.walk_timing && {walk_timing: reqbody.walk_timing}),
        ...(reqbody.car_timing && {car_timing: reqbody.car_timing}),
        ...(reqbody.transit_timing && {transit_timing: reqbody.transit_timing}),
        thumbnail: urlObject.thumbnail,
        icon: urlObject.icon,
        lat: reqbody.lat,
        long: reqbody.long,
        description: reqbody.description,
      },
    ).then(async (res) => {
      response.send({status: 1, data: res});

      invalidateProjectLandmarkAPIs(organization_id, reqbody.project_id).then((invalidationResult) => {
        logger.info('Project landmark APIs invalidated successfully', { result: invalidationResult });
      }).catch((invalidationError) => {
        logger.error('Error invalidating project landmark APIs', { error: invalidationError });
      });
      // Await fs.promises.rm('projectLandmarks/', { recursive: true });
      return;
    })
      .catch((error) => {
        logger.error('Error in createLandmark', {message: error});
        response.send({status: 0, message: error});
        return;
      });

  // Const upload = multer({ dest: 'projectLandmarks/' }).single('thumbnail');
  // Upload(request, response, async (err) => {

  // });
}

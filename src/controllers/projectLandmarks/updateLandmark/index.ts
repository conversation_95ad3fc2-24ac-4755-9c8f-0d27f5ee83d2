import {Request, Response} from 'express';
import { ProjectLandmarkModule, invalidateProjectLandmarkAPIs } from '../../../modules/projectLandmark';
import logger from '../../../config/logger';

export default async function updateLandmark (request:Request, response:Response):Promise<void>{
  const organization_id = request.headers.organization as string;
  const landmark = new ProjectLandmarkModule(request.body.project_id, organization_id);
  landmark.updateLandmark(request.body).then((res) => {
    response.send({status: 1, data: res});

    invalidateProjectLandmarkAPIs(organization_id, request.body.project_id).then((invalidationResult) => {
      logger.info('Project landmark APIs invalidated successfully', { result: invalidationResult });
    }).catch((invalidationError) => {
      logger.error('Error invalidating project landmark APIs', { error: invalidationError });
    });
  })
    .catch((err) => {
      logger.error('Error in updateLandmark', {message: err});
      response.send({status: 0, meaasage: err});
    });
  return;
}

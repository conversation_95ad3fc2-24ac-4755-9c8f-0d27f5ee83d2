import {Request, Response} from 'express';
import { ProjectLandmarkModule, invalidateProjectLandmarkAPIs } from '../../../modules/projectLandmark';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';

export default async function updateLandmarkFiles (request:Request, response:Response):Promise<void>{
  const organization_id = request.headers.organization as string;
  const landmark = new ProjectLandmarkModule(request.body.project_id, organization_id);
  const requestFiles = request.files;
  if (requestFiles === undefined) {
    response.status(400).json({ error: 'Thumbnail is required.'});
    return;
  }
  const urlObject = await UploadUnitplanFiles(requestFiles, landmark.storagepath+request.body.landmar_id);
  landmark.updateLandmarkFiles(request.body, urlObject.thumbnail, urlObject.icon).then((res) => {
    response.send({status: 1, data: res});

    invalidateProjectLandmarkAPIs(organization_id, request.body.project_id).then((invalidationResult) => {
      logger.info('Project landmark APIs invalidated successfully', { result: invalidationResult });
    }).catch((invalidationError) => {
      logger.error('Error invalidating project landmark APIs', { error: invalidationError });
    });
  })
    .catch((err) => {
      logger.error('Error in updateLandmarkFiles', {message: err});
      response.send({status: 0, meaasage: err});
    });
  return;
}

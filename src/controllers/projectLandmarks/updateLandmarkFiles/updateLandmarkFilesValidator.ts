import { header, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

interface UploadedFiles {
  thumbnail: Express.Multer.File[],
  icon: Express.Multer.File[],
}

const updateLandmarkFilesValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  if (files) {
    if (!files.thumbnail && !files.icon) {
      console.log(files);
      res
        .status(400)
        .json({ error: 'Thumbnail or icon field is required.' });
    } else {
      const requiredTextFields = [
        'project_id',
        'landmark_id',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        header('organization', 'Organization  is required').notEmpty().run(req);
        header('accesstoken', 'Access Token is required').notEmpty().run(req);
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default updateLandmarkFilesValidate;

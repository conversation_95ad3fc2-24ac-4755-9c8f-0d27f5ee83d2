import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { routeCategory } from '../../../types/projectLandmark';

const saveRoutesValidator = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('landmark_id', 'Landmark ID is required').notEmpty(),
  body('json', 'JSON string is required').notEmpty(),
  body('mode', 'Mode is required').notEmpty(),
  body('mode').isIn(Object.values(routeCategory))
    .withMessage(
      'Invalid mode value. Please ensure that you are using a valid mode value',
    ),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default saveRoutesValidator;

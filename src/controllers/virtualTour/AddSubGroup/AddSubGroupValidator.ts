import { body,  header, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const AddSubGroupValidator = [
  header('organization', 'Organization is required').notEmpty().isString(),
  header('accesstoken', 'Access Token is required').notEmpty().isString(),

  body('project_id', 'Project ID is required and must be a string').notEmpty().isString(),
  body('tour_id', 'Invalid Tour ID').notEmpty().isString(),
  body('group_id', 'Invalid Group ID').notEmpty().isString(),

  body('name', 'SubGroup name is required and must be a string').notEmpty().isString(),
  body('order')
    .notEmpty()
    .isInt()
    .withMessage('Subgroup order must be a valid integer'),
  body('icon')
    .optional()
    .notEmpty()
    .withMessage('Subgroup icon must not be empty if provided')
    .isString()
    .withMessage('Subgroup icon must be a string'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default AddSubGroupValidator;

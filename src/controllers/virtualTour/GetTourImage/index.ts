import { VirtualTourModule } from '../../../modules/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetTourImage (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id;

  if (!organization_id) {
    logger.error('Organization ID not found in request', { headers: request.headers });
    response.status(500).json({ status: 0, error: 'Organization ID not found' });
    return;
  }

  const { project_id, tour_id, image_id } = request.params;

  try {
    const tourModule = new VirtualTourModule(organization_id, project_id);
    const imageData = await tourModule.GetTourImageById(tour_id, image_id);

    if (!imageData) {
      logger.warn('Tour Image not found', { project_id, tour_id, image_id });
      response.status(404).json({ status: 0, error: 'Tour Image not found' });
      return;
    }

    response.status(200).json({ status: 1, data: imageData });
  } catch (error) {
    logger.error('Error fetching Tour Image', { error });
    response.status(500).json({ status: 0, error: `Error fetching tour image: ${error}` });
  }
}

import logger from '../../../config/logger';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';

export async function UpdateSubGroupOrder (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const project_id = req.body.project_id as string;
    const tour_id = req.body.tour_id as string;
    const group_id = req.body.group_id as string;
    const subgroup_id = req.body.subgroup_id as string;
    const order = req.body.order;

    const newOrder = typeof order === 'string' ? parseInt(order, 10) : order;

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.UpdateSubGroupOrder(
      tour_id,
      group_id,
      subgroup_id,
      newOrder,
    );

    res.status(201).json({
      status: 1,
      message: 'Subgroup order updated successfully',
      data: updatedTour,
    });
  } catch (error) {
    logger.error('Error in UpdateSubGroupOrder', { error });
    res.status(500).json({
      status: 0,
      error: `Error updating Subgroup order: ${error instanceof Error ? error.message : error}`,
    });
  }
}

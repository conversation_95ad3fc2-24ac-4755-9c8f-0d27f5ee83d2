import { ExtendedRequest } from './../../../types/extras';
import logger from '../../../config/logger';
import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { EnvVar, tile_rendering_status } from '../../../types/virtualTour';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
import { processStereoImages } from '../../../helpers/storageUpload';

export async function UpdateTourImage (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const organization_id = request.organization_id as string;
    const { project_id, tour_id, image_id, type, isStereo, url, thumbnail } = request.body;
    const tourModule = new VirtualTourModule(organization_id, project_id);

    let finalUrl = url;
    let finalThumbnail = thumbnail;
    let stereoThumbnailUrl = '';
    let stereoUrl = '';

    // If URLs are provided in request body, process them
    if (url && thumbnail) {
      const isStereoEnabled = isStereo === true || isStereo === 'true';
      console.log('isStereoEnabled:', isStereoEnabled);
      if (isStereoEnabled) {
        const storagePath = `${tourModule.storagepath}/${tour_id}`;
        const processedImages = await processStereoImages(url, thumbnail, storagePath, image_id as string);
        finalUrl = processedImages.finalUrl;
        finalThumbnail = processedImages.finalThumbnail;
        stereoUrl = processedImages.stereoUrl;
        stereoThumbnailUrl = processedImages.stereoThumbnailUrl;
      }
      const updateFields: any = {
        ...request.body,
        updated_at: new Date().toISOString(),
        tile_rendering_status: tile_rendering_status.NOT_STARTED,
        tile_rendering_failed_info: null,
        stereoThumbnail: stereoThumbnailUrl,
        stereoUrl: stereoUrl,
      };
      if (finalUrl) {
        updateFields.url = finalUrl;
      }
      if (finalThumbnail) {
        updateFields.thumbnail = finalThumbnail;
      }
      console.log('Updating tour image with fields:', updateFields);
      const updatedTour = await tourModule.UpdateTourImage(tour_id, image_id, updateFields);

      if (!updatedTour) {
        throw new Error(`Failed to UpdateTourImage. Payload: ${JSON.stringify(updateFields)}, Response: ${JSON.stringify(updatedTour)}`);
      }
      if (finalUrl) {
        const storagePath = (tourModule.storagepath +'/'+tour_id) as string;
        const imageId = image_id as string;
        const imageData = [{
          id: imageId,
          url: finalUrl,
        }];
        const envVars : EnvVar[]  = [
          { name: 'project_id', value: project_id },
          { name: 'organization_id', value: organization_id },
          { name: 'tour_id', value: tour_id },
          { name: 'storagepath', value: storagePath },
          { name: 'imageData', value: JSON.stringify(imageData) },
          { name: 'type', value: type },
          { name: 'jobType', value: 'update_image' },
        ];

        const jobId = 'tile-generator';
        const runJob = new cloudRunJobModule;
        runJob.runJobExecution(jobId, envVars); // Run the job
      }
      response.status(200).json({ status: 1, data: updatedTour });
    } else {
      const updateFields = { ...request.body };
      const tourData = await tourModule.UpdateTourImage(tour_id, image_id, updateFields);
      response.status(200).json({ status: 1, data: tourData });
    }
  } catch (error) {
    logger.error('Error Updating tour Image', { message: error });
    response.status(404).json({ status: 0, error: 'Error Updating tour Image' + error });
  }
}

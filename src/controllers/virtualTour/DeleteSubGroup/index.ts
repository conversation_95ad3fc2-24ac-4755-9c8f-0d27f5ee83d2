import {  Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function DeleteSubGroup (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const project_id = req.body.project_id as string;
    const tour_id = req.body.tour_id as string;
    const group_id = req.body.group_id as string;
    const subgroup_id = req.body.subgroup_id as string;

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.DeleteSubGroup(tour_id, group_id, subgroup_id);

    res.status(200).json({
      status: 1,
      message: 'SubGroup deleted successfully',
      data: updatedTour,
    });
  } catch (error) {
    logger.error('Error in DeleteSubGroup', { message: error });
    res.status(500).json({
      status: 0,
      error: `Error deleting subgroup: ${error}`,
    });
  }
}

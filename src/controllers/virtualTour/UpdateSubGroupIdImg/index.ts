import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { VirtualTourModule } from '../../../modules/virtualTour';
import logger from '../../../config/logger';

export async function UpdateSubGroupIdImg (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const project_id = req.body.project_id as string;
    const tour_id = req.body.tour_id as string;
    const group_id = req.body.group_id as string;
    const subgroup_id = req.body.subgroup_id as string;
    const image_id = req.body.image_id as string;

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.UpdateSubGroupIdImg(
      tour_id,
      group_id,
      subgroup_id,
      image_id,
    );

    res.status(200).json({
      status: 1,
      message: 'Image linked to subgroup successfully',
      data: updatedTour,
    });
  } catch (error) {
    logger.error('Error in UpdateSubGroupIdImg', { error });
    res.status(500).json({
      status: 0,
      error: `Error linking image to subgroup: ${error instanceof Error ? error.message : error}`,
    });
  }
}

import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { VirtualTourModule } from '../../../modules/virtualTour';
import logger from '../../../config/logger';

export async function AddLink (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const project_id = req.body.project_id as string;
    const tour_id = req.body.tour_id as string;
    const image_id = req.body.image_id as string;
    const destination_img_id = req.body.destination_img_id as string;

    const linkData = {
      position: {
        x: req.body.position.x,
        y: req.body.position.y,
        z: req.body.position.z,
      },
      text: req.body.text,
      destination_img_id: destination_img_id,
    };

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.AddLink(tour_id, image_id, linkData);

    res.status(201).json({
      status: 1,
      message: 'Link added successfully',
      data: updatedTour,
    });
  } catch (error) {
    logger.error('Error in AddLink', { error });
    res.status(500).json({
      status: 0,
      error: `Error adding link: ${error instanceof Error ? error.message : error}`,
    });
  }
}

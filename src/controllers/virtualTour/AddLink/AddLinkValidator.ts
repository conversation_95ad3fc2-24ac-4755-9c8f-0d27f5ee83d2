import { body, header, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const AddLinkValidator = [
  header('organization', 'Organization is required').notEmpty().isString(),
  header('accesstoken', 'Access Token is required').notEmpty().isString(),

  body('project_id', 'Project ID is required and must be a string').notEmpty().isString(),
  body('tour_id', 'Tour ID is required and must be a string').notEmpty().isString(),
  body('position').isObject().withMessage('Position must be an object'),
  body('position.x', 'Position X coordinate is required and must be a string').notEmpty().isString(),
  body('position.y', 'Position Y coordinate is required and must be a string').notEmpty().isString(),
  body('position.z', 'Position Z coordinate is required and must be a string').notEmpty().isString(),

  body('text', 'Text is required and must be a string').notEmpty().isString(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default AddLinkValidator;

import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const DeleteTourLabelValidate = [
  body('project_id', 'Project ID is required and must be a string').notEmpty().isString(),
  body('tour_id', 'Tour ID is required and must be a string').notEmpty().isString(),
  body('label_id', 'Label ID is required and must be a string').notEmpty().isString(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default DeleteTourLabelValidate;

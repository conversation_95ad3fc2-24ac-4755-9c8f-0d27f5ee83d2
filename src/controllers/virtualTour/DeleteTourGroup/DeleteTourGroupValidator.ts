import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const DeleteTourGroupValidator = [
  header('organization', 'Organization is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Invalid Project ID').notEmpty().isString(),
  body('tour_id', 'Invalid Tour ID').notEmpty().isString(),
  body('group_id', 'Invalid Group ID').notEmpty().isString(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default DeleteTourGroupValidator;

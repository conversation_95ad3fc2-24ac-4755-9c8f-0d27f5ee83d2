import { body, header, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const DuplicateTourImageValidator = [
  header('organization', 'Organization is required').notEmpty().isString(),
  header('accesstoken', 'Access Token is required').notEmpty().isString(),
  body('project_id', 'Project ID is required and must be a string').notEmpty().isString(),
  body('tour_id', 'Tour ID is required and must be a string').notEmpty().isString(),
  body('image_id', 'image_id is required and must be a string').notEmpty().isString(),
  body('targetorder')
    .notEmpty()
    .isInt()
    .withMessage('targetorder must be a valid integer'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default DuplicateTourImageValidator;

import { VirtualTourModule, invalidateTourAPIs } from '../../../modules/virtualTour';
import { VirtualTour } from '../../../types/virtualTour';
import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import mongoose from 'mongoose';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';

export async function CreateVirtualTour (
  request: FileRequest,
  response: Response,
): Promise<VirtualTour | void>{
  const organization_id = request.organization_id;
  const {project_id} = request.body;
  if (!organization_id){
    response.status(500).json({status: 0, error: 'Organization Id not found '});
    return;
  }
  const requestFiles = request.files;
  const virtualTour = new VirtualTourModule(organization_id, project_id);
  const id = new mongoose.Types.ObjectId();
  let urlObject: { [key: string]: string }={};
  if (requestFiles){
    urlObject = await UploadUnitplanFiles(requestFiles, virtualTour.storagepath + id);
  }
  const createVirtualTour = {
    _id: id,
    name: request.body.name,
    description: request.body.description,
    organization: organization_id,
    project_id: request.body.project_id,
    category: request.body.category,
    type: request.body.type,
    unitplan_id: request.body.unitplan_id,
    space_id: request.body.space_id,
    link: request.body.link,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    model: urlObject.model,
    camera: urlObject.camera,
  };
  virtualTour.CreateVirtualTour(createVirtualTour).then((virtualTourData) => {
    if (virtualTourData===null){
      response.status(404).json({status: 0, error: 'Resource not found...'});
    } else {
      response.status(200).json({status: 1, data: virtualTourData});

      // Invalidate tour APIs (non-blocking)
      invalidateTourAPIs(organization_id, project_id).then((invalidateRes) => {
        logger.info('Tour APIs invalidated successfully', { result: invalidateRes });
      }).catch((err) => {
        logger.error('Error invalidating tour APIs', { error: err });
      });
    }
  }).catch((error: Error) => {
    logger.error('Error in CreateVirtualTour', {message: error});

    response.status(500).json({status: 0, error: 'Error creating virtual tour '+error});
  });
}

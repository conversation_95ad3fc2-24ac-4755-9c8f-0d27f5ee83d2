import { Request, Response, NextFunction } from 'express';
import { body, header,  validationResult } from 'express-validator';

const UpdateTourGroupValidator = [
  header('organization', 'Organization is required')
    .notEmpty()
    .isString(),

  header('accesstoken', 'Access Token is required')
    .notEmpty()
    .isString(),

  body('project_id', 'Project ID is required and must be a string')
    .notEmpty()
    .isString(),

  body('tour_id', 'Tour ID is required and must be a string')
    .notEmpty()
    .isString(),

  body('group_id', 'Group ID is required and must be a string')
    .notEmpty()
    .isString(),

  body('name', 'Group name is required and must be a string')
    .optional()
    .isString(),

  body('order')
    .optional()
    .isInt()
    .withMessage('Group order must be a valid integer'),

  body('icon')
    .optional()
    .isString()
    .withMessage('Group icon must be a string'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default UpdateTourGroupValidator;

import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function UpdateTourGroup (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  try {
    const organization_id = req.organization_id;
    const { project_id, tour_id, group_id } = req.body;
    const updateData = {
      name: req.body.name,
      icon: req.body.icon,
      order: req.body.order,
    };

    if (!organization_id) {
      res.status(400).json({ status: 0, error: 'Organization ID is required' });
      return;
    }

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.UpdateGroup(tour_id, group_id, updateData);

    if (!updatedTour) {
      res.status(404).json({ status: 0, error: 'Tour or group not found' });
      return;
    }

    res.status(200).json({
      status: 1,
      data: updatedTour.groups[group_id],
    });
  } catch (error) {
    logger.error('Error in UpdateGroup', { error });
    res.status(500).json({ status: 0, error: `Error updating group: ${error}` });
  }
}

import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const AddTourLabelValidate = [

  body('project_id', 'Project ID is required and must be a string').notEmpty(),
  body('tour_id', 'Tour ID is required and must be a string').notEmpty(),
  body('name', 'Name is required and must be a string').notEmpty(),
  body('camera_name', 'Camera name is required and must be a string').notEmpty(),
  body('camera_position', 'Camera Position is required').notEmpty(),
  body('camera_position.x', 'Camera Position X coordinate is required and must be a number').notEmpty().isNumeric(),
  body('camera_position.y', 'Camera Position Y coordinate is required and must be a number').notEmpty().isNumeric(),
  body('camera_position.z', 'Camera Position Z coordinate is required and must be a number').notEmpty().isNumeric(),
  body('controls_target', 'Controls Target is required').notEmpty(),
  body('controls_target.x', 'Controls Target X coordinate is required and must be a number').notEmpty().isNumeric(),
  body('controls_target.y', 'Controls Target Y coordinate is required and must be a number').notEmpty().isNumeric(),
  body('controls_target.z', 'Controls Target Z coordinate is required and must be a number').notEmpty().isNumeric(),
  body('order', 'order is required and must be a number').notEmpty().isNumeric(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default AddTourLabelValidate;

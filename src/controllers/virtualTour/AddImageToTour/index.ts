import { VirtualTourModule } from '../../../modules/virtualTour';
import { addImageType, EnvVar, tile_rendering_status } from '../../../types/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import mongoose from 'mongoose';
import logger from '../../../config/logger';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
import { processStereoImages } from '../../../helpers/storageUpload';

export async function AddImageToTour (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const {project_id, tour_id, images, type, isStereo } = req.body;
    const imageData = [];
    let storagePath = '';
    if (!project_id || !tour_id ) {
      throw new Error('project_id (or) tour_id is undefined');
    }

    if (!Array.isArray(images) || images.length === 0 ) {
      throw new Error('images must be a non-empty array');
    }

    const allAddedImages = [];

    for (const item of images){

      const { name, rotation, order, url, thumbnail } = item; // Item details

      if (!url || !thumbnail) {
        throw new Error('Urls not found neither in thumbnail nor url');
      }

      const id = new mongoose.Types.ObjectId().toString();
      const tourModule = new VirtualTourModule(organization_id, project_id);
      storagePath = (tourModule.storagepath +'/'+tour_id) as string;

      let finalUrl = url;
      let finalThumbnail = thumbnail;
      let stereoThumbnailUrl = '';
      let stereoUrl = '';

      // If stereo is enabled or type is stereo, crop images
      if (isStereo) {
        const processedImages = await processStereoImages(url, thumbnail, storagePath, id);
        finalUrl = processedImages.finalUrl;
        finalThumbnail = processedImages.finalThumbnail;
        stereoUrl = processedImages.stereoUrl;
        stereoThumbnailUrl = processedImages.stereoThumbnailUrl;
      }

      const imagePayload: addImageType = {
        id,
        name,
        url: finalUrl,
        thumbnail: finalThumbnail,
        rotation,
        order: Number(order),
        tile_rendering_status: tile_rendering_status.NOT_STARTED,
        tile_rendering_failed_info: null,
        updated_at: new Date().toISOString(),
      };
      // Add stereo fields if isStereo is true
      if (isStereo && stereoThumbnailUrl && stereoUrl) {
        imagePayload.stereoThumbnail = stereoThumbnailUrl;
        imagePayload.stereoUrl = stereoUrl;
      }
      console.log('Adding image with payload:', imagePayload);
      const addedImage = await tourModule.AddImagesToTour(tour_id, imagePayload);

      if (!addedImage) {
        const errorMsg = `Failed to AddImagesToTour. Payload: ${JSON.stringify(imagePayload)}, ` +
          `Response: ${JSON.stringify(addedImage)}`;
        throw new Error(errorMsg);
      }
      const image_id = id as unknown as string;
      // Use cropped URL for tile generation
      imageData.push({id: image_id, url: finalUrl});
      allAddedImages.push(addedImage); // Push the addedImage module result.
    }
    console.log('image', imageData);
    if (imageData.length > 0){
      const envVars : EnvVar[]  = [
        { name: 'project_id', value: project_id },
        { name: 'organization_id', value: organization_id },
        { name: 'tour_id', value: tour_id },
        { name: 'storagepath', value: storagePath },
        { name: 'imageData', value: JSON.stringify(imageData.flat()) },
        { name: 'type', value: type },
        { name: 'jobType', value: 'add_image' },
      ];
      const jobId = 'tile-generator';
      const runJob = new cloudRunJobModule;
      runJob.runJobExecution(jobId, envVars); // Run the job
    }
    res.status(200).json({ status: 1, data: allAddedImages[allAddedImages.length - 1]  }); // Send the all response
  } catch (error) { // Loop exits immediately
    logger.error('Error in AddImageToTour', { message: error });
    res.status(500).json({ status: 0, error: `Error adding image: ${error}` });
  }
}

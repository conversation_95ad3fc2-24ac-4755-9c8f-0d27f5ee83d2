import { ExtendedRequest } from '../../../types/extras';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.body;
  const organization_id = request.organization_id as string;
  const virtualTour = new VirtualTourModule(organization_id, project_id);
  const virtualtour_id = request.body.tour_id;
  const timeStamp = request.body.timeStamp;

  await virtualTour
    .moveToTrash(virtualtour_id, project_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving virtual tour to trash: '+ error });
    });
}

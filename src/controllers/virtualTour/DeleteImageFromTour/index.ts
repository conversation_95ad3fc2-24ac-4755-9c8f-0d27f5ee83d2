import { Response } from 'express';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';
import { VirtualTourModule } from '../../../modules/virtualTour';

export async function DeleteImageFromTour (request: ExtendedRequest, response: Response):Promise<void>{
  const organization_id = request.organization_id as string;
  const { project_id, tour_id, image_id } = request.body;
  logger.info('Delete Image From Tour Controller Called', {tour_id, image_id});
  const translations = new VirtualTourModule(organization_id, project_id);
  if (tour_id){
    const translated = await translations.DeleteImageFromTour(tour_id, image_id);
    response.status(200).send({ status: 1, data: translated });
  } else {
    logger.error('Unable to find TranslationId');
    response.status(500).send('Unable to find TranslationId');
  }
}

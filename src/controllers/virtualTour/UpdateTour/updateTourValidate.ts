import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { tourCategory, tourType } from '../../../types/virtualTour';

interface UploadedFiles {
  model: Express.Multer.File[];
  camera: Express.Multer.File[];
}

const updateTourValidate = [
  header('organization', 'Organization is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),

  body('project_id', 'Invalid or missing Project ID').notEmpty().isString(),
  body('tour_id', 'Invalid or missing Tour ID').notEmpty().isString(),
  body('name', 'Name should be a string').optional().isString(),
  body('description', 'Description should be a string').optional().isString(),
  body('category', 'Category should be a valid value').optional().isIn(Object.values(tourCategory)),
  body('type', 'Type should be a valid value').optional().isIn(Object.values(tourType)),
  body('unitplan_id', 'Unit Plan ID should be a string').optional().isString(),

  body('link')
    .if(body('type').equals(tourType.EXTERNAL))
    .notEmpty()
    .withMessage('Link is required for external tour type')
    .isString()
    .withMessage('Link must be a string'),

  body('space_id')
    .if(body('type').equals(tourType.MATTERPORT))
    .notEmpty()
    .withMessage('Space ID is required for matterport tour type')
    .isString()
    .withMessage('Space ID must be a string'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    const files = req.files as UploadedFiles | undefined;
    if (files) {
      if (req.body.type===tourType.MLE && (!files.model || !files.camera)){
        res
          .status(400)
          .json({ error: 'Both Model glb and Camera gltf files are required.' });
      } else if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
      }
      next();
    }
  },
];

export default updateTourValidate;

import logger from '../../../config/logger';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';

export async function UpdateGroupOrder (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const project_id = req.body.project_id as string;
    const tour_id = req.body.tour_id as string;
    const group_id = req.body.group_id as string;
    const order = req.body.order;

    const newOrder = typeof order === 'string' ? parseInt(order, 10) : order;

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.UpdateGroupOrder(tour_id, group_id, newOrder);

    res.status(200).json({
      status: 1,
      message: 'Group order updated successfully',
      data: updatedTour,
    });
  } catch (error) {
    logger.error('Error in UpdateGroupOrder', { error });
    res.status(500).json({
      status: 0,
      error: `Error updating Group order: ${error instanceof Error ? error.message : error}`,
    });
  }
}

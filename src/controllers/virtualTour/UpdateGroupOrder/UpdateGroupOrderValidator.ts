import { Request, Response, NextFunction } from 'express';
import { body, header,  validationResult } from 'express-validator';

const UpdateGroupOrderValidator = [
  header('organization', 'Organization is required').notEmpty().isString(),
  header('accesstoken', 'Access Token is required').notEmpty().isString(),

  body('project_id', 'Invalid Project ID').notEmpty().isString(),
  body('tour_id', 'Invalid Tour ID').notEmpty().isString(),
  body('group_id', 'Invalid Group ID').notEmpty().isString(),

  body('order')
    .notEmpty()
    .isInt()
    .withMessage('Group order must be a valid integer'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }
    next();
  },
];

export default UpdateGroupOrderValidator;

import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import mongoose from 'mongoose';
import { Group } from '../../../types/virtualTour';

export async function AddGroupToTour (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const organization_id = req.organization_id;
    const { project_id, tour_id, name, icon, order } = req.body;

    if (!organization_id) {
      res.status(400).json({ status: 0, error: 'Organization ID is required' });
      return;
    }

    const groupData: Partial<Group> = {
      _id: new mongoose.Types.ObjectId().toString(),
      name,
      icon,
      order,
      subgroups: {},
    };

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.AddGroupToTour(tour_id, groupData);

    if (!updatedTour) {
      res.status(404).json({ status: 0, error: 'Tour not found' });
      return;
    }

    res.status(200).json({
      status: 1,
      data: updatedTour.groups[groupData._id as string],
    });
  } catch (error) {
    logger.error('Error in AddGroupToTour', { error });
    res.status(500).json({ status: 0, error: `Error creating group: ${error}` });
  }
}

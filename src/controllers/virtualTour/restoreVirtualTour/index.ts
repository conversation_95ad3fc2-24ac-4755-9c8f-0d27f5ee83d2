import { ExtendedRequest } from '../../../types/extras';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreVirtualTour (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.body;
  const virtualTour = new VirtualTourModule( organization_id, project_id);
  const trash_id = request.body.trash_id;
  await virtualTour
    .restoreVirtualTour(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'virtualTour got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreVirtualTour', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore virtualTour : '+ error });
    });
}

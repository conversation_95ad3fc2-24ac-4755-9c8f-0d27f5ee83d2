// First, import the Image type to ensure compatibility
import { VirtualTourModule, invalidateTourAPIs } from '../../../modules/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import axios from 'axios';
import {  tourType, VirtualTour, Image, EnvVar, tile_rendering_status } from '../../../types/virtualTour';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import fs from 'fs';
import path from 'path';
import { downloadImageAsFile } from '../../../helpers/downloadFile';
import { processStereoImages } from '../../../helpers/storageUpload';

// Define interface for external API data
interface ImageLink {
  'dest-image'?: string;
  position: string;
  [key: string]: any;
}

interface ImageData {
  name?: string;
  thumbnail?: string;
  url?: string;
  rotation?: string;
  links?: Record<string, ImageLink>;
  [key: string]: any;
}

interface ExternalData {
  images: Record<string, ImageData>;
  displayName?: string;
  details?: string;
  [key: string]: any;
}

// Define our transformed data types to match VirtualTour requirements
interface TransformedImageLink {
  _id: string;
  position: {
    x: string;
    y: string;
    z: string;
  };
  text: string;
  destination_img_id: string;
}

// Helper functions
function getDestinationImageId (destImageUrl: string | undefined, images: Record<string, ImageData>): string {
  if (!destImageUrl) {
    return '';
  }

  for (const [imageId, imageData] of Object.entries(images)) {
    if (imageData.url === destImageUrl) {
      return imageId;
    }
  }

  return '';
}

function getDestinationImageName (destImageUrl: string | undefined, images: Record<string, ImageData>): string {
  if (!destImageUrl) {
    return '';
  }

  for (const [, imageData] of Object.entries(images)) {
    if (imageData.url === destImageUrl) {
      return imageData.name || '';
    }
  }

  return '';
}
export function convertAFrameRotationToSphericalCoordinates (
  aframeRotation: string | number[] | null | undefined,
): string {
  const fallback = '0 0 1';
  if (!aframeRotation) {
    return fallback;
  }
  let rotationValues: number[] = [0, 0, 1]; // Default fallback
  try {
    if (Array.isArray(aframeRotation)) {
      rotationValues = aframeRotation.map((val) => Number(val) || 0);
    } else if (typeof aframeRotation === 'string') {
      rotationValues = aframeRotation
        .split(' ')
        .map((val) => Number(val) || 0);
    }
  } catch (error) {
    console.log('Error parsing A-Frame rotation values:', error);
    return fallback;
  }

  if (rotationValues[1] !== null && rotationValues[1] !== undefined) {
    const degToRad = (deg: number): number => (deg * Math.PI) / 180;
    const radToDeg = (rad: number): number => (rad * 180) / Math.PI;

    const yRotationRad = degToRad((rotationValues[1] % 360) as number);

    // A-Frame default assumption: phi ≈ 1.5 rad (~86°)
    const phi = 1.5;
    const theta = yRotationRad;
    const radius = 1;  // Distance from the origin

    // Spherical → Cartesian
    const x = radius * Math.sin(phi) * Math.sin(theta);
    const y = radius * Math.cos(phi);
    const z = radius * Math.sin(phi) * Math.cos(theta);

    // Cartesian → Spherical
    const cartesianRadius = Math.sqrt((x * x) + (y * y) + (z * z));
    const elevation = radToDeg(Math.asin(y / cartesianRadius));
    let azimuth = radToDeg(Math.atan2(x, z));

    // Normalize azimuth to 0–360
    if (azimuth < 0) {
      azimuth += 360;
    }

    const azimuthStr = (Math.round(azimuth * 100) / 100).toString();
    const elevationStr = (Math.round(elevation * 100) / 100).toString();
    const radiusStr = (Math.round(cartesianRadius * 100) / 100).toString();

    return `${azimuthStr} ${elevationStr} ${radiusStr}`;
  }
  return fallback;
}

// Function to transform external data to our schema
async function transformToDbSchema (
  data: ExternalData,
  organization_id: string,
  project_id: string,
  tour_id: string,
  isStereo: boolean = false,
): Promise<Partial<VirtualTour>> {
  try {
    const { images } = data;

    if (!images) {
      throw new Error('No images found in the data');
    }
    const tourModule = new VirtualTourModule(organization_id, project_id);
    const storagePath = (tourModule.storagepath +'/'+tour_id) as string;
    // Create the updated tour object with only required fields
    // Use Partial<VirtualTour> to match UpdateTour parameter type
    const transformedData: Partial<VirtualTour> = {
      _id: tour_id,
      type: tourType.CUSTOM,
      images: {}, // Will be populated below
      updated_at: new Date().toISOString(),
    };

    // Transform images
    let order = 1;
    for (const [imageId, imageData] of Object.entries(images)) {
      // Create link objects according to our schema
      const links: Record<string, TransformedImageLink> = {};
      if (imageData.links) {
        for (const [linkId, linkData] of Object.entries(imageData.links)) {
          // Get destination image name for the link text
          const destImageName = getDestinationImageName(linkData['dest-image'], images);

          // Parse position coordinates
          const positionParts = linkData.position.split(' ');
          links[linkId] = {
            _id: linkId,
            position: {
              x: positionParts[0] || '0',
              y: positionParts[1] || '0',
              z: positionParts[2] || '0',
            },
            text: destImageName, // Use destination image name as link text
            destination_img_id: getDestinationImageId(linkData['dest-image'], images),
          };
        }
      }

      // Create a unique subfolder for each image in the output directory
      const outputDir = path.join(process.cwd(), 'output');
      const imageSubfolder = path.join(outputDir, imageId);
      if (!fs.existsSync(imageSubfolder)) {
        fs.mkdirSync(imageSubfolder, { recursive: true });
      }
      const filesToUpload: { [fieldname: string]: Express.Multer.File[] } = {};

      // Download images as files (pass the subfolder path)
      const thumbnailFile = await downloadImageAsFile(imageData.thumbnail, 'thumbnail', imageSubfolder);
      if (thumbnailFile) {
        filesToUpload.thumbnail = [thumbnailFile];
      }

      let thumbnailUrl = '';
      let imageUrl = '';
      let stereoThumbnailUrl = '';
      let stereoUrl = '';

      if (isStereo) {
        // Use the helper function for stereo processing
        const processedImages = await processStereoImages(
          imageData.url || '',
          imageData.thumbnail || '',
          storagePath,
          imageId,
        );
        imageUrl = processedImages.finalUrl;
        thumbnailUrl = processedImages.finalThumbnail;
        stereoUrl = processedImages.stereoUrl;
        stereoThumbnailUrl = processedImages.stereoThumbnailUrl;
      } else {
        // Original behavior for non-stereo images
        // Upload thumbnail separately
        if (filesToUpload.thumbnail) {
          const thumbnailFiles = { 'thumbnail': filesToUpload.thumbnail };
          const uploadedThumbnail = await UploadUnitplanFiles(thumbnailFiles, storagePath);
          thumbnailUrl = uploadedThumbnail.thumbnail || '';
        }

        // Ensure subfolder exists for main url
        if (!fs.existsSync(imageSubfolder)) {
          fs.mkdirSync(imageSubfolder, { recursive: true });
        }

        const downloadedImageFile = await downloadImageAsFile(imageData.url, 'url', imageSubfolder);
        if (downloadedImageFile) {
          filesToUpload.url = [downloadedImageFile];
        }

        if (filesToUpload.url) {
          const imageFiles = { 'url': filesToUpload.url };
          const uploadedURL = await UploadUnitplanFiles(imageFiles, storagePath);
          imageUrl = uploadedURL.url || '';
        }
      }
      const convertedRotation = convertAFrameRotationToSphericalCoordinates(imageData.rotation);
      // Create image object that matches Image type
      const imageObj: Image = {
        id: imageId,
        name: imageData.name || `Image ${order + 1}`,
        thumbnail: thumbnailUrl || '',
        url: imageUrl || '',
        rotation: convertedRotation || '0 0 0',
        groupId: '',  // Add default value
        subGroupId: '', // Add default value
        links: {}, // Initialize links as an empty object
        order: order, // Assign the current order value
        tile_rendering_status: tile_rendering_status.NOT_STARTED,
        tile_rendering_failed_info: null,
        updated_at: new Date().toISOString(),
      };

      // Add stereo fields if isStereo is true
      if (isStereo) {
        imageObj.stereoThumbnail = stereoThumbnailUrl || '';
        imageObj.stereoUrl = stereoUrl || '';
      }

      // Only add links if they exist
      if (Object.keys(links).length > 0) {
        imageObj.links = links;
      }

      // Only add order if it's needed
      if (order > 0) {
        imageObj.order = order;
      }

      transformedData.images![imageId] = imageObj;
      order++;
    }

    return transformedData;
  } catch (error) {
    logger.error('Error transforming data:', error);
    throw new Error(`Failed to transform data: ${(error as Error).message}`);
  }
}

// Main API handler
export async function ConvertExternalTour (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { organization_id } = request;

  if (!organization_id) {
    logger.error('Organization ID not found in request headers', { headers: request.headers });
    response.status(500).json({ status: 0, error: 'Organization ID not found' });
    return;
  }

  const { link, project_id, tour_id, isStereo } = request.body;
  if (!link || !project_id || !tour_id) {
    logger.error('Missing required fields', { link, project_id, tour_id });
    response.status(400).json({ status: 0, error: 'Link, Project ID and Tour ID are required' });
    return;
  }

  try {
    const virtualTour = new VirtualTourModule(organization_id, project_id);

    const existingTour = await virtualTour.GetTourById(tour_id);

    if (!existingTour) {
      response.status(404).json({ status: 0, error: 'Tour not found' });
      return;
    }

    // Extract userId and project from the link
    const urlParts = link.split('/');
    const userId = urlParts[4];
    const project = urlParts[5];

    if (!userId || !project) {
      response.status(400).json({ status: 0, error: 'Invalid URL format' });
      return;
    }

    const apiUrl = `https://cloudfunctions-172924419383.asia-south1.run.app/getImages?userid=${userId}&project=${project}`;

    const externalResponse = await axios.get(apiUrl);
    console.log('External Response:', externalResponse.data);
    const externalData = externalResponse.data;
    const isStereoType = externalResponse.data.projectsettings.stereotype === 1 || false;
    console.log('isStereoType', isStereoType);
    const transformedData = await transformToDbSchema(
      externalData,
      organization_id,
      project_id,
      tour_id,
      isStereoType || isStereo,
    );

    if (transformedData.type === tourType.CUSTOM) {
      transformedData.link = null;
    }

    // Update existing tour
    const result = await virtualTour.UpdateTour(tour_id, transformedData);

    const storagePath = (virtualTour.storagepath +'/'+tour_id) as string;
    let batch = [];
    const batchSize = 9;
    const totalImages = transformedData.images ? Object.values(transformedData.images).length : 0;

    if (transformedData.images){
      for (let i=0;i < totalImages;i+=batchSize){
        const slicedImages = Object.values(transformedData.images).slice(i, i+batchSize);
        const batchData = slicedImages.map((image) => ({
          id: image.id,
          url: image.url,
        }));
        batch.push(batchData);
        const envVars : EnvVar[]  = [
          { name: 'project_id', value: project_id },
          { name: 'organization_id', value: organization_id },
          { name: 'tour_id', value: tour_id },
          { name: 'storagepath', value: storagePath},
          { name: 'imageData', value: JSON.stringify(batch.flat())},
          { name: 'type', value: tourType.CUSTOM },
          { name: 'jobType', value: 'add_image' },
        ];
        const jobId = 'tile-generator';
        const runJob = new cloudRunJobModule;
        runJob.runJobExecution(jobId, envVars);
        batch = []; // Remove uploaded imageData
      }
    }
    if (transformedData.type === tourType.CUSTOM && result && 'link' in result) {
      const updatedTour = await virtualTour.RemoveField(tour_id, 'link');

      if (updatedTour) {
        response.status(200).json({
          status: 1,
          data: updatedTour,
          message: 'Tour updated successfully',
        });
        return;
      }
    }

    if (!result) {
      response.status(500).json({ status: 0, error: 'Failed to update tour data' });
      return;
    }

    response.status(200).json({
      status: 1,
      data: result,
      message: 'Tour updated successfully',
    });

    // Invalidate tour APIs (non-blocking)
    invalidateTourAPIs(organization_id, project_id, tour_id).then((invalidateRes) => {
      logger.info('Tour APIs invalidated successfully', { result: invalidateRes });
    }).catch((err) => {
      logger.error('Error invalidating tour APIs', { error: err });
    });
  } catch (error) {
    logger.error('Error in ConvertExternalTour', { error });
    response.status(500).json({ status: 0, error: `Error converting tour: ${error}` });
  }
}

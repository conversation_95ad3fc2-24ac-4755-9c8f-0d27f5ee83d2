import { VirtualTourModule } from '../../../modules/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetAllTour (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { organization_id } = request;
  const { project_id } = request.params;

  if (!organization_id) {
    logger.error('Organization ID not found in request', { headers: request.headers });
    response.status(500).json({ status: 0, error: 'Organization ID not found' });
    return;
  }

  try {

    console.log(organization_id, project_id);

    const virtualTour = new VirtualTourModule(organization_id, project_id);
    const virtualTourData = await virtualTour.GetAllTours();

    if (!virtualTourData) {
      logger.warn('No tours found', { organization_id, project_id });
      response.status(404).json({ status: 0, error: 'No tours found' });
      return;
    }

    response.status(200).json({ status: 1, data: virtualTourData });
  } catch (error) {
    logger.error('Error fetching virtual tour', {error});
    response.status(500).json({ status: 0, error: `Error fetching virtual tour: ${error}` });
  }
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, param } from 'express-validator';

const forbiddenQueryKeys = ['unitNumber', 'floorPlanImage', 'floorPlan', 'unitImage'];

const getUnitsValidator = [
  param('projectId', 'ID is required').notEmpty(),
  param('organization_id', 'Organization is required').notEmpty(),

  // Check for forbidden query keys
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    // Check if validation failed
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
    }

    // Warn if any forbidden query keys are present
    const invalidKeys = forbiddenQueryKeys.filter((key) => key in req.query);

    if (invalidKeys.length > 0) {
      res.status(400).json({
        error: 'Invalid query parameters detected.',
        invalidKeys,
        message: `The following keys are not allowed in query: ${invalidKeys.join(', ')}`,
      });
    }

    next();
  },
];

export default getUnitsValidator;

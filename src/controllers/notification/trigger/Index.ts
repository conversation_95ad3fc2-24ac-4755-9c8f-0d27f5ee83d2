import { Request, Response } from 'express';
import { NotificationModule } from '../../../modules/notification';
import logger from '../../../config/logger';
import { UserModule } from '../../../modules/user';
import { UserDetailsWithFCM } from '../../../types/meetings';

export async function sendNotificationController (req: Request, res: Response): Promise<void> {
  try {
    const { _id, notification, data, url } = req.body;
    const type = data?.type;

    const User = new UserModule();
    const userDetail = await User.GetUserDetails(_id) as UserDetailsWithFCM;

    if (!userDetail) {
      res.status(404).json({
        status: 0,
        message: 'User not found',
        error: 'No user details available for the given ID',
      });
      return;
    }

    const tokens = userDetail.fcmToken;
    if (!tokens || (Array.isArray(tokens) && tokens.length === 0)) {
      res.status(200).json({
        status: 0,
        message: 'No FCM tokens available',
        error: 'User has no registered FCM tokens',
      });
      return;
    }

    const notificationModule = new NotificationModule(_id);

    // Handle based on data.type
    if (type === 'meeting_reminder') {
      if (!url) {
        res.status(400).json({
          status: 0,
          message: 'URL is required for meeting_reminder',
        });
        return;
      }
      await notificationModule.CreateNotification(_id, data.organization, url);
    } else if (type !== 'invite_sent') {
      res.status(400).json({
        status: 0,
        message: `Unhandled notification type: ${type}`,
      });
      return;
    }

    data.user_id = _id;
    const result = await notificationModule.sendNotification({
      tokens,
      notification,
      data,
    });

    res.status(200).json({
      status: 1,
      message: 'Notification sent successfully',
      ...result,
    });
  } catch (error) {
    logger.error('Error sending notification', { error });
    res.status(500).json({
      status: 0,
      message: 'Failed to send notification',
      error: error instanceof Error ? error.message : error,
    });
  }
}

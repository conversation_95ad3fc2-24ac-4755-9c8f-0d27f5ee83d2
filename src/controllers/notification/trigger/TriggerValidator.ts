import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

export const validateNotificationPayload = [
  // Validate user ID
  body('_id')
    .notEmpty()
    .withMessage('User ID is required')
    .isString()
    .withMessage('User ID must be a string'),

  // Validate notification object
  body('notification')
    .exists()
    .withMessage('Notification object is required')
    .isObject()
    .withMessage('Notification must be an object'),

  body('notification.title')
    .exists()
    .withMessage('Notification title is required')
    .isString()
    .withMessage('Notification title must be a string')
    .trim()
    .notEmpty()
    .withMessage('Notification title cannot be empty'),

  body('notification.body')
    .exists()
    .withMessage('Notification body is required')
    .isString()
    .withMessage('Notification body must be a string')
    .trim()
    .notEmpty()
    .withMessage('Notification body cannot be empty'),

  body('data.organization', 'Organization is required '),

  // Validate optional data object
  body('data')
    .optional()
    .isObject()
    .withMessage('Data must be an object')
    .custom((value) => {
      // Ensure all data values are strings
      Object.entries(value).forEach(([key, val]) => {
        if (typeof val !== 'string') {
          throw new Error(`Data value for key "${key}" must be a string`);
        }
      });
      return true;
    }),

  body('data.type')
    .notEmpty()
    .withMessage('Notification type is required')
    .isIn(['meeting_reminder', 'invite_sent'])
    .withMessage('Invalid notification type'),

  // Middleware to handle validation errors
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array().map((error) => ({
          field: error,
          message: error.msg,
        })),
      });
      return; // Explicitly return to prevent further execution
    }

    next();
  },
];

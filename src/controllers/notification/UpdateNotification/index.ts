import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { NotificationModule } from '../../../modules/notification';
import logger from '../../../config/logger';

export async function UpdateNotifications (
  request:ExtendedRequest,
  response:Response,
): Promise<void> {
  try {
    const body = request.body;
    const id = request.params.id;
    const notifcations = new NotificationModule(body.user_id);
    delete body.user_id;
    const notificationData = await notifcations.updateNotificationById(id, body);

    if (notificationData){
      response.status(200).json({ status: 1, data: notificationData });
    } else {
      response.status(200).json({ status: 0, data: {} });
    }
  } catch (error){
    logger.error('Error retrieving meetings:', error);
    response.status(500).json({ status: 0, error: 'Internal server error' });
  }
}

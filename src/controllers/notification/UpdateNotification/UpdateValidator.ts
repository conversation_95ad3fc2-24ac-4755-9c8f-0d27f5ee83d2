import { param, body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { NotificationStatus } from '../../../types/notification';

export const validateNotificationUpdatePayload = [
  // Validate notification ID from params
  param('id')
    .notEmpty()
    .withMessage('Notification ID is required')
    .isString()
    .withMessage('Notification ID must be a string')
    .isMongoId()
    .withMessage('Invalid notification ID format'),

  body('user_id').notEmpty().withMessage('user_id is required'),

  // Validate viewed status if present
  body('viewed')
    .optional()
    .isBoolean()
    .withMessage('Viewed status must be a boolean'),

  // Validate notification status if present
  body('status')
    .optional()
    .isString()
    .withMessage('Status must be a string')
    .isIn([NotificationStatus.ACTIVE, NotificationStatus.CANCELLED])
    .withMessage('Status must be one of: active, cancelled'),

  // Middleware to handle validation errors
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array().map((error) => ({
          field: error,
          message: error.msg,
        })),
      });
      return; // Explicitly return to prevent further execution
    }

    next();
  },
];

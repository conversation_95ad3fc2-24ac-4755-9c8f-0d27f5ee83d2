import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import { AssetType } from '../../../types/asset';
import logger from '../../../config/logger';

const updateAssetValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID  is required').notEmpty(),
  body('asset_id', 'Asset Item ID  is required').notEmpty(),
  body('type', 'Please select valid Asset item type').optional().isIn(Object.values(AssetType)),
  body('url', 'Url should be a String').optional().isString(),
  body('media_type', 'Media Type should be a String').optional().isString(),
  body('updated_at', 'Updated At should be a String').optional().isString(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error:', {message: errors.array()});
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default updateAssetValidate;

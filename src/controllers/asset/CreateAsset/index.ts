import {Response} from 'express';
import { FileRequest } from '../../../types/extras';
import { Types } from 'mongoose';
import { AssetsModule } from '../../../modules/asset';
import logger from '../../../config/logger';

export default async function createAsset (request:FileRequest, response:Response):Promise<void> {
    interface createAsset {
      project_id: string,
      file_name:string,
      file_type: string,
      file_url: string,
      media_type: string,
      updated_at:string,
    }

    const reqbody:createAsset = request.body;
    const organization_id = request.headers.organization as string;
    const asset = new AssetsModule(reqbody.project_id, organization_id);
    const id = new Types.ObjectId();

    asset.createAsset(
      {
        _id: id,
        file_type: reqbody.file_type,
        file_url: reqbody.file_url,
        file_name: reqbody.file_name,
        media_type: reqbody.media_type,
        created_at: reqbody.updated_at,
        updated_at: reqbody.updated_at,

      },
    ).then(async (res) => {
      response.send({status: 1, data: res});
      return;
    })
      .catch((error) => {
        logger.error('Error:', {message: error});
        response.send({status: 0, message: error});
        return;
      });
}

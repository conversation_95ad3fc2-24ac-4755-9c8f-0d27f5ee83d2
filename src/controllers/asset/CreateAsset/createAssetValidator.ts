import { Request, Response, NextFunction } from 'express';
import { check, validationResult } from 'express-validator';
import { AssetType } from '../../../types/asset';
import logger from '../../../config/logger';

const createAssetValidate = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const requiredTextFields = ['file_type', 'project_id'];
  const missingTextFields = requiredTextFields.filter(
    (field) => !(field in req.body),
  );

  if (missingTextFields.length > 0) {
    logger.error(`Missing text fields: ${missingTextFields.join(', ')}`);
    res.status(400).json({
      error: `Missing text fields: ${missingTextFields.join(', ')}`,
    });
    return;
  }

  await check('file_type', 'Invalid Asset type value. Please ensure that you are using a valid type value')
    .isIn(Object.values(AssetType))
    .run(req);
  check('url', 'url is required').notEmpty().isString();

  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    logger.error('Errors', {message: errors.array()});
    res.status(400).json({ error: errors.array() });
    return;
  }
  next();
};

export default createAssetValidate;

import { Request, Response } from 'express';
import { AssetsModule } from '../../../modules/asset';
import { ProjectModule } from '../../../modules/projects';
import logger from '../../../config/logger';

export async function getListofAssets (request: Request, response: Response): Promise<void> {
  try {
    const { project_id } = request.params;
    const organization_id = request.headers.organization as string;
    const assets = new AssetsModule(project_id, organization_id);
    const project = new ProjectModule(organization_id);
    const projectDetails = await project.getProjectById(project_id);
    const assetsList = await assets.getListofAssets();

    if (assetsList && projectDetails) {
      response.status(200).json({
        status: 1,
        data: { project_name: projectDetails.name, project_description: projectDetails.description,
          assets: assetsList},
      });
    } else {
      response.status(404).json({ status: 0, error: 'Assets not found' });
    }
  } catch (error) {
    logger.error('Error:', {message: error});
    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

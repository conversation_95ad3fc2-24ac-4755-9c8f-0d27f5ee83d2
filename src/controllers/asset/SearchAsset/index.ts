import { Request, Response } from 'express';
import { AssetsModule } from '../../../modules/asset';
import logger from '../../../config/logger';

export async function searchAssets (
  request: Request,
  response: Response,
): Promise<void> {

  const {project_id} = request.params;
  const organization_id = request.headers.organization as string;
  const search = request.query.searchQuery as string;

  const assets = new AssetsModule(project_id, organization_id);

  const searchedAssets = await assets.FindAsset(search);
  if (Array.isArray(searchedAssets) && searchedAssets.length > 0) {
    response.status(200).json({ status: 1, data: searchedAssets});
  } else if (typeof searchedAssets === 'string') {
    response.status(200).json({ status: 1, data: searchedAssets });
  } else {
    logger.error('Error in Assets Scenes');
    response.status(404).json({ status: 0, error: 'No Assets found' });
  }
}

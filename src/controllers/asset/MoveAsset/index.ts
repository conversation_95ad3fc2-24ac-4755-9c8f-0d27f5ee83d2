import { Request, Response } from 'express';
import logger from '../../../config/logger';
import { AssetsModule } from '../../../modules/asset';

export async function moveAsset (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const asset = new AssetsModule(project_id, organization_id);

  const { sourceUrl, destinationPath } = request.body;

  asset.CopyAsset(sourceUrl, destinationPath)
    .then((assetUrl) => {
      response.status(200).json({ status: 1, downloadURL: assetUrl });
    }).catch((error) => {
      logger.error('Asset Item not Copied', {message: error});
      response.status(404).json({ status: 0, error: 'Asset Item not Copied'+ error });
    });

}

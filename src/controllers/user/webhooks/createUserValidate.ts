import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { UserRole } from '../../../types/organization';

const createUserValidate = [
  body('email', 'Email is required').notEmpty(),
  body('email', 'Invalid email').isEmail(),
  body('role', 'Role is required').notEmpty(),
  body('role', 'Invalid role').isIn(Object.values(UserRole)),
  body('first_name', 'First Name is required').notEmpty(),
  body('last_name', 'Last Name is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createUserValidate;

import logger from '../../../config/logger';
import { UserModule } from '../../../modules/user';
import { CreateUserInput } from '../../../types/user';
import { Request, Response } from 'express';

export function CreateUser (req: Request, res: Response): void {
  const user = new UserModule();
  const user_data: CreateUserInput = {
    first_name: req.body.first_name,
    last_name: req.body.last_name || '',
    email: req.body.email,
    uid: req.body.uid,
    role: req.body.role,
  };
  user
    .CreateUser(user_data)
    .then((userData) => {
      res.send({ status: 1, data: userData });
    })
    .catch((error) => {
      logger.error('Error in CreateUser', {message: error});
      res.send({ status: 0, error: 'error while creating'+error });
    });
}

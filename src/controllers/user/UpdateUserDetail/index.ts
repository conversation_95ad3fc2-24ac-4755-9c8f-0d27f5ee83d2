import { Request, Response } from 'express';
import logger from '../../../config/logger';
import { UserModule } from '../../../modules/user';

export async function UpdateUserDetails (request: Request, response: Response): Promise<void> {
  const userModule = new UserModule();
  try {
    const userId = request.body._id as string;
    const updateData = { ...request.body };

    // Update user details in database
    const updatedUser = await userModule.UpdateUserDetails(userId, updateData);
    response.status(200).json({
      status: 1,
      data: updatedUser,
    });
  } catch (error) {
    logger.error('Error while updating user details', { message: error });
    response.status(500).json({ status: 0, error: `Error while updating user details: ${error}` });
  }
}

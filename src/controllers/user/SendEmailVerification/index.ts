import { Request, Response } from 'express';
import { UserModule } from '../../../modules/user';
import logger from '../../../config/logger';

export async function SendEmailVerification (
  req: Request,
  res: Response,
): Promise<void> {
  const User = new UserModule();
  const email = req.body.email;
  const refererHeader = req.headers.referer;
  const defaultUrl = 'https://dashboard.propvr.tech';
  const refererUrl = Array.isArray(refererHeader) ?
    refererHeader[0] : refererHeader || defaultUrl;
  User.SendEmailVerification(email, refererUrl)
    .then(() => {
      res.send({ status: 1, data: 'Email verification sent successfully' });
    })
    .catch((error) => {
      logger.error('Failed to send Email Verification', {message: error});
      res.send({ status: 0, error: error.message });
    });
}

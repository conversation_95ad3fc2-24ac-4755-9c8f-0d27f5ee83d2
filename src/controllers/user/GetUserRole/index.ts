import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { OrganizationModule } from '../../../modules/organization';
import logger from '../../../config/logger';
export function GetUserRole (request: ExtendedRequest, response: Response): void {
  const Organization = new OrganizationModule();
  const IsAuthenticated = request.IsAuthenticated;
  const organizationId = request.headers.organization as string;
  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'sdjfsdnlksdlskdmvlksdmvlksdmvlksdmvs Not authorized' });
    return;
  }
  Organization.GetUserRole(IsAuthenticated.uid, organizationId)
    .then(async (userrole:object|null) => {
      response.send({ status: 1, data: { userrole, organizationId } });
    })
    .catch((error) => {
      logger.error('Error in GetUserRole', {message: error});
      response.send({ status: 0, error: error });
    });
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

type Middleware = (req: Request, res: Response, next: NextFunction) => void;

const userValidate: Middleware[] = [
  body('email', 'email is required').notEmpty(),
  body('password', 'password is required').notEmpty(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default userValidate;

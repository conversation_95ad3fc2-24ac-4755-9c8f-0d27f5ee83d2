import { UserCredential } from '@firebase/auth';
import { Request, Response } from 'express';
import { UserModule } from '../../../modules/user';
import { UserCred } from '../../../types/user';
import logger from '../../../config/logger';

export async function AuthenticateUser (
  req: Request,
  res: Response,
): Promise<void> {
  const User = new UserModule();
  const email = req.body.email;
  const password = req.body.password;
  User.AuthenticateUser(email, password)
    .then((UserRecord: UserCredential) => {
      const user: UserCred = UserRecord.user;
      res.send({
        status: 1,
        data: {
          email: user.email,
          uid: user.uid,
          displayName: user.displayName,
          emailVerified: user.emailVerified,
          phoneNumber: user.phoneNumber,
          photoURL: user.photoURL,
          stsTokenManager: user.stsTokenManager,
        },
        UserRecord,
      });
    })
    .catch((error) => {
      logger.error('Error in AuthenticateUser', {message: error});
      res.send({ status: 0, error: error });
    });
}

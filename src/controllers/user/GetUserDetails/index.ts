import { Response } from 'express';
import { UserModule } from '../../../modules/user';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export function GetUserDetails (request: ExtendedRequest, response: Response): void {
  const User = new UserModule();
  const IsAuthenticated = request.IsAuthenticated;

  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }

  console.log('Fetching user details for UID:', IsAuthenticated.uid);

  User.GetUserDetails(IsAuthenticated.uid)
    .then(async (userdata: object | null) => {
      console.log('User data retrieved from Firestore:', userdata);
      response.send({ status: 1, data: userdata });
    })
    .catch((error) => {
      logger.error('Error in GetUserDetails', { message: error });
      response.send({ status: 0, error: error });
    });
}

import { Request, Response } from 'express';
import { trashModule } from '../../../modules/trash';
import logger from '../../../config/logger';

export async function getTrashedDataWithSearch (
  request: Request,
  response: Response,
): Promise<void> {
  const { type, project_id, limit, pageSize, searchText } = request.body;
  const organization_id = request.headers.organization as string;

  // Convert string values to numbers for pagination with default values
  const limitNum = limit ? parseInt(limit as string) : null;
  const pageSizeNum = pageSize ? parseInt(pageSize as string) : null;
  const searchTextStr = searchText as string || '';

  logger.info('getTrashedDataWithSearch Called', {
    type,
    project_id,
    limitNum,
    pageSizeNum,
    searchTextStr,
  });

  try {
    const trash = new trashModule(organization_id);
    console.log('trash module instance:', trash);

    const result = await trash.getTrashedDataWithSearch(
      type,
      project_id,
      limitNum,
      pageSizeNum,
      searchTextStr,
    );

    response.status(201).json({
      status: 1,
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error in getTrashedDataWithSearch', { error });
    response.status(500).json({
      status: 0,
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

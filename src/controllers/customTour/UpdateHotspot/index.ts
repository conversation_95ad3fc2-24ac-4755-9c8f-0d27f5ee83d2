import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { customTourModule } from '../../../modules/customTour';
import { updateHotspot } from '../../../types/customTour';
import logger from '../../../config/logger';

export async function UpdateHotspot (
  request: ExtendedRequest,
  response: Response,
): Promise<object | void> {
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }

  const tour_id = request.body.tour_id;
  const project_id = request.body.project_id;
  const image_id = request.body.image_id;
  const hotspot_id = request.body.hotspot_id;
  const tour = new customTourModule(organization_id, tour_id, project_id);
  const updateHotspotObj: updateHotspot = {
    position: request.body.position,
    destination: request.body.destination,
  };
  tour.updateHotspot(image_id, hotspot_id, updateHotspotObj).then((imageData) => {
    response.status(201).json({ status: 1, data: imageData });
  }).catch((error) => {
    logger
      .error('Unable to update hotspot:', {message: error});
    response.send({ status: 0, error: 'Unable to update hotspot' + error });
  });

}

import { Response, NextFunction } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { validationResult, query, header } from 'express-validator';

const getImagesValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  query('tour_id', 'Tour ID is required').notEmpty(),
  (req: ExtendedRequest, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default getImagesValidate;

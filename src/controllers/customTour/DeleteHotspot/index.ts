import { Response } from 'express';
import { customTourModule } from '../../../modules/customTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
export async function DeleteHotspot (
  request: ExtendedRequest,
  response: Response,
): Promise<customTourModule | void> {
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  const tour_id = request.body.tour_id;
  const project_id = request.body.project_id;
  const image_id = request.body.image_id;
  const hotspot_id = request.body.hotspot_id;
  const tour = new customTourModule(organization_id, tour_id, project_id);
  await tour
    .deleteHotspot(image_id, hotspot_id)
    .then((deletedHotspot) => {
      response.status(201).json({ status: 1, data: deletedHotspot });
    })
    .catch((error: Error) => {
      logger
        .error('Error while deleting the hotspot:', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting the hotspot:'+ error });
    });
}

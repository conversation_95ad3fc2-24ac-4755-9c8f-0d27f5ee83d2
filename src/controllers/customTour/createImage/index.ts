import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { customTourModule } from '../../../modules/customTour/index';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import mongoose from 'mongoose';
import logger from '../../../config/logger';
// Import fs from 'fs';
export async function createImage (
  request: FileRequest,
  response: Response): Promise<void> {
  const organization_id = request.organization_id as string;
  const tour_id = request.body.tour_id;
  const project_id = request.body.project_id;
  const customTourMod = new customTourModule(organization_id, tour_id, project_id);
  const requestFiles = request.files;
  const id = new mongoose.Types.ObjectId();
  if (!requestFiles) {
    // Handle Multer errors
    response.send('Error reading file: ');
    return;
  }
  UploadUnitplanFiles(requestFiles, customTourMod.storagepath)
    .then((urlObject: { [key: string]: string }) => {
      const createCusTourObj = {
        id: id,
        name: request.body.name,
        thumbnail: urlObject.thumbnail,
        url: urlObject.url,
      };
      customTourMod.createImage(createCusTourObj)
        .then(async (res) => {
          response.json({ status: 1, data: res });
          // Await fs.promises.rm('tour_thumbnail', { recursive: true });
        })
        .catch((error) => {
          logger.error('Error:', {message: error});
          response.json({ status: 0, message: error });
        });
    })
    .catch((error: Error) => {
      logger.error('Error while uploading files:', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while uploading files' });
      console.error(error);
    });
}

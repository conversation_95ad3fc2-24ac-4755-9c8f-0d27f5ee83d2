import {  Response, NextFunction } from 'express';
import { validationResult, header } from 'express-validator';
import { ExtendedRequest } from '../../../types/extras';
interface UploadedFiles {
  thumbnail: Express.Multer.File[];
  url: Express.Multer.File[];
}

const replaceImageValidate = (
  req: ExtendedRequest,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;

  if (files) {
    if (!files.thumbnail) {
      res
        .status(400)
        .json({ error: 'Thumbnail and url field is required.' });
    } else {
      const requiredTextFields = [
        'image_id',
        'project_id',
        'tour_id',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        header('organization', 'Organization ID is required').notEmpty().run(req),
        header('accesstoken', 'Access Token is required').notEmpty().run(req);
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default replaceImageValidate;

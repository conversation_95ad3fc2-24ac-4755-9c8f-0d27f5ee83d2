import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { customTourModule } from '../../../modules/customTour';
import { FileRequest } from '../../../types/extras';
import { Response } from 'express';
import fs from 'fs';
export async function replaceImage (
  request: FileRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const tour_id = request.body.tour_id;
  const project_id = request.body.project_id;
  const customTourMod = new customTourModule(organization_id, tour_id, project_id);
  const requestFiles = request.files;
  if (!requestFiles) {
    // Handle Multer errorspro
    response.send('Error reading file: ');
    return;
  }
  UploadUnitplanFiles(requestFiles, customTourMod.storagepath)
    .then((urlObject: { [key: string]: string }) => {
      const obj = {
        image_id: request.body.image_id,
        thumbnail: urlObject.thumbnail,
        url: urlObject.url,
      };
      customTourMod.updateImage(obj).then(async (res) => {
        response.json({ status: 1, data: res });
        // Safely delete the tour_thumbnail directory to prevent race conditions
        try {
          if (fs.existsSync('tour_thumbnail')) {
            await fs.promises.rm('tour_thumbnail', { recursive: true });
          }
        } catch (error) {
          console.log('Error deleting tour_thumbnail directory:', error);
        }
      })
        .catch((err) => {
          logger
            .error('Error in replaceImage', {message: err});
          response.json({ status: 0, message: err });
        });
    })
    .catch((error: Error) => {
      response
        .status(500)
        .json({ status: 0, error: 'Error while uploading files' });
      console.error(error);
    });

}

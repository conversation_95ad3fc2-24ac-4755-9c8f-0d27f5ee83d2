import logger from '../../../config/logger';
import { customTourModule } from '../../../modules/customTour';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export async function updateImage (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const tour_id = request.body.tour_id;
  const project_id = request.body.project_id;
  const customTourMod = new customTourModule(organization_id, tour_id, project_id);
  customTourMod.updateImage(request.body).then((res) => {
    response.json({ status: 1, data: res });
  })
    .catch((error) => {
      logger
        .error('Error in updateImage', {message: error});
      response.json({ status: 0, message: error });
    });
}

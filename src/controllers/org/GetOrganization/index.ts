import { Response } from 'express';
import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import { migratedOrganizationRecords, Organization } from '../../../types/organization';
import logger from '../../../config/logger';
import { migrateOrgSchema, isOrganization } from '../../../helpers/organization';
export default async function GetOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization = new OrganizationModule();
  const IsAuthenticated = request.IsAuthenticated;
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  await organization
    .GetOrganization(organization_id)
    .then((organizations:Organization | migratedOrganizationRecords) => {
      if (!organizations) {
        response.send({ status: 0, error: 'no organization found' });
        return;
      }

      // Check if it's already in the new schema format
      if (isOrganization(organizations)) {
        // Already migrated - send as is
        response.send({ status: 1, data: organizations });
      } else {
        // Old schema - migrate it first, then send
        const migratedOrg = migrateOrgSchema(organizations as migratedOrganizationRecords);
        response.send({ status: 1, data: migratedOrg });
      }
    })

    .catch((error) => {
      logger.error('error while getting organization', {message: error});
      response.send({ status: 0, error: 'error while getting organization' + error});
    });
  return;
}

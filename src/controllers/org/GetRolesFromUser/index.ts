import { OrganizationModule } from '../../../modules/organization';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetUserRole (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization = new OrganizationModule();
  const organization_id = request.organization_id;
  const IsAuthenticated = request.IsAuthenticated;

  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }

  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  const userRole = await organization.GetUserRole(
    IsAuthenticated.uid,
    organization_id,
  );
  if (!userRole) {
    logger.error('User not found');
    response.send({ status: 0, error: 'User not found' });
    return;
  }
  response.send({ status: 1, data: userRole });
}

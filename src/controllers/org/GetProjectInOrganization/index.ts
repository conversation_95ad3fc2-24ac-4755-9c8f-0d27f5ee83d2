import { Response } from 'express';

import { ProjectModule } from '../../../modules/projects';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export default async function GetProjectInOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id;
  const project_id = request.body.project_id;
  const project = new ProjectModule(organization_id as string);
  if (!organization_id) {
    logger.error('no organization is found');
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  const projects = await project.GetProjectInOrganization(
    project_id,
  );
  response.send({ status: 1, data: projects });
  return;
}

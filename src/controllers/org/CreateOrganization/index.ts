import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { OrganizationModule, invalidateOrganizationAPIs } from '../../../modules/organization';
import {
  CreateOrganizationInput,
  Organization,
  User,
  UserRole,
} from '../../../types/organization';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
import { logoType, Theme, FontType } from '../../../types/projects';
export default async function CreateOrganization (req: ExtendedRequest, res: Response): Promise<void> {
  const IsAuthenticated = req.IsAuthenticated;
  if (!IsAuthenticated) {
    res.send({ status: 0, error: 'Not authorized' });
    return;
  }
  const organization = new OrganizationModule();
  const requestFiles = req.files;
  if (requestFiles === undefined) {
    res.status(400).json({ error: 'Image fields are required.' });
    return;
  }
  const organizationId = await organization.GenerateOrganizationId();
  const urlObject: { [key: string]: string } = await UploadUnitplanFiles(
    requestFiles,
    'CreationtoolAssets/' + organizationId,
  );
  const organization_data: CreateOrganizationInput = {
    name: req.body.name,
    founding_date: req.body.founding_date,
    contact_email: req.body.contact_email,
    phone_number: req.body.phone_number,
    address: req.body.address,
    website: req.body.website,
    max_users: 10,
    thumbnail: urlObject.thumbnail,
    organizationId: organizationId,
    unique_org_id: req.body.unique_org_id,
    organizationSettings: {
      theme: {
        theme: Theme.DARK,
        primary: '#000000',
        secondary: '#FFFFFF',
        primary_text: '#000000',
        secondary_text: '#FFFFFF',
        font_type: FontType.ROBOTO,
        font_url: 'https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap',
      },
      currency: {
        baseCurrency: 'AED',
        currency_provider: 'custom',
        currency_provider_name: '',
        exchangeRatio: [],
      },
      weblite: {
        org_logo_click_type: logoType.DEFAULT,
        mastersvg_visibility: false,
        is_broadcast_enabled: false,
        share_masterscenes: {
          whatsapp: false,
          email: false,
          twitter: false,
        },
      },
      pixelstreaming: {
        max_concurrent_sessions: 0,
        duration: 0,
      },
      salestool: {
        slots: [
          '2024-02-29T20:00:00.000Z',
          '2024-02-29T10:00:00.000Z',
          '2024-02-29T10:30:00.000Z',
          '2024-02-29T19:00:00.000Z',
          '2024-03-01T11:30:00.000Z',
          '2024-02-29T19:30:00.000Z',
          '2024-02-29T20:15:00.000Z',
          '2024-02-29T23:00:00.000Z',
        ],
        timezone: 'Asia/Kolkata',
      },
    },
  };
  organization
    .CreateOrganization(organization_data)
    .then((organizations: Organization) => {
      const organization_id = organizations._id;
      const user: User = {
        user_id: IsAuthenticated.uid,
        email: IsAuthenticated.email as string,
        first_name: IsAuthenticated.displayName || null,
        last_name: IsAuthenticated.displayName || null,
        organization_id: [organization_id],
      };
      const handleUserRoleAssignment = () => {
        const userRoleObj = {
          user_id: user.user_id,
          organizationId: organization_id,
          roleId: 'admin' as UserRole,
          email: user.email,
        };
        return organization.AssignRoleToUser(userRoleObj);
      };

      const sendResponseAndInvalidate = () => {
        res.send({ status: 1, data: organizations });
        // Invalidate organization APIs (non-blocking)
        invalidateOrganizationAPIs(organization_id).then((invalidationResult) => {
          logger.info('Organization APIs invalidated successfully', { result: invalidationResult });
        }).catch((invalidationError) => {
          logger.error('Error invalidating organization APIs', { error: invalidationError });
        });
      };

      organization
        .AddUserToOrganization(organization_id, user)
        .then(() => handleUserRoleAssignment())
        .then(() => {
          sendResponseAndInvalidate();
        })
        .catch((err) => {
          logger.error('error assigning role to admin', { message: err });
          res.send({
            status: 0,
            error: 'error assigning role to admin' + err,
          });
        })
        .catch((err) => {
          logger.error('error adding current user to the organization', {
            message: err,
          });
          res.send({
            status: 0,
            error: 'error adding current user to the organization' + err,
          });
        });
    })
    .catch((error) => {
      logger.error('error while creating', { message: error });
      res.send({ status: 0, error: 'error while creating' + error });
    });
}

import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { UserModule } from '../../../modules/user';
import { arrayToObject } from '../../../helpers/dataFormatHelper';
import logger from '../../../config/logger';
export default async function ListUsersInOrganization (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  const users = new UserModule();
  const organization_id = req.organization_id;
  const filterQuery = req.body.filter;
  if (!organization_id) {
    res.send({ status: 0, error: 'no organization is found' });
    return;
  }
  await users
    .ListUsersInOrganization(organization_id, filterQuery)
    .then((usersData) => {
      if (usersData){
        const documentsObj = arrayToObject(usersData, 'user_id');
        res.send({ status: 1, data: documentsObj });
      }
    })
    .catch((error) => {
      logger.error('Error in ListUserInOrganization', {message: error});
      res.send({ status: 0, error: error });
    });
  return;
}

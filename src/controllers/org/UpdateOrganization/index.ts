import { Response } from 'express';

import { OrganizationModule, invalidateOrganizationAPIs } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import {
  Organization,
  UpdateOrganizationInput,
  migratedOrganizationRecords,
} from '../../../types/organization';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
import { isOrganization, migrateOrgSchema } from '../../../helpers/organization';
export default async function UpdateOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization = new OrganizationModule();
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  const requestFiles = request.files;
  if (requestFiles) {
    UploadUnitplanFiles(requestFiles, 'CreationtoolAssets/'+organization_id)
      .then((urlObject: { [key: string]: string }) => {
        const updateFields: UpdateOrganizationInput = {
          ...request.body,
        };
        updateFields.thumbnail = urlObject.thumbnail;
        // UpdateFields.font_url = urlObject.font_url;
        organization
          .UpdateOrganization(organization_id, updateFields)
          .then((organizations: Organization | migratedOrganizationRecords) => {
            if (!organizations) {
              response.send({ status: 0, error: 'no organization found' });
              return;
            }
            // Check if it's already in the new schema format
            if (isOrganization(organizations)) {
              // Already migrated - send as is
              response.send({ status: 1, data: organizations });
            } else {
              // Old schema - migrate it first, then send
              const migratedOrg = migrateOrgSchema(organizations as migratedOrganizationRecords);
              response.send({ status: 1, data: migratedOrg });
            }

            // Invalidate organization APIs (non-blocking)
            invalidateOrganizationAPIs(organization_id).then((res) => {
              logger.info('Organization APIs invalidated successfully', { result: res });
            }).catch((err) => {
              logger.error('Error invalidating organization APIs', { error: err });
            });
          })
          .catch((error) => {
            logger.error('error while updating ', {message: error});
            response.send({ status: 0, error: 'error while updating' + error });
          });
      });
  } else {
    const updateFields: UpdateOrganizationInput = {
      ...request.body,
    };

    organization
      .UpdateOrganization(organization_id, updateFields)
      .then((organizations:Organization | migratedOrganizationRecords) => {
        if (!organizations) {
          response.send({ status: 0, error: 'no organization found' });
          return;
        }

        // Check if it's already in the new schema format
        if (isOrganization(organizations)) {
        // Already migrated - send as is
          response.send({ status: 1, data: organizations });
        } else {
        // Old schema - migrate it first, then send
          const migratedOrg = migrateOrgSchema(organizations as migratedOrganizationRecords);
          response.send({ status: 1, data: migratedOrg });
        }
      })
      .catch((error) => {
        logger.error('error while updating ', {message: error});
        response.send({ status: 0, error: 'error while updating' + error });
      });
  }

}

import { Response } from 'express';

import { OrganizationModule, invalidateOrganizationAPIs } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import {
  migratedOrganizationRecords,
  Organization,
  organizationSettingsInput,
} from '../../../types/organization';
import logger from '../../../config/logger';
import {generateCustomFont, generateFontLink } from '../../../helpers/projects';
import { FontType } from '../../../types/projects';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { isOrganization, migrateOrgSchema } from '../../../helpers/organization';
export default async function UpdateOrgSettings (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization = new OrganizationModule();
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  const requestFiles = request.files;
  if (requestFiles) {
    UploadUnitplanFiles(requestFiles, 'CreationtoolAssets/'+organization_id)
      .then((urlObject: { [key: string]: string }) => {
        const updateFields: organizationSettingsInput = {
          ...request.body.query,
        };
        updateFields.theme.font_url = urlObject.font_url;
        organization
          .UpdateOrganizationSettings(organization_id, updateFields)
          .then((organizations:Organization | migratedOrganizationRecords) => {
            if (!organizations) {
              response.send({ status: 0, error: 'no organization found' });
              return;
            }

            // Check if it's already in the new schema format
            if (isOrganization(organizations)) {
              console.log('is it here or not');

              // Already migrated - send as is
              response.send({ status: 1, data: organizations });
            } else {
              // Old schema - migrate it first, then send
              const migratedOrg = migrateOrgSchema(organizations as migratedOrganizationRecords);
              console.log(migratedOrg, 'why not here');

              response.send({ status: 1, data: migratedOrg });
            }

            // Invalidate organization APIs (non-blocking)
            invalidateOrganizationAPIs(organization_id).then((res) => {
              logger.info('Organization APIs invalidated successfully', { result: res });
            }).catch((err) => {
              logger.error('Error invalidating organization APIs', { error: err });
            });
          })
          .catch((error) => {
            logger.error('error while updating ', {message: error});
            response.send({ status: 0, error: 'error while updating' + error });
          });
      });
  } else {
    const updateFields: organizationSettingsInput = {
      ...request.body.query,
    };
    const theme = request.body.query?.theme;
    const font_type = request.body.query.theme?.font_type as FontType;
    if (font_type as FontType) {
      const fontLink = await generateFontLink(font_type);
      updateFields.theme.font_url = fontLink;
    }

    if (theme && request.body.query?.theme?.font_type){
      if (font_type !== 'custom'){
        const fontLink =  await generateFontLink(font_type);
        updateFields.theme.font_url = fontLink;
      }
    }

    if (theme && font_type === 'custom' && request.body.query?.theme?.font_url){
      const link = request.body.theme.font_url as string;
      const fontLink = await generateCustomFont(link);
      updateFields.theme.font_url = fontLink;
    }

    organization
      .UpdateOrganizationSettings(organization_id, updateFields)
      .then((organizations:Organization | migratedOrganizationRecords) => {
        if (!organizations) {
          response.send({ status: 0, error: 'no organization found' });
          return;
        }

        // Check if it's already in the new schema format
        if (isOrganization(organizations)) {
          // Already migrated - send as is
          response.send({ status: 1, data: organizations });
        } else {
          // Old schema - migrate it first, then send
          const migratedOrg = migrateOrgSchema(organizations as migratedOrganizationRecords);
          response.send({ status: 1, data: migratedOrg });
        }

        // Invalidate organization APIs (non-blocking)
        invalidateOrganizationAPIs(organization_id).then((res) => {
          logger.info('Organization APIs invalidated successfully', { result: res });
        }).catch((err) => {
          logger.error('Error invalidating organization APIs', { error: err });
        });
      })
      .catch((error) => {
        logger.error('error while updating ', {message: error});
        response.send({ status: 0, error: 'error while updating' + error });
      });
  }
}

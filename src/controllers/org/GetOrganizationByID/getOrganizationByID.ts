import { Response } from 'express';
import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import { migratedOrganizationRecords, Organization } from '../../../types/organization';
import { isOrganization, migrateOrgSchema } from '../../../helpers/organization';

export default async function getOrganizationByID (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organizationModule = new OrganizationModule();

  const organizationId = request.params.id;

  if (!organizationId) {
    response.send({ status: 0, error: 'No organization ID provided' });
    return;
  }

  try {
    const organization: Organization | migratedOrganizationRecords | null =
    await organizationModule.FindOrganizationById(organizationId);
    if (!organization) {
      response.send({ status: 0, error: 'no organization found' });
      return;
    }

    if (isOrganization(organization)) {
      // Already migrated - send as is
      response.send({ status: 1, data: organization });
    } else {
      response.send({ status: 0, error: 'Organization not found' });
      // Old schema - migrate it first, then send
      const migratedOrg = migrateOrgSchema(organization as migratedOrganizationRecords);
      response.send({ status: 1, data: migratedOrg });
    }
  } catch (error) {
    console.error('Error while finding organization', { message: error });
    response.send({
      status: 0,
      error: `Error while finding organization: ${error}`,
    });
  }
}

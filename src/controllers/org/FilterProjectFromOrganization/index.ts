import { Request, Response } from 'express';
import { ProjectModule } from '../../../modules/projects';

export default async function FilterProjectsFromOrganization (
  request: Request,
  response: Response,
):Promise<void>{
  console.log('------------');

  const organization_id = request.headers.organization;
  const filters = request.body.filters;
  const page = request.body.page;
  console.log('filters', filters);

  const project = new ProjectModule(organization_id as string);
  const projects = await project.FilterProjectsFromOrganization(filters, page);
  response.send({ status: 1, data: projects });
  return;
}

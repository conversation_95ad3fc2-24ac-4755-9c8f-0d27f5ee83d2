import {  Response } from 'express';
import { getUserByEmail } from '../../../helpers/authUser';
import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import { User } from '../../../types/organization';
import { CreateInvitationInput } from '../../../types/invitations';
import { InvitesModule } from '../../../modules/invites';
import logger from '../../../config/logger';
import { UserModule } from '../../../modules/user';
export default async function AddUserToOrganization (request: ExtendedRequest, response: Response):Promise<void> {
  const organization = new OrganizationModule();
  const organization_id = request.organization_id;
  const email = request.body.email;
  const role = request.body.role;

  if (!role && !email && !organization_id) {
    response.send({ status: 0, error: 'invalid details' });
    return ;
  }
  if (!organization_id){
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }
  const organizationDetails = await organization.GetOrganization(organization_id);
  if (!organizationDetails) {
    response.send({ status: 0, error: 'Invalid organization' });
    return ;
  }
  const refererHeader = request.headers.referer;
  const defaultUrl = 'https://dashboard.propvr.tech';
  const refererUrl = Array.isArray(refererHeader) ?
    refererHeader[0] : refererHeader || defaultUrl;
  try {
    const UserRecord = await getUserByEmail(email);

    if (UserRecord) {
      if (UserRecord.email) {
        const user: User = {
          user_id: UserRecord.uid,
          email: UserRecord.email,
          first_name: UserRecord.displayName || null,
          last_name: UserRecord.displayName || null,
          organization_id: [organization_id],
        };
        organization
          .AddUserToOrganization(organization_id, user)
          .then(() => {
            const userRoleObj = {
              user_id: user.user_id,
              organizationId: organization_id,
              roleId: role,
              email: user.email,
            };
            organization.AssignRoleToUser(userRoleObj).then(async () => {
              const InviteObj = new InvitesModule();
              const userModule = new UserModule();
              const username = await userModule.getUsername(UserRecord.uid);
              await InviteObj.notifyUser({
                email: email,
                organization_id: organization_id,
                organization_name: organizationDetails.name,
                role: role,
                referer_url: refererUrl,
                user_name: username || '',
              });
              response.send({ status: 1, data: true });
            }).catch((error) => {
              logger.error('Unable to assign role to user', {message: error});
              response.send({ status: 0, error: 'Unable to assign role to user'+ error });
            });
          })
          .catch((error) => {
            logger.error('Unable to add user to organization', {message: error});
            response.send({ status: 0, error: 'Unable to add user to organization' + error });
          });
      } else {
        response.send({ status: 0, error: 'user record not found' });
      }
    } else {
      const InviteObj = new InvitesModule();
      await InviteObj.SendInvite({
        email: email,
        organization_id: organization_id,
        organization_name: organizationDetails.name,
        role: role,
        referer_url: refererUrl,
        user_name: '',
      });
      const invitation_data: CreateInvitationInput = {
        email: email,
        role: role,
        organization_id: organization_id,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      InviteObj.CreateInvitation(invitation_data).then(() => {
        response.send({ status: 1, data: 'Sent user invite' });

      }).catch((err) => {
        logger.error('Error', {message: err});
        response.send({ status: 0, error: 'Error sending invite' + err });
      });
    }
  } catch (error) {
    logger.error('Error while getting user record', {message: error});
    response.send({ status: 0, error: 'Error while getting user record' });
  }

}

import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import { UserRole } from '../../../types/organization';

const addUserOrgValidate = [
  header('accesstoken', 'Access Token is required').notEmpty(),
  header('organization', 'Organization  is required').notEmpty(),
  body('email', 'email  is required').notEmpty(),
  body('email', 'Email is invalid').isEmail(),
  body('role', 'role  is required').notEmpty(),
  body('role', 'Invalid user role value. Please ensure that you are using a valid role value')
    .isIn(Object.values(UserRole)),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default addUserOrgValidate;

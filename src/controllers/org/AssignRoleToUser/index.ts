import { Response } from 'express';
import { getUser } from '../../../helpers/authUser';
import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';

export default async function AssignRoleToUser (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const user_id = request.body.user_id;
  const role = request.body.role;

  if (!user_id || !role) {
    response.send({ status: 0, error: 'Missing data' });
    return;
  }

  const organization = new OrganizationModule();
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  const userRecord = await getUser(user_id);
  if (!userRecord || !userRecord.email) {
    response.send({ status: 0, error: 'user record not found' });
    return;
  }
  if (request.IsAuthenticated) {
    if (request.IsAuthenticated.uid === user_id) {
      response.send({ status: 0, error: 'operation not allowed' });
      return;
    }
  } else {
    response.send({ status: 0, error: 'not authorized' });
    return;
  }

  const userData = await organization.checkAndCreateUser(user_id);
  if (
    userData &&
    userData.organization_id !== undefined &&
    userData.organization_id.includes(organization_id)
  ) {
    const userRoleObj = {
      user_id: user_id,
      organizationId: organization_id,
      roleId: role,
      email: userRecord.email,
    };
    const newRole = await organization.AssignRoleToUser(userRoleObj);
    response.send({ status: 1, data: newRole });
    return;
  }
  response.send({ status: 0, error: 'user not found in this organization' });
  return;
}

import { Response } from 'express';
import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import { migratedOrganizationRecords, Organization } from '../../../types/organization';
import { isOrganization, migrateOrgSchema } from '../../../helpers/organization';

export default async function getOrganizationByUniqueOrgid (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organizationModule = new OrganizationModule();

  const unique_org_id = request.params.unique_org_id;

  if (!unique_org_id) {
    response.send({ status: 0, error: 'No unique user  ID provided' });
    return;
  }

  try {
    const organization: Organization| migratedOrganizationRecords | null =
      await organizationModule.FindOrganizationByunique_org_id(unique_org_id);

    if (!organization) {
      response.send({ status: 0, error: 'no organization found' });
      return;
    }

    // Check if it's already in the new schema format
    if (isOrganization(organization)) {
      // Already migrated - send as is
      response.send({ status: 1, data: organization });
    } else {
      // Old schema - migrate it first, then send
      const migratedOrg = migrateOrgSchema(organization as migratedOrganizationRecords);
      response.send({ status: 1, data: migratedOrg });
    }
  } catch (error) {
    response.send({
      status: 0,
      error: `Error while finding organization: ${error}`,
    });
  }
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, check } from 'express-validator';

const getSvgValidate = [
  check('organization', 'Organization  is required').notEmpty(),
  check('accesstoken', 'Access Token is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default getSvgValidate;

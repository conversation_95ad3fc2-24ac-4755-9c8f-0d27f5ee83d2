import { Request, Response } from 'express';
import { MasterSVGModule } from '../../../modules/masterSVG';
import logger from '../../../config/logger';

export async function GetSvg (
  request: Request,
  response: Response,
): Promise<void> {
  const { scene_id } = request.params;
  const organization_id = request.headers.organization as string;
  //   Pass organization_id in this ProjectModule
  const svg = new MasterSVGModule(organization_id);

  const svgData = await svg.getSvgById(scene_id);

  if (svgData) {
    response.status(200).json({ status: 1, data: svgData });
  } else {
    logger.error('scene not found:');
    response.status(404).json({ status: 0, error: 'scene not found' });
  }
}

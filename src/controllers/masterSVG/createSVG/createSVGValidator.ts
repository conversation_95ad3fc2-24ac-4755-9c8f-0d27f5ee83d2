import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { masterSVGType  } from '../../../types/masterSVG';

interface UploadedFiles {
  svgFile: Express.Multer.File[];
}

const CreateSVGValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.files);
  console.log(req.body);

  const files = req.files as UploadedFiles | undefined;
  console.log(files);
  if (files) {
    const requiredTextFields = [
      'type',
      'scene_id',
    ];

    const missingTextFields = requiredTextFields.filter(
      (field) => !(field in req.body),
    );

    if (missingTextFields.length > 0) {
      res.status(400).json({
        status: 0,
        error: `Missing text fields: ${missingTextFields.join(', ')}`,
      });
      return;
    }
    // Validate type
    body('type', 'Invalid type value. Please ensure that you are using a valid type value')
      .isIn(Object.values(masterSVGType)).run(req);

    // Validate zoom levels if provided
    body('minZoomLevel')
      .optional()
      .isNumeric()
      .withMessage('minZoomLevel must be a number')
      .isFloat({ min: 0, max: 100 })
      .withMessage('minZoomLevel must be between 0 and 100')
      .run(req);

    body('maxZoomLevel')
      .optional()
      .isNumeric()
      .withMessage('maxZoomLevel must be a number')
      .isFloat({ min: 0, max: 100 })
      .withMessage('maxZoomLevel must be between 0 and 100')
      .run(req);

    // Custom validation to ensure minZoomLevel is less than maxZoomLevel if both are provided
    body().custom((value) => {
      if (value.minZoomLevel !== undefined && value.maxZoomLevel !== undefined) {
        if (value.minZoomLevel >= value.maxZoomLevel) {
          throw new Error('minZoomLevel must be less than maxZoomLevel');
        }
      }
      return true;
    }).run(req);

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      console.log(errors);
      res.status(400).json({
        status: 0,
        error: 'Validation failed',
        details: errors.array().map((error) => error.msg),
      });
      return;
    }
    next();

  } else {
    res.status(400).json({
      status: 0,
      error: 'Invalid file structure in the request.',
    });
    return;
  }
};

export default CreateSVGValidate;

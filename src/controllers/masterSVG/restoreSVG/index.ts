import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import { MasterSVGModule } from './../../../modules/masterSVG/index';
export async function restoreSVG (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const svg = new MasterSVGModule(organization_id);
  const trash_id = request.body.trash_id;
  await svg
    .restoreSVG(organization_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'SVG got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreSVG', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore SVG : '+ error });
    });
}

import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { MasterSVGModule } from '../../../modules/masterSVG';
import { invalidateMasterSceneAPIs } from '../../../modules/masterScene';
import logger from '../../../config/logger';

export default async function editSVG (
  request: FileRequest,
  response: Response,
): Promise<void> {
  interface editSVGRequest {
    svg_id: string;
    minZoomLevel?: number;
    maxZoomLevel?: number;
    applyOnLayers?: boolean; // For deepzoom functionality
  }

  const organization_id = request.headers.organization as string;
  const reqbody: editSVGRequest = request.body;

  // Validate required fields
  if (!reqbody.svg_id) {
    response.send({ status: 0, error: 'svg_id is required' });
    return;
  }

  // Validate that at least one zoom level is provided
  if (reqbody.minZoomLevel === undefined && reqbody.maxZoomLevel === undefined) {
    response.send({ status: 0, error: 'At least one zoom level (minZoomLevel or maxZoomLevel) must be provided' });
    return;
  }

  try {
    const masterSVG = new MasterSVGModule(organization_id);

    // Use the module method to update zoom levels at SVG document level
    const result = await masterSVG.editSVGZoomLevels(
      reqbody.svg_id,
      reqbody.minZoomLevel,
      reqbody.maxZoomLevel,
      reqbody.applyOnLayers,
    );

    // Check if the result is an error string
    if (typeof result === 'string') {
      response.send({ status: 0, error: result });
      return;
    }

    if (!result) {
      response.send({ status: 0, error: 'Failed to update SVG' });
      return;
    }

    logger.info('editSVG successful', {
      svg_id: reqbody.svg_id,
      minZoomLevel: reqbody.minZoomLevel,
      maxZoomLevel: reqbody.maxZoomLevel,
      applyOnLayers: reqbody.applyOnLayers,
    });

    response.send({ status: 1, data: result });

    // Invalidate master scene APIs (non-blocking)
    invalidateMasterSceneAPIs(organization_id).then((invalidateRes) => {
      logger.info('Master scene APIs invalidated successfully', { result: invalidateRes });
    }).catch((err) => {
      logger.error('Error invalidating master scene APIs', { error: err });
    });

  } catch (error) {
    logger.error('Error in editSVG', { error: error });
    response.send({ status: 0, error: 'Internal server error' });
  }
}

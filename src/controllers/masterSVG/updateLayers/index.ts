import {Request, Response} from 'express';
import { MasterSVGModule } from '../../../modules/masterSVG';
import { invalidateMasterSceneAPIs } from '../../../modules/masterScene';
import logger from '../../../config/logger';

export default async function updateLayers (request:Request, response:Response):Promise<void>{
  const organization_id = request.headers.organization as string;
  const masterSVG = new MasterSVGModule(organization_id);
  masterSVG.updateLayers(request.body, organization_id).then((res) => {
    response.send({status: 1, data: res});

    // Invalidate master scene APIs (non-blocking)
    invalidateMasterSceneAPIs(organization_id).then((invalidateRes) => {
      logger.info('Master scene APIs invalidated successfully', { result: invalidateRes });
    }).catch((err) => {
      logger.error('Error invalidating master scene APIs', { error: err });
    });
  })
    .catch((err) => {
      logger.error('scene not found:', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import { MasterSVGModule } from '../../../modules/masterSVG';
export async function deleteMasterLayers (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const layer_id = request.body.layer_id;
  const svg_id = request.body.svg_id;
  const masterSVG = new MasterSVGModule(organization_id);

  await masterSVG
    .deleteMasterLayers(svg_id, layer_id, organization_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Layer Deleted' });
    })
    .catch((error: Error) => {
      logger.error('Error in deleteLayers', {message: error});
      response
        .status(400)
        .json({ status: 0, error: 'Error in deleteLayers'+ error });
    });
}

import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
import { MasterSVGModule } from './../../../modules/masterSVG/index';
import { invalidateMasterSceneAPIs } from '../../../modules/masterScene';
export async function updateSVGLayers (
  request: FileRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const masterSVG = new MasterSVGModule(organization_id);
  let urlobject;
  if (request.files){
    urlobject =await UploadUnitplanFiles(request.files, masterSVG.storagepath+request.body.layer_id);
  }
  masterSVG.createLayers(request.body, request.body.layer_id, urlobject?.svgFile).then((res) => {
    response.send({status: 1, data: res});

    // Invalidate master scene APIs (non-blocking)
    invalidateMasterSceneAPIs(organization_id).then((invalidateRes) => {
      logger.info('Master scene APIs invalidated successfully', { result: invalidateRes });
    }).catch((err) => {
      logger.error('Error invalidating master scene APIs', { error: err });
    });
  })
    .catch((err) => {
      logger.error('Error in updateupdateSVGLayersLayers', {message: err});
      response.send({status: 0, meassage: err});
    });

}

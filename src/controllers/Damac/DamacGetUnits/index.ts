
import { Request, Response } from 'express';
import { ExternalUnitModule } from '../../../modules/damac';
import logger from '../../../config/logger';

export async function getDamacUnitsList (
  request:Request,
  response:Response,
):Promise<void>{
  try {
    const { id } = request.params;
    const externalUnit = new ExternalUnitModule();
    const unitsList = await externalUnit.getUnitsList(id);

    response.status(200).json({
      status: 1,
      data: unitsList,
    });
  } catch (error) {
    logger.error('Error in GetUnitsList controller', { error });
    response.status(500).json({
      status: 0,
      error: `Error while fetching units list${error}`,
    });
  }
}

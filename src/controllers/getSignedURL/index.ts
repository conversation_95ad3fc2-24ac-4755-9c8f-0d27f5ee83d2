import { Request, Response } from 'express';

import { StorageModule } from '../../modules/storage';
import { optionsType } from '../../types/signedURLs';
import logger from '../../config/logger';

export async function getSignedURL (
  request: Request,
  response: Response,
): Promise<void> {
  try {
    const requestData = request.body;
    const path = requestData.path;
    const actions = requestData.actions || 'write';
    const milliseconds = 15 * 60 * 1000;
    const expires = eval(requestData.expires) || Date.now() + milliseconds;
    const contentType = requestData.contentType || 'video/quicktime';

    if (path) {

      const options: optionsType = {
        version: 'v4',
        action: actions,
        expires: expires,
        contentType: contentType,
      };

      const modal = new StorageModule();

      const outputFromStorageModule = await modal.getSignedUrl(path, options);

      response.status(200).send({
        status: 1,
        data: {
          url: outputFromStorageModule.url,
          storageLink: outputFromStorageModule.thumbnail_url,
        },
      });
    } else {
      response.status(400).send({ message: 'Path is not found' });
    }
  } catch (error) {
    logger
      .error('Error in getSignedURL:', {message: error});

    response.status(400).send({ message: 'Error in getSignedURL' });
  }
}

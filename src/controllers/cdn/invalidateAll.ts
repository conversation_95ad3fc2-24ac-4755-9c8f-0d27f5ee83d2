import { Request, Response } from 'express';
import logger from '../../config/logger';
import { invalidateCacheByPattern } from '../../modules/cdn';

export async function invalidateAll (req: Request, res: Response): Promise<void> {
  try {
    const { pattern } = req.query;

    if (!pattern || typeof pattern !== 'string') {
      res.status(400).json({
        success: false,
        error: 'Pattern query parameter is required',
      });
      return;
    }

    // Convert single pattern to array for the existing function
    const patterns = [`*${pattern}`];
    const result = await invalidateCacheByPattern(patterns);
    console.log(result);

    res.json({
      message: 'Cache invalidation completed',
      pattern: pattern,
      ...result,
    });
  } catch (error) {
    logger.error('Cache invalidation endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
}

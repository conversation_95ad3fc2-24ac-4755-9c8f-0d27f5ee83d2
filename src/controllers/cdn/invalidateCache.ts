import { Request, Response } from 'express';
import logger from '../../config/logger';
import { invalidateCacheByPattern } from '../../modules/cdn';

export async function invalidateCache (req: Request, res: Response): Promise<void> {
  try {
    const { patterns } = req.body;

    if (!patterns || !Array.isArray(patterns) || patterns.length === 0) {
      res.status(400).json({
        success: false,
        error: 'Patterns array is required in request body',
      });
      return;
    }

    const result = await invalidateCacheByPattern(patterns);

    res.json({
      message: 'Cache invalidation completed',
      ...result,
    });
  } catch (error) {
    logger.error('Cache invalidation endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
}

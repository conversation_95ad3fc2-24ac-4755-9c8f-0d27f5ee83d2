import { Response, Request } from 'express';
import { fastforexModule } from '../../../modules/currencyLibrary/fastForexIndex';
import logger from '../../../config/logger';
import { currencyLibraryModule } from '../../../modules/currencyLibrary';
import { currencyLibrary, currencyProvidersEnum, topCurrencies,
  BulkOperationResult } from '../../../types/currencyLibrary';
import mongoose from 'mongoose';

export default async function BulkCurrencyRatioCreate (
  request: Request,
  response: Response,
): Promise<void>{
  try {
    const { provider_name } = request.body;
    console.log(provider_name, 'Provider Name For Future use');

    const successful: currencyLibrary[] = [];
    const failed: Array<{ currency: string; error: string }> = [];

    const processCurrency = async (currencyCode: string): Promise<currencyLibrary | null> => {
      try {
        const currencyModule = new fastforexModule();
        const resultData = await currencyModule.fetchExchangeRates(currencyCode);

        const createPayload = {
          _id: new mongoose.Types.ObjectId(),
          baseCurrency: resultData.baseCurrency,
          exchangeRatio: resultData.exchangeRatio,
          timestamp: resultData.updated,
          source: resultData.provider,
        };

        const currencyLibraryMod = new currencyLibraryModule(currencyProvidersEnum.FASTFOREX);

        const currencyResult = await currencyLibraryMod.CreateCurrency(createPayload);

        if (!currencyResult) {
          throw new Error(`Failed to create currency entry for ${currencyCode}`);
        }

        return currencyResult;
      } catch (error) {
        logger.error(`Error processing currency ${currencyCode}:`, error);
        return null;
      }
    };

    // Process all currencies concurrently
    const results = await Promise.allSettled(
      topCurrencies.map((currency) => processCurrency(currency)),
    );

    // Categorize results
    results.forEach((result, index) => {
      const currencyCode = topCurrencies[index];

      if (result.status === 'fulfilled' && result.value) {
        successful.push(result.value);
      } else {
        const errorMessage = result.status === 'rejected'
          ? result.reason?.message || 'Unknown error'
          : 'Processing failed';

        failed.push({
          currency: currencyCode,
          error: errorMessage,
        });
      }
    });

    const bulkResult: BulkOperationResult = {
      successful,
      failed,
      totalProcessed: topCurrencies.length,
      successCount: successful.length,
      failureCount: failed.length,
    };

    // Send appropriate response based on results
    if (successful.length > 0) {
      response.status(200).json({
        status: 1,
        message: `Bulk operation completed. ${successful.length} currencies created successfully${failed.length > 0 ? `
        , ${failed.length} failed` : ''}`,
        data: bulkResult,
      });
    } else {
      response.status(500).json({
        status: 0,
        message: 'All currency operations failed',
        error: bulkResult,
      });
    }

  } catch (error) {
    logger.error('An error occurred during bulk currency creation:', error);
    response.status(500).json({
      status: 0,
      error: `Error during bulk currency creation: ${error instanceof Error ? error.message : error}`,
    });
  }
}

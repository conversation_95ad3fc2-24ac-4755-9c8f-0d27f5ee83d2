import { Response, Request } from 'express';
import { fastforexModule } from '../../../modules/currencyLibrary/fastForexIndex';
import logger from '../../../config/logger';
import { currencyLibraryModule } from '../../../modules/currencyLibrary';
import { currencyProvidersEnum } from '../../../types/currencyLibrary';
import mongoose from 'mongoose';

export default async function providerExchangeRatesCreate (
  request: Request,
  response: Response,
): Promise<void> {

  const { provider_name, baseCurrency } = request.body;
  console.log(provider_name, 'Provider Name For Future use');

  try {
    const currencyModule = new fastforexModule();
    const resultData = await currencyModule.fetchExchangeRates(baseCurrency);

    const createPayload = {
      _id: new mongoose.Types.ObjectId(),
      baseCurrency: resultData.baseCurrency,
      exchangeRatio: resultData.exchangeRatio,
      timestamp: resultData.updated,
      source: resultData.provider,

    };
    const currencyLibrary = new currencyLibraryModule(currencyProvidersEnum.FASTFOREX);

    const currencyResult = await currencyLibrary.CreateCurrency(createPayload);

    if (currencyResult) {
      response.send({ status: 1, data: currencyResult });
    } else {
      logger.error('Error in Creating Base Currency Rates');
      response.send({ status: 0, error: 'Error in Creating Base Currency Rates' });
    }
  } catch (error) {
    logger.error('An error occurred while creating exchange rates.');
    response.send({
      status: 0,
      error: `Error creating exchange rates: ${error}`,
    });
  }

}

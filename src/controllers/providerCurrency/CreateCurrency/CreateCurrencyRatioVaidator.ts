import { body, validationResult, header } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import logger from '../../../config/logger';

const CreateCurrencyRatioValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('provider_name', 'Provider Name is required').notEmpty(),
  body('baseCurrency', 'Base Currency is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Errors:', {message: errors.array()});
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default CreateCurrencyRatioValidate;

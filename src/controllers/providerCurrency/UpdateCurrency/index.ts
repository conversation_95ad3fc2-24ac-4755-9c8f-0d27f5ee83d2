import { Response, Request } from 'express';
import { fastforexModule } from '../../../modules/currencyLibrary/fastForexIndex';
import logger from '../../../config/logger';
import { currencyLibraryModule } from '../../../modules/currencyLibrary';
import { currencyProvidersEnum } from '../../../types/currencyLibrary';

export default async function providerExchangeRatesUpdate (
  request: Request,
  response: Response,
): Promise<void> {

  const { provider_name, baseCurrency } = request.body;

  try {
    const currencyModule = new fastforexModule();
    const resultData = await currencyModule.fetchExchangeRates(baseCurrency);

    const uploadHandlerPayload = {
      exchangeRatio: resultData.exchangeRatio,
      timestamp: resultData.updated,
    };
    const currencyLibrary = new currencyLibraryModule(currencyProvidersEnum.FASTFOREX);

    const currencyResult = await currencyLibrary.UpdateCurrency(uploadHandlerPayload, baseCurrency, provider_name);

    if (currencyResult) {
      response.send({ status: 1, data: currencyResult });
    } else {
      logger.error('Error in updating Base Currency Rates');
      response.send({ status: 0, error: 'Error in updating Base Currency Rates' });
    }
  } catch (error) {
    logger.error('An error occurred while updating exchange rates.');
    response.send({
      status: 0,
      error: `Error updating exchange rates: ${error}`,
    });
  }

}

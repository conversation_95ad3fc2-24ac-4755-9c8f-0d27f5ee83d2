import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { currencyLibraryModule } from '../../../modules/currencyLibrary';
import { currencyLibrary } from '../../../types/currencyLibrary';

export default async function getCurrencyByBase (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {

  const provider_name = request.params.provider_name;
  const base_currency = request.params.base_currency;
  const currencyModule = new currencyLibraryModule(provider_name);

  if (!provider_name) {
    response.send({ status: 0, error: 'No Provider name provided' });
    return;
  }

  if (!base_currency) {
    response.send({ status: 0, error: 'No Base Currency provided' });
    return;
  }

  try {
    const result: currencyLibrary | void =
      await currencyModule.GetCurrency(provider_name, base_currency);

    if (result) {
      response.send({ status: 1, data: result });
    } else {
      response.send({ status: 0, error: 'Currency not found' });
    }
  } catch (error) {
    response.send({
      status: 0,
      error: `Error while fetching Currency: ${error}`,
    });
  }
}

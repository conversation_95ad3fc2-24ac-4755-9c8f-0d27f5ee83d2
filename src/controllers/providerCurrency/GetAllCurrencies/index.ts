import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { currencyLibraryModule } from '../../../modules/currencyLibrary';
import { currencyLibrary } from '../../../types/currencyLibrary';
import { arrayToObject, UnknownObject } from '../../../helpers/dataFormatHelper';

export default async function getListOfCurrencies (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {

  const provider_name = request.params.provider_name;
  const currencyModule = new currencyLibraryModule(provider_name);

  if (!provider_name) {
    response.send({ status: 0, error: 'No Provider name provided' });
    return;
  }

  try {
    const result: currencyLibrary[] | void =
      await currencyModule.GetCurrencies(provider_name);

    let listOfCurrencies = {};
    if (result) {
      listOfCurrencies = arrayToObject(result as unknown as UnknownObject[]);
      response.send({ status: 1, data: listOfCurrencies });
    } else {
      response.send({ status: 0, error: 'Currencies not found' });
    }
  } catch (error) {
    response.send({
      status: 0,
      error: `Error while fetching Currencies: ${error}`,
    });
  }
}

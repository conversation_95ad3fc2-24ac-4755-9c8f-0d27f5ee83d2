import {  Request, Response } from 'express';
import logger from '../../../config/logger';
import { MiniMapModule } from '../../../modules/miniMap';

export async function DeleteHotSpot (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const minimap_id = request.body.minimap_id as string;
  const hotspot_id = request.body.hotspot_id as string;
  if (!organization_id){
    logger.error('No organization is found ');
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }

  const miniMap = new MiniMapModule(project_id, organization_id);
  console.log('minimap_id, hotspot_id', minimap_id, hotspot_id);

  await miniMap.DeleteHotSpot(minimap_id, hotspot_id)
    .then((miniMapData) => {
      response.status(201).json({ status: 1, data: miniMapData });
    })
    .catch((error: Error) => {
      logger.error('Error while deleting the miniMap:', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting the miniMap'+ error });
    });
}

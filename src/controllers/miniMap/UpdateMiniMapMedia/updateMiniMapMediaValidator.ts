import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

interface UploadedFiles {
    low_res: Express.Multer.File[];
    high_res: Express.Multer.File[];
}

const UpdateMiniMapMediaValidator = [
  body('project_id', 'project_id is required').notEmpty(),
  body('minimap_id', 'minimap_id is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    const files = req.files as UploadedFiles | undefined;
    if (!files || (!files.low_res?.length && !files.high_res?.length)) {
      res.status(400).json({
        error: 'At least one of the files (low_res or high_res) must be provided.',
      });
      return;
    }
    next();
  },
];

export default UpdateMiniMapMediaValidator;

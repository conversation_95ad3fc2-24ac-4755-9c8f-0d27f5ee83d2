import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { Request, Response } from 'express';
import { MiniMapModule } from '../../../modules/miniMap';

export async function UpdateMiniMapMedia (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const minimap = new MiniMapModule(project_id, organization_id);
  const minimap_id = request.body.minimap_id as string;
  const reqFiles = request.files;

  if (reqFiles) {
    const mediaUrl = await UploadUnitplanFiles(reqFiles, minimap.storagepath+minimap_id);
    const mediaObj = {
      low_res: mediaUrl.low_res,
      high_res: mediaUrl.high_res,
    };
    minimap.UpdateMiniMapMedia(minimap_id, mediaObj).then((minimapData) => {
      response.status(201).json({ status: 1, data: minimapData });
    }).catch((error) => {
      logger.error('Error:', {message: error});
      response.send({ status: 0, error: 'Unable to edit media' + error });
    });
  }
}

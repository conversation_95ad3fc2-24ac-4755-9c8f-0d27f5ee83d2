import logger from '../../../config/logger';
import { Request, Response } from 'express';
import { MiniMapModule } from '../../../modules/miniMap';

export async function UpdateMiniMap (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const minimap = new MiniMapModule(project_id, organization_id);
  const minimap_id = request.body.minimap_id as string;
  const hotspot_id = request.body.hotspots?.hotspot_id as string;

  minimap.UpdateMiniMap(minimap_id, hotspot_id, request.body).then((minimapData) => {
    response.status(200).json({ status: 1, data: minimapData });
  }).catch((error) => {
    logger.error('Minimap not found ', {message: error});
    response.status(404).json({ status: 0, error: 'Minimap not found'+ error });
  });
}

import { validationResult } from 'express-validator';
import logger from '../../../config/logger';
import { ExtendedRequest } from './../../../types/extras';
import { Response, NextFunction } from 'express';
import { TypeEnum } from '../../../types/miniMap';

interface UploadedFiles {
    low_res: Express.Multer.File[];
    high_res: Express.Multer.File[];
}

const CreateMiniMapValidate = (
  req: ExtendedRequest,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  const missingFields: string[] = [];

  if (!files || !files.low_res) {
    missingFields.push('low_res');
  }
  if (!files || !files.high_res) {
    missingFields.push('high_res');
  }

  const requiredTextFields = ['project_id', 'name', 'referenceId', 'type'];
  const missingTextFields = requiredTextFields.filter(
    (field) => !(field in req.body),
  );

  missingFields.push(...missingTextFields);

  if (req.body.type && !Object.values(TypeEnum).includes(req.body.type)) {
    missingFields.push('type (invalid value)');
  }

  if (missingFields.length > 0) {
    const errorMessage = `Missing fields: ${missingFields.join(', ')}`;
    logger.error(errorMessage);
    res.status(400).send({ status: 0, error: errorMessage });
    return;
  }

  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.error('Validation failed', { errors: errors.array() });
    res.status(400).send({ status: 0, error: 'Validation failed', details: errors.array() });
    return;
  }

  next();
};

export default CreateMiniMapValidate;

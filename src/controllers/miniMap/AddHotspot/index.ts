import mongoose from 'mongoose';
import { MiniMapModule } from '../../../modules/miniMap';
import { ExtendedRequest } from '../../../types/extras';
import {  Response } from 'express';
import logger from '../../../config/logger';

export async function AddHotspot (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id;
  const minimap_id = request.body.minimap_id;
  const organization_id = request.headers.organization as string;
  const miniMap = new MiniMapModule(project_id, organization_id);
  const id = new mongoose.Types.ObjectId().toString();

  const addHotspotData = {
    hotspot_id: id,
    text: request.body.text,
    x: request.body.x,
    y: request.body.y,
    destination: request.body.destination,
  };
  miniMap
    .AddHotspot(addHotspotData, minimap_id)
    .then((hotspotData) => {
      response.status(201).json({ status: 1, data: hotspotData });
    })
    .catch((error: Error) => {
      logger.error('Error while adding the hotspot', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while adding the hotspot '+ error });
    });
}

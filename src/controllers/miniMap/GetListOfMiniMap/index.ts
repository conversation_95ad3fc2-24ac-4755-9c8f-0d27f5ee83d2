import { Request, Response } from 'express';
import logger from '../../../config/logger';
import { MiniMapModule } from '../../../modules/miniMap';

export async function GetLitsOfMiniMap (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.query.project_id as string;
  const organization_id = request.headers.organization as string;
  if (!organization_id){
    logger.error('No organization is found ');
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }
  const miniMap = new MiniMapModule(project_id, organization_id);
  const miniMapData = await miniMap.GetListOfMiniMap();

  if (miniMapData) {
    response.send({ status: 1, data: miniMapData });
  } else {
    response.send({ status: 1, data: [] });
  }
}

export enum masterLandmarkCategory {
    HEALTH = 'health',
    EDUCATION = 'education',
    ENTERTAINMENT = 'entertainment',
    TRANSIT = 'transit',
    COMMERCIAL = 'commercial',
    RELIGIOUS = 'religious',
    GOVERNMENTAL = 'governmental',
    RECREATIONAL = 'recreational',
    LANDMARK = 'landmark',
    OTHER = 'other',
    OTHERPROJECTS = 'other projects',
    BANKS = 'banks',
    METROSTATIONS = 'metro stations',
    HOTELS = 'hotels',
    WORKSPACES = 'workspaces',
    RESTAURANTS = 'restaurants',
    SHOPPING = 'shopping',
    MALLS = 'malls',
    PARKS = 'parks'
}
export type masterLandmark = {
    _id : string,
    name : string,
    thumbnail : string,
    distance : number,
    category : masterLandmarkCategory,
    walk_timing : number,
    transit_timing : number,
    car_timing : number,
    description? : string
}
export type updateMasterLandmark = {
    landmark_id?:string,
    name? : string,
    distance? : number,
    category? : masterLandmarkCategory,
    walk_timing? : number,
    transit_timing? : number,
    car_timing? : number,
    description? : string,
    thumbnail? : string
}

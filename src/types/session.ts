import { Types } from 'mongoose';
import { Leads } from './leads';
export enum SessionStatus {
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  ONGOING = 'on-going',
  ENDED = 'ended',
  ONHOLD = 'on-hold',
  CANCELLED = 'cancelled'
}
export enum SessionSource {
  API = 'api',
  DASHBOARD = 'dashboard',
  BROADCAST = 'broadcast'
}

export enum SessionType {
  DEFAULT = 'default',
  PIXELSTREAMING = 'pixel_streaming',
  ALE = 'ale',
  LARK = 'lark'
}

export enum Theme{
  PRIMARY = 'primary',
  SECONDARY ='secondary',
  TERTIARY = 'tertiary'
}

export type Session = {
  _id: Types.ObjectId;
  duration_minutes: number;
  pixel_duration_minutes: number
  organization_id: string;
  user_id: string;
  project_id?: string;
  status: SessionStatus;
  code: string;
  start: string;
  type?: string;
  invite_link: string;
  source: SessionSource;
  last_interaction_time:string,
  config?:object,
  is_scheduled: boolean,
  schedule_time: string|null,
  end_time: string,
  scheduled_end_time:string,
  instance_start_time?: string,
  instance_end_time?: string,
  description?:string,
  participants:Leads[],
  thread_id?: string,
  tag?:string,
  pixel_streaming_link?:string,
  is_pixelstreaming_active:boolean,
  is_reserved?: boolean,
};

export type Anonymous_Session = {
  _id: Types.ObjectId;
  duration_minutes: number;
  organization_id: string; //
  project_id?: string; //
  status: SessionStatus;
  code: string;
  start: string;
  type?: string;
  invite_link: string;
  source: SessionSource;
  last_interaction_time:string,
  config?:object,
  is_scheduled: boolean,
  schedule_time: string|null,
  end_time: string,
  scheduled_start_time?: string,
  actual_start_time?: string,
  actual_end_time?: string,
  instance_start_time?: string,
  instance_end_time?: string,
  description?:string,
  participants:object[],
  is_reserved?: boolean,
};

export type UpdateSessionInput = {
  session_id: string,
  lead_id?: string,
  duration_minutes: number,
  pixel_duration_minutes?: number,
  is_pixelstreaming_active?: boolean,
  type?: SessionType,
  project_id?: string,
  is_reserved?: boolean,
  instance_start_time?: string,
  instance_end_time?: string
};
export type ExportSessionInput = {
  session_id:string,
  duration:number
}
export type createSessionInput = {
  project_id?: string,
  user_id: string,
  type?: SessionType,
  source: SessionSource,
  is_scheduled: boolean,
  duration: number,
  description?: string,
  organization_id: string,
  schedule_time: string|null,
  config?:object,
  referrer?:string | undefined,
  tag?:string,
  is_reserved?:boolean
}

export type createBroadcastSessionInput = {
  project_id: string,
  organization_id: string,
  name: string,
  duration?: number,
  description?: string,
  referrer?: string | undefined,
}

export type getSlotsInput = {
  date: string,
  organization_id: string,
  zone:string
}
export type getMonthlySlotsInput = {
  project_id: string,
  from_date: string,
  to_date: string,
  organization_id: string | string[] | undefined,
  zone:string
}

export type checkInstanceAvailabilityInput = {
  start_time: string,
  end_time: string,
  organization_id: string,
}

export type getOrgSlotsInput = {
  date: string,
  organization_id: string,
  zone: string,
  project_id?: string, // Optional for backward compatibility
}

export type sessionAnalyticsQuery = {
  organization_id? : string,
  user_id?: string,
  project_id?: string,
  min_duration? : string,
  max_duration? : string,
  status?: SessionStatus,
  type?: string,
  start_date?: string,
  end_date?: string,
  is_scheduled?: boolean,
  tag?:string
}
export type bookSessionInput = {
  project_id: string,
  type: string,
  source: SessionSource,
  is_scheduled: boolean,
  description: string,
  organization_id: string,
  schedule_time: string|null,
  config:object,
  referrer:string | undefined
}

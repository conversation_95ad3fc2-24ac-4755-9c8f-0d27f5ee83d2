import { Types } from 'mongoose';
import { UserRole } from './organization';
import { SessionType } from './session';

export enum FontType{
  DEFAULT = 'default',
  CUSTOM = 'custom',
  ADVENT_PRO = 'Advent Pro',
  AFACAD_FLUX = 'Afacad Flux',
  ALBERT_SANS = 'Albert Sans',
  ALEGREYA = 'Alegreya',
  ALEGREYA_SANS_SC = 'Alegreya Sans SC',
  ALEO = 'Aleo',
  ALEXANDRIA = 'Alexandria',
  ALUMNI_SANS = 'Alumni Sans',
  ANDADA_PRO = 'Andada Pro',
  ANYBODY = 'Anybody',
  ARCHIVO = 'Archivo',
  ASAP = 'Asap',
  ASAP_CONDENSED = 'Asap Condensed',
  AZERET_MONO = 'Azeret Mono',
  BAI_JAMJUREE = 'Bai Jamjuree',
  BARLOW = 'Barlow',
  BARLOW_CONDENSED = 'Barlow Condensed',
  BARLOW_SEMI_CONDENSED = 'Barlow Semi Condensed',
  BE_VIETNAM_PRO = 'Be Vietnam Pro',
  BESLEY = 'Besley',
  BIG_SHOULDERS_DISPLAY = 'Big Shoulders Display',
  BIG_SHOULDERS_INLINE_DISPLAY = 'Big Shoulders Inline Display',
  BIG_SHOULDERS_INLINE_TEXT = 'Big Shoulders Inline Text',
  BIG_SHOULDERS_STENCIL_DISPLAY = 'Big Shoulders Stencil Display',
  BIG_SHOULDERS_STENCIL_TEXT = 'Big Shoulders Stencil Text',
  BIG_SHOULDERS_TEXT = 'Big Shoulders Text',
  BITTER = 'Bitter',
  BODONI_MODA_SC = 'Bodoni Moda SC',
  CATAMARAN = 'Catamaran',
  CHAKRA_PETCH = 'Chakra Petch',
  CHIVO = 'Chivo',
  CHIVO_MONO = 'Chivo Mono',
  COMME = 'Comme',
  COMMISSIONER = 'Commissioner',
  CORMORANT = 'Cormorant',
  CORMORANT_GARAMOND = 'Cormorant Garamond',
  CORMORANT_INFANT = 'Cormorant Infant',
  CRIMSON_PRO = 'Crimson Pro',
  DOTO = 'Doto',
  ENCODE_SANS_CONDENSED = 'Encode Sans Condensed',
  ENCODE_SANS_EXPANDED = 'Encode Sans Expanded',
  ENCODE_SANS_SC = 'Encode Sans SC',
  ENCODE_SANS_SEMI_CONDENSED = 'Encode Sans Semi Condensed',
  ENCODE_SANS_SEMI_EXPANDED = 'Encode Sans Semi Expanded',
  EPILOGUE = 'Epilogue',
  EXO = 'Exo',
  EXO_2 = 'Exo 2',
  FAHKWANG = 'Fahkwang',
  FAUSTINA = 'Faustina',
  FIGTREE = 'Figtree',
  FIRA_SANS = 'Fira Sans',
  FIRA_SANS_CONDENSED = 'Fira Sans Condensed',
  FIRA_SANS_EXTRA_CONDENSED = 'Fira Sans Extra Condensed',
  FOLDIT = 'Foldit',
  FRAUNCES = 'Fraunces',
  FUNNEL_SANS = 'Funnel Sans',
  GANTARI = 'Gantari',
  GEIST = 'Geist',
  GEIST_MONO = 'Geist Mono',
  GENOS = 'Genos',
  GEOLOGICA = 'Geologica',
  GEORAMA = 'Georama',
  GLORY = 'Glory',
  GLUTEN = 'Gluten',
  GOTHIC_A1 = 'Gothic A1',
  GRANDSTANDER = 'Grandstander',
  GRENZE = 'Grenze',
  GRENZE_GOTISCH = 'Grenze Gotisch',
  HAHMLET = 'Hahmlet',
  HANDJET = 'Handjet',
  HANKEN_GROTESK = 'Hanken Grotesk',
  HEEBO = 'Heebo',
  HEPTA_SLAB = 'Hepta Slab',
  HOST_GROTESK = 'Host Grotesk',
  HUBOT_SANS = 'Hubot Sans',
  IMBUE = 'Imbue',
  INTER = 'Inter',
  INTER_TIGHT = 'Inter Tight',
  JOSEFIN_SANS = 'Josefin Sans',
  JOSEFIN_SLAB = 'Josefin Slab',
  JOST = 'Jost',
  KANIT = 'Kanit',
  KANTUMRUY_PRO = 'Kantumruy Pro',
  KARLA = 'Karla',
  KODCHASAN = 'Kodchasan',
  KRUB = 'Krub',
  KUFAM = 'Kufam',
  KULIM_PARK = 'Kulim Park',
  KUMBH_SANS = 'Kumbh Sans',
  LABRADA = 'Labrada',
  LATO = 'Lato',
  LEAGUE_SPARTAN = 'League Spartan',
  LEXEND = 'Lexend',
  LEXEND_DECA = 'Lexend Deca',
  LEXEND_EXA = 'Lexend Exa',
  LEXEND_GIGA = 'Lexend Giga',
  LEXEND_MEGA = 'Lexend Mega',
  LEXEND_PETA = 'Lexend Peta',
  LEXEND_TERA = 'Lexend Tera',
  LEXEND_ZETTA = 'Lexend Zetta',
  LIBRE_FRANKLIN = 'Libre Franklin',
  LINEFONT = 'Linefont',
  LISU_BOSA = 'Lisu Bosa',
  LITERATA = 'Literata',
  LIVVIC = 'Livvic',
  MALI = 'Mali',
  MANUALE = 'Manuale',
  MERRIWEATHER_SANS = 'Merriweather Sans',
  MOHAVE = 'Mohave',
  MONA_SANS = 'Mona Sans',
  MONTSERRAT = 'Montserrat',
  MONTSERRAT_ALTERNATES = 'Montserrat Alternates',
  MULISH = 'Mulish',
  MURECHO = 'Murecho',
  NEWSREADER = 'Newsreader',
  NIRAMIT = 'Niramit',
  NOTO_KUFI_ARABIC = 'Noto Kufi Arabic',
  NOTO_RASHI_HEBREW = 'Noto Rashi Hebrew',
  NOTO_SANS = 'Noto Sans',
  NOTO_SANS_ARABIC = 'Noto Sans Arabic',
  NOTO_SANS_ARMENIAN = 'Noto Sans Armenian',
  NOTO_SANS_BENGALI = 'Noto Sans Bengali',
  NOTO_SANS_CANADIAN_ABORIGINAL = 'Noto Sans Canadian Aboriginal',
  NOTO_SANS_CHAM = 'Noto Sans Cham',
  NOTO_SANS_CHEROKEE = 'Noto Sans Cherokee',
  NOTO_SANS_DEVANAGARI = 'Noto Sans Devanagari',
  NOTO_SANS_DISPLAY = 'Noto Sans Display',
  NOTO_SANS_ETHIOPIC = 'Noto Sans Ethiopic',
  NOTO_SANS_GEORGIAN = 'Noto Sans Georgian',
  NOTO_SANS_GUJARATI = 'Noto Sans Gujarati',
  NOTO_SANS_GURMUKHI = 'Noto Sans Gurmukhi',
  NOTO_SANS_HEBREW = 'Noto Sans Hebrew',
  NOTO_SANS_KANNADA = 'Noto Sans Kannada',
  NOTO_SANS_KHMER = 'Noto Sans Khmer',
  NOTO_SANS_LAO = 'Noto Sans Lao',
  NOTO_SANS_LAO_LOOPED = 'Noto Sans Lao Looped',
  NOTO_SANS_MALAYALAM = 'Noto Sans Malayalam',
  NOTO_SANS_MEETEI_MAYEK = 'Noto Sans Meetei Mayek',
  NOTO_SANS_MONO = 'Noto Sans Mono',
  NOTO_SANS_MYANMAR = 'Noto Sans Myanmar',
  NOTO_SANS_ORIYA = 'Noto Sans Oriya',
  NOTO_SANS_SINHALA = 'Noto Sans Sinhala',
  NOTO_SANS_SYMBOLS = 'Noto Sans Symbols',
  NOTO_SANS_SYRIAC = 'Noto Sans Syriac',
  NOTO_SANS_SYRIAC_EASTERN = 'Noto Sans Syriac Eastern',
  NOTO_SANS_TAMIL = 'Noto Sans Tamil',
  NOTO_SANS_TELUGU = 'Noto Sans Telugu',
  NOTO_SANS_THAANA = 'Noto Sans Thaana',
  NOTO_SANS_THAI = 'Noto Sans Thai',
  NOTO_SANS_THAI_LOOPED = 'Noto Sans Thai Looped',
  NOTO_SERIF = 'Noto Serif',
  NOTO_SERIF_ARMENIAN = 'Noto Serif Armenian',
  NOTO_SERIF_BENGALI = 'Noto Serif Bengali',
  NOTO_SERIF_DEVANAGARI = 'Noto Serif Devanagari',
  NOTO_SERIF_DISPLAY = 'Noto Serif Display',
  NOTO_SERIF_ETHIOPIC = 'Noto Serif Ethiopic',
  NOTO_SERIF_GEORGIAN = 'Noto Serif Georgian',
  NOTO_SERIF_GUJARATI = 'Noto Serif Gujarati',
  NOTO_SERIF_GURMUKHI = 'Noto Serif Gurmukhi',
  NOTO_SERIF_HEBREW = 'Noto Serif Hebrew',
  NOTO_SERIF_KANNADA = 'Noto Serif Kannada',
  NOTO_SERIF_KHMER = 'Noto Serif Khmer',
  NOTO_SERIF_LAO = 'Noto Serif Lao',
  NOTO_SERIF_MALAYALAM = 'Noto Serif Malayalam',
  NOTO_SERIF_MYANMAR = 'Noto Serif Myanmar',
  NOTO_SERIF_SINHALA = 'Noto Serif Sinhala',
  NOTO_SERIF_TAMIL = 'Noto Serif Tamil',
  NOTO_SERIF_TELUGU = 'Noto Serif Telugu',
  NOTO_SERIF_THAI = 'Noto Serif Thai',
  NOTO_SERIF_TIBETAN = 'Noto Serif Tibetan',
  NUNITO = 'Nunito',
  NUNITO_SANS = 'Nunito Sans',
  ONEST = 'Onest',
  OPEN_SANS = 'Open Sans',
  OUTFIT = 'Outfit',
  OVERPASS = 'Overpass',
  PATHWAY_EXTREME = 'Pathway Extreme',
  PETRONA = 'Petrona',
  PIAZZOLLA = 'Piazzolla',
  PLATYPI = 'Platypi',
  PLAYFAIR = 'Playfair',
  PLAYFAIR_DISPLAY = 'Playfair Display',
  PLUS_JAKARTA_SANS = 'Plus Jakarta Sans',
  POPPINS = 'Poppins',
  PROMPT = 'Prompt',
  PROZA_LIBRE = 'Proza Libre',
  PUBLIC_SANS = 'Public Sans',
  RADIO_CANADA = 'Radio Canada',
  RALEWAY = 'Raleway',
  RASA = 'Rasa',
  RED_HAT_DISPLAY = 'Red Hat Display',
  RED_HAT_MONO = 'Red Hat Mono',
  RED_HAT_TEXT = 'Red Hat Text',
  REDDIT_SANS = 'Reddit Sans',
  RETHINK_SANS = 'Rethink Sans',
  ROBOTO = 'Roboto',
  ROBOTO_CONDENSED = 'Roboto Condensed',
  ROBOTO_MONO = 'Roboto Mono',
  ROBOTO_SERIF = 'Roboto Serif',
  ROBOTO_SLAB = 'Roboto Slab',
  ROKKITT = 'Rokkitt',
  ROSARIO = 'Rosario',
  RUBIK = 'Rubik',
  SAIRA = 'Saira',
  SAIRA_CONDENSED = 'Saira Condensed',
  SAIRA_EXTRA_CONDENSED = 'Saira Extra Condensed',
  SAIRA_SEMI_CONDENSED = 'Saira Semi Condensed',
  SARABUN = 'Sarabun',
  SCHIBSTED_GROTESK = 'Schibsted Grotesk',
  SHANTELL_SANS = 'Shantell Sans',
  SMOOCH_SANS = 'Smooch Sans',
  SOFIA_SANS = 'Sofia Sans',
  SOFIA_SANS_CONDENSED = 'Sofia Sans Condensed',
  SOFIA_SANS_EXTRA_CONDENSED = 'Sofia Sans Extra Condensed',
  SOFIA_SANS_SEMI_CONDENSED = 'Sofia Sans Semi Condensed',
  SOUR_GUMMY = 'Sour Gummy',
  SOURCE_CODE_PRO = 'Source Code Pro',
  SOURCE_SANS_3 = 'Source Sans 3',
  SOURCE_SERIF_4 = 'Source Serif 4',
  SPECTRAL = 'Spectral',
  SPLINE_SANS_MONO = 'Spline Sans Mono',
  TAVIRAJ = 'Taviraj',
  TEACHERS = 'Teachers',
  TEXTURINA = 'Texturina',
  TITILLIUM_WEB = 'Titillium Web',
  TOMORROW = 'Tomorrow',
  TOURNEY = 'Tourney',
  TRIRONG = 'Trirong',
  TRUCULENTA = 'Truculenta',
  UBUNTU_SANS = 'Ubuntu Sans',
  URBANIST = 'Urbanist',
  VAZIRMATN = 'Vazirmatn',
  VICTOR_MONO = 'Victor Mono',
  VOLLKORN = 'Vollkorn',
  WAVEFONT = 'Wavefont',
  WITTGENSTEIN = 'Wittgenstein',
  WIX_MADEFOR_TEXT = 'Wix Madefor Text',
  WORK_SANS = 'Work Sans',
  YRSA = 'Yrsa',
  YSABEAU = 'Ysabeau',
  YSABEAU_INFANT = 'Ysabeau Infant',
  YSABEAU_OFFICE = 'Ysabeau Office',
  ZILLA_SLAB = 'Zilla Slab',
}
export enum ExperienceType {
  PROPVR_360 = 'propvr_360',
  RT_APPLICATION = 'rt_application',
  EMBED_V1 = 'embed_v1',
}
export enum PropertyType {
  TOWER = 'tower',
  VILLA = 'villa',
  BOTH = 'both',
}
export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  CUSTOM = 'custom',
  GLASS = 'glass'
}

export enum ctaType {
  DEFAULT='default',
  CUSTOM='custom',
  EVENT_EMIT ='event_emit'
}
export enum logoType {
  DEFAULT='default',
  CUSTOM='custom',
}

export enum measurement_type {
  SQFT='sqft',
  SQMT='sqmt',
}

export enum bedroom_format {
  BR='BR',
  BHK='BHK',
}

export enum unitCardType {
  DEFAULT='default',
  CUSTOM='custom',
}

export enum StatusDefaultColor {
  AVAILABLE_TRANSPARENT = '#ffff',
  ONHOLD_TRANSPARENT = '#111827',
  NOT_AVAILABLE_TRANSPARENT = '#111827',
}

export type ColorMode = 'colored' | 'transparent' | 'default';

export type Interaction =
  | 'visible_clickable'
  | 'visible_not_clickable'
  | 'not_visible';

export type StatusConfig = {
  color: {
    mode: ColorMode;
    defaultColor: string;
    selectedColor?: string;
  };
  interaction: Interaction;
};

export type CustomizationSettings = {
  status_config: {
    available: StatusConfig;
    onhold: StatusConfig;
    'not available': StatusConfig;
  };
};

export type HologramTag = {
  name: string,
  color: string
}
export type generalSettings = {
  hideStatus: boolean;
  slots?: string[];
  is_enabled?: boolean;
  branding_logo?: string;
  branding_logo_dark?: string;
  lat?: number;
  timezone: string
  long?: number;
  updated_at?:string,
};
export type pixelstreamingSettings = {
  application_id?: string;
  is_enabled?: boolean;
  pixel_streaming_endpoint?: string;
  max_concurrent_sessions?: number;
  session_duration?: number;
  resource_group?: string,
  vm_scaleset_name?: string,
  min_instances?:number,
  auto_scale?: boolean,
  generated_key?: Array<{key: string, time: string}>
};
export enum SceneType {
  PROJECT = 'project',
  MASTER = 'master',
}
export type unitcardConfigType = {
  type :boolean,
  measurement:boolean,
  bedrooms:boolean,
  bathrooms:boolean,
  status:boolean,
  price:boolean,
  style:boolean,
  view:boolean,
  maid:boolean,
  floor_id:boolean,
  building_id:boolean,
  units:boolean,
  favIcon:boolean,
}

export type share_scenes_type = {
  whatsapp: boolean,
  email: boolean,
  twitter: boolean,
  // Instagram: boolean,
  // Facebook: boolean,
}

export type aleSettings = {
  is_call_enabled: boolean;
  is_cta_enabled: boolean;
  cta_type: ctaType;
  cta_name: string;
  is_unitplan_cta_enabled: boolean,
  unitplan_cta_type: ctaType,
  unitplan_cta_name: string,
  unitplan_cta_link?: string,
  is_enabled?: boolean;
  initial_scene_type?: SceneType;
  initial_scene_id?: string;
  welcome_video?: string;
  welcome_thumbnail?: string,
  shortened_link?: string;
  default_language: string,
  supported_languages?: string[]
  unit_card_customize_type?:string,
  unitcard_config: unitcardConfigType,
  currency_support?: boolean,
  svg_visibility?: boolean,
  share_scenes?: share_scenes_type,
  measurement_type?: string,
  bedroom_format?: string,
  unit_label: boolean,
  is_logo_clickable :boolean
  logo_click_type: logoType.DEFAULT,
  logo_click_link ?:string,
};

export type embedSettings = {
  is_enabled?: boolean;
};
export type salestoolSettings = {
  is_enabled?: boolean,
  session_duration?: number,
  default_experience?: SessionType,
  tags?:string[]
}
export type theme = {
  theme: Theme,
  primary:string,
  primary_text:string,
  secondary:string,
  secondary_text:string,
  font_type:FontType,
  font_url:string,
}
export type hologramSettings = {
    project_logo: string,
    project_type: string,
    project_location: string,
    amount: string
    bedrooms: number,
    thumbnail: string,
    file: string,
    tags?: HologramTag[]
}

export type bulkUpdateQuery = {
  id:string,
  order?:number,
  name?: {
    category: string;
    count: number
    }
}
export type bulkUpdateType = {
  query:bulkUpdateQuery[],
  project_id:string
}
export type galleryPayload = {
  id:string
  order?:number,
  name?:string,
}
export type bulkGalleryUpdateType = {
  query: galleryPayload[],
  project_id:string
}
export type substatusType = {
  name: string;
}

export type statusCategoryType = {
  _id?: string | Types.ObjectId;
  name: string;
  substatus: substatusType[];
  order?: number;
  created_at?: string;
  __v?: number;
}

export type statusCategoryStructure = {
  status_type: 'default' | 'custom';
  data: Record<string, statusCategoryType>;
}

export type bulkStatus = {
  id:string,
  order?:number,
  name?:string;

}

export type Project = {
  _id: Types.ObjectId;
  name: string;
  description?: string;
  is_public?: boolean;
  experience: string[];
  property_type: PropertyType;
  organization_id: string;
  city: string;
  country: string;
  project_thumbnail: string;
  unique_project_id?: string;
  roles?: UserRole;
  data_sync?: {
    number_of_units?: number;
    number_of_amenities?: number;
    number_of_floors?: number;
    number_of_towers?: number;
  };
  projectSettings: {
    general: generalSettings,
    pixelstreaming: pixelstreamingSettings,
    ale: aleSettings,
    embed: embedSettings,
    salestool: salestoolSettings,
    theme:theme,
    hologramSettings?: hologramSettings
    amenity?:bulkUpdateQuery;
    gallery?:galleryPayload;
    metadata?:object;
    statusCategory?:statusCategoryStructure;
    customization?: CustomizationSettings;
  };
  units?: object;
};

export type createProjectInput = {
  id: Types.ObjectId;
  name: string;
  is_public?: boolean;
  description?: string;
  experience: string[];
  property_type: PropertyType;
  organization_id: string;
  city: string;
  country: string;
  project_thumbnail: string;
  unique_project_id?: string;
  primary:string,
  secondary:string,
  primary_text:string,
  secondary_text:string,
  font_type:FontType,
  font_url:string,
  lat?:number,
  long?:number,
  theme: Theme,
};
export type editProjectInput={
  id: string;
  name: string;
  experience: ExperienceType;
  property_type: PropertyType;
  city: string;
  country: string;
  project_thumbnail?: string;
  unique_project_id?: string;
}
export type updateProjectInput = {
  _id: string;
  is_enabled: boolean;
  session_duration: number;
  slots: string[];
  pixel_streaming_endpoint?: string;
  max_concurrent_sessions: number,
  initial_scene_type: SceneType,
  initial_scene_id: string,
  branding_logo: string,
  branding_logo_dark: string,
  default_experience: SessionType,
  primary:string,
  secondary:string,
  primary_text:string,
  secondary_text:string,
  font_type:FontType,
  font_url:string,
  lat:number,
  long:number,
  welcome_video: string,
  welcome_thumbnail: string,
  description: string,
  hideStatus:boolean,
  theme: Theme,
  timezone: string,
  shortened_link?: string;
  tags?:string[],
  project_location: string,
  project_type: string,
  amount: string,
  bedrooms: string[],
  thumbnail: string,
  file: string,
  hologram_project_logo: string,
  resource_group: string,
  vm_scaleset_name: string,
  min_instances: number,
  auto_scale: boolean,
  default_language: string,
  supported_languages: string[],
  currency_support:boolean,
  cta_name:string,
  cta_type:ctaType,
  is_cta_enabled:boolean,
  is_unitplan_cta_enabled: boolean,
  unitplan_cta_type: ctaType,
  unitplan_cta_name: string,
  unitplan_cta_link?: string,
  unit_card_customize_type:string
  unitcard_config: unitcardConfigType,
  application_id: string,
  metadata: object,
  is_logo_clickable :boolean,
  logo_click_type: logoType,
  logo_click_link:string,
};

export type ProjectSettingsType = {
  general: generalSettings,
  pixelstreaming: pixelstreamingSettings,
  ale: aleSettings,
  embed: embedSettings,
  salestool: salestoolSettings,
  theme:theme,
  hologramSettings?: hologramSettings,
  metadata?: object,
  status:object,
  customization?: CustomizationSettings,
}

export type projectVMDetails = {
  resourceGroupName?: string,
  vmScaleSetName?: string,
  minInstanceCount?: number,
  autoScale?: boolean
}
export enum SupportedLanguages {
  AFRIKAANS = 'af',
  ALBANIAN = 'sq',
  AMHARIC = 'am',
  ARABIC = 'ar',
  ARMENIAN = 'hy',
  AZERBAIJANI = 'az',
  BASQUE = 'eu',
  BELARUSIAN = 'be',
  BENGALI = 'bn',
  BOSNIAN = 'bs',
  BULGARIAN = 'bg',
  CATALAN = 'ca',
  CEBUANO = 'ceb',
  CHINESE_SIMPLIFIED = 'zh-CN',
  CHINESE_TRADITIONAL = 'zh-TW',
  CORSICAN = 'co',
  CROATIAN = 'hr',
  CZECH = 'cs',
  DANISH = 'da',
  DUTCH = 'nl',
  ENGLISH = 'en',
  ESPERANTO = 'eo',
  ESTONIAN = 'et',
  FINNISH = 'fi',
  FRENCH = 'fr',
  FRISIAN = 'fy',
  GALICIAN = 'gl',
  GEORGIAN = 'ka',
  GERMAN = 'de',
  GREEK = 'el',
  GUJARATI = 'gu',
  HAITIAN_CREOLE = 'ht',
  HAUSA = 'ha',
  HAWAIIAN = 'haw',
  HEBREW = 'iw',
  HINDI = 'hi',
  HMONG = 'hmn',
  HUNGARIAN = 'hu',
  ICELANDIC = 'is',
  IGBO = 'ig',
  INDONESIAN = 'id',
  IRISH = 'ga',
  ITALIAN = 'it',
  JAPANESE = 'ja',
  JAVANESE = 'jw',
  KANNADA = 'kn',
  KAZAKH = 'kk',
  KHMER = 'km',
  KOREAN = 'ko',
  KURDISH = 'ku',
  KYRGYZ = 'ky',
  LAO = 'lo',
  LATIN = 'la',
  LATVIAN = 'lv',
  LITHUANIAN = 'lt',
  LUXEMBOURGISH = 'lb',
  MACEDONIAN = 'mk',
  MALAGASY = 'mg',
  MALAY = 'ms',
  MALAYALAM = 'ml',
  MALTESE = 'mt',
  MAORI = 'mi',
  MARATHI = 'mr',
  MONGOLIAN = 'mn',
  MYANMAR_BURMESE = 'my',
  NEPALI = 'ne',
  NORWEGIAN = 'no',
  NYANJA_CHICHEWA = 'ny',
  PASHTO = 'ps',
  PERSIAN = 'fa',
  POLISH = 'pl',
  PORTUGUESE = 'pt',
  PUNJABI = 'pa',
  ROMANIAN = 'ro',
  RUSSIAN = 'ru',
  SAMOAN = 'sm',
  SCOTS_GAELIC = 'gd',
  SERBIAN = 'sr',
  SESOTHO = 'st',
  SHONA = 'sn',
  SINDHI = 'sd',
  SINHALA = 'si',
  SLOVAK = 'sk',
  SLOVENIAN = 'sl',
  SOMALI = 'so',
  SPANISH = 'es',
  SUNDANESE = 'su',
  SWAHILI = 'sw',
  SWEDISH = 'sv',
  TAGALOG_FILIPINO = 'tl',
  TAJIK = 'tg',
  TAMIL = 'ta',
  TELUGU = 'te',
  THAI = 'th',
  TURKISH = 'tr',
  UKRAINIAN = 'uk',
  URDU = 'ur',
  UZBEK = 'uz',
  VIETNAMESE = 'vi',
  WELSH = 'cy',
  XHOSA = 'xh',
  YIDDISH = 'yi',
  YORUBA = 'yo',
  ZULU = 'zu'
}
export type languages = [
  { name: 'Afrikaans', code: 'af' },
  { name: 'Albanian', code: 'sq' },
  { name: 'Amharic', code: 'am' },
  { name: 'Arabic', code: 'ar' },
  { name: 'Armenian', code: 'hy' },
  { name: 'Azerbaijani', code: 'az' },
  { name: 'Basque', code: 'eu' },
  { name: 'Belarusian', code: 'be' },
  { name: 'Bengali', code: 'bn' },
  { name: 'Bosnian', code: 'bs' },
  { name: 'Bulgarian', code: 'bg' },
  { name: 'Catalan', code: 'ca' },
  { name: 'Cebuano', code: 'ceb' },
  { name: 'Chinese (Simplified)', code: 'zh-CN' },
  { name: 'Chinese (Traditional)', code: 'zh-TW' },
  { name: 'Corsican', code: 'co' },
  { name: 'Croatian', code: 'hr' },
  { name: 'Czech', code: 'cs' },
  { name: 'Danish', code: 'da' },
  { name: 'Dutch', code: 'nl' },
  { name: 'English', code: 'en' },
  { name: 'Esperanto', code: 'eo' },
  { name: 'Estonian', code: 'et' },
  { name: 'Finnish', code: 'fi' },
  { name: 'French', code: 'fr' },
  { name: 'Frisian', code: 'fy' },
  { name: 'Galician', code: 'gl' },
  { name: 'Georgian', code: 'ka' },
  { name: 'German', code: 'de' },
  { name: 'Greek', code: 'el' },
  { name: 'Gujarati', code: 'gu' },
  { name: 'Haitian Creole', code: 'ht' },
  { name: 'Hausa', code: 'ha' },
  { name: 'Hawaiian', code: 'haw' },
  { name: 'Hebrew', code: 'iw' },
  { name: 'Hindi', code: 'hi' },
  { name: 'Hmong', code: 'hmn' },
  { name: 'Hungarian', code: 'hu' },
  { name: 'Icelandic', code: 'is' },
  { name: 'Igbo', code: 'ig' },
  { name: 'Indonesian', code: 'id' },
  { name: 'Irish', code: 'ga' },
  { name: 'Italian', code: 'it' },
  { name: 'Japanese', code: 'ja' },
  { name: 'Javanese', code: 'jw' },
  { name: 'Kannada', code: 'kn' },
  { name: 'Kazakh', code: 'kk' },
  { name: 'Khmer', code: 'km' },
  { name: 'Korean', code: 'ko' },
  { name: 'Kurdish', code: 'ku' },
  { name: 'Kyrgyz', code: 'ky' },
  { name: 'Lao', code: 'lo' },
  { name: 'Latin', code: 'la' },
  { name: 'Latvian', code: 'lv' },
  { name: 'Lithuanian', code: 'lt' },
  { name: 'Luxembourgish', code: 'lb' },
  { name: 'Macedonian', code: 'mk' },
  { name: 'Malagasy', code: 'mg' },
  { name: 'Malay', code: 'ms' },
  { name: 'Malayalam', code: 'ml' },
  { name: 'Maltese', code: 'mt' },
  { name: 'Maori', code: 'mi' },
  { name: 'Marathi', code: 'mr' },
  { name: 'Mongolian', code: 'mn' },
  { name: 'Myanmar (Burmese)', code: 'my' },
  { name: 'Nepali', code: 'ne' },
  { name: 'Norwegian', code: 'no' },
  { name: 'Nyanja (Chichewa)', code: 'ny' },
  { name: 'Pashto', code: 'ps' },
  { name: 'Persian', code: 'fa' },
  { name: 'Polish', code: 'pl' },
  { name: 'Portuguese', code: 'pt' },
  { name: 'Punjabi', code: 'pa' },
  { name: 'Romanian', code: 'ro' },
  { name: 'Russian', code: 'ru' },
  { name: 'Samoan', code: 'sm' },
  { name: 'Scots Gaelic', code: 'gd' },
  { name: 'Serbian', code: 'sr' },
  { name: 'Sesotho', code: 'st' },
  { name: 'Shona', code: 'sn' },
  { name: 'Sindhi', code: 'sd' },
  { name: 'Sinhala', code: 'si' },
  { name: 'Slovak', code: 'sk' },
  { name: 'Slovenian', code: 'sl' },
  { name: 'Somali', code: 'so' },
  { name: 'Spanish', code: 'es' },
  { name: 'Sundanese', code: 'su' },
  { name: 'Swahili', code: 'sw' },
  { name: 'Swedish', code: 'sv' },
  { name: 'Tagalog (Filipino)', code: 'tl' },
  { name: 'Tajik', code: 'tg' },
  { name: 'Tamil', code: 'ta' },
  { name: 'Telugu', code: 'te' },
  { name: 'Thai', code: 'th' },
  { name: 'Turkish', code: 'tr' },
  { name: 'Ukrainian', code: 'uk' },
  { name: 'Urdu', code: 'ur' },
  { name: 'Uzbek', code: 'uz' },
  { name: 'Vietnamese', code: 'vi' },
  { name: 'Welsh', code: 'cy' },
  { name: 'Xhosa', code: 'xh' },
  { name: 'Yiddish', code: 'yi' },
  { name: 'Yoruba', code: 'yo' },
  { name: 'Zulu', code: 'zu' }
];

export type projectMetaData = Record<string, string>

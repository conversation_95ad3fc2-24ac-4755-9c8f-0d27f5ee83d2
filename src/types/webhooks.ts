import { Types } from 'mongoose';
export type CustomUser = {
    _id: Types.ObjectId,
    key: string,
    organization: string
}

export type unitMetaData = Record<string, string>

export type ppgUnitType = {
    metadata:object,
    status:string,
    price?:string
  }

export type ppgRequestBodyType = {
    project_id: string,
    data: Array<ppgUnitType>
  }

export type webhookData = {
  _id: string;
  organization_id: string;
  name: string;
  targetUrl: string;
  rules:{
    allowed_projects: string[];
  };
  listOfEvents: string[];
};

export type webhookResult ={
  status: string,
  value: {
    _id: any; success: boolean, taskId:string, error: string
}
}

export type WebhookEventsData = {
  _id : string;
  webhook_id : string;
  organization_id : string;
  eventType:string;
  targetUrl: string;
  data: any,
  log :Array<{
    response : object,
    message : string,
    time : string
  }>,
  lastExecutionStatus?: string,
  lastExecutionTime?: string,
};

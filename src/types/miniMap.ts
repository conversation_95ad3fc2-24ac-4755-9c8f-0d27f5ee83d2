import { Types } from 'mongoose';

export enum TypeEnum {
    AMENITY = 'amenity',
    AMENITYCATEGORY = 'amenity_category',
}

export type AddHotspotData = {
    hotspot_id: string;
    text: string;
    x: number;
    y: number;
    destination: string;
};

export type MiniMap = {
    _id: Types.ObjectId;
    name: string;
    low_res: string;
    high_res: string;
    hotspots?: {[key:string]:AddHotspotData}
    referenceId: string;
    type: TypeEnum,
}

export type MiniMapData = {
    _id: Types.ObjectId;
    name: string;
    hotspots?:{[key:string]:AddHotspotData}
    referenceId: string;
    type: TypeEnum,
}

export type MiniMapMedia = {
    low_res: string;
    high_res: string;
}

import { Types } from 'mongoose';
export type Translations = {
    _id: Types.ObjectId | string,
    name:string,
    en: string, // English
    ab?: string, // Abkhaz
    ace?: string, // Acehnese
    ach?: string, // Acholi
    af?: string, // Afrikaans
    sq?: string, // Albanian
    alz?: string, // Alur
    am?: string, // Amharic
    ar?: string, // Arabic
    hy?: string, // Armenian
    as?: string, // Assamese
    awa?: string, // Awadhi
    ay?: string, // Aymara
    az?: string, // Azerbaijani
    ban?: string, // Balinese
    bm?: string, // Bambara
    ba?: string, // Bashkir
    eu?: string, // Basque
    btx?: string, // Batak Karo
    bts?: string, // Batak Simalungun
    bbc?: string, // Batak Toba
    be?: string, // Belarusian
    bem?: string, // Bemba
    bn?: string, // Bengali
    bew?: string, // Betawi
    bho?: string, // Bhojpuri
    bik?: string, // Bikol
    bs?: string, // Bosnian
    br?: string, // Breton
    bg?: string, // Bulgarian
    bua?: string, // Buryat
    yue?: string, // Cantonese
    ca?: string, // Catalan
    ceb?: string, // Cebuano
    ny?: string, // Chichewa (Nyanja)
    zh_CN?: string, // Chinese (Simplified)
    zh_TW?: string, // Chinese (Traditional)
    cv?: string, // Chuvash
    co?: string, // Corsican
    crh?: string, // Crimean Tatar
    hr?: string, // Croatian
    cs?: string, // Czech
    da?: string, // Danish
    din?: string, // Dinka
    dv?: string, // Divehi
    doi?: string, // Dogri
    dov?: string, // Dombe
    nl?: string, // Dutch
    dz?: string, // Dzongkha
    eo?: string, // Esperanto
    et?: string, // Estonian
    ee?: string, // Ewe
    fj?: string, // Fijian
    fil?: string, // Filipino (Tagalog)
    fi?: string, // Finnish
    fr?: string, // French
    fr_FR?: string, // French (French)
    fr_CA?: string, // French (Canadian)
    fy?: string, // Frisian
    ff?: string, // Fulfulde
    gaa?: string, // Ga
    gl?: string, // Galician
    lg?: string, // Ganda (Luganda)
    ka?: string, // Georgian
    de?: string, // German
    el?: string, // Greek
    gn?: string, // Guarani
    gu?: string, // Gujarati
    ht?: string, // Haitian Creole
    cnh?: string, // Hakha Chin
    ha?: string, // Hausa
    haw?: string, // Hawaiian
    he?: string, // Hebrew
    hil?: string, // Hiligaynon
    hi?: string, // Hindi
    hmn?: string, // Hmong
    hu?: string, // Hungarian
    hrx?: string, // Hunsrik
    is?: string, // Icelandic
    ig?: string, // Igbo
    ilo?: string, // Iloko
    id?: string, // Indonesian
    ga?: string, // Irish
    it?: string, // Italian
    ja?: string, // Japanese
    jw?: string, // Javanese
    kn?: string, // Kannada
    pam?: string, // Kapampangan
    kk?: string, // Kazakh
    km?: string, // Khmer
    cgg?: string, // Kiga
    rw?: string, // Kinyarwanda
    ktu?: string, // Kituba
    gom?: string, // Konkani
    ko?: string, // Korean
    kri?: string, // Krio
    ku?: string, // Kurdish (Kurmanji)
    ckb?: string, // Kurdish (Sorani)
    ky?: string, // Kyrgyz
    lo?: string, // Lao
    ltg?: string, // Latgalian
    la?: string, // Latin
    lv?: string, // Latvian
    lij?: string, // Ligurian
    li?: string, // Limburgan
    ln?: string, // Lingala
    lt?: string, // Lithuanian
    lmo?: string, // Lombard
    luo?: string, // Luo
    lb?: string, // Luxembourgish
    mk?: string, // Macedonian
    mai?: string, // Maithili
    mak?: string, // Makassar
    mg?: string, // Malagasy
    ms?: string, // Malay
    ms_Arab?: string, // Malay (Jawi)
    ml?: string, // Malayalam
    mt?: string, // Maltese
    mi?: string, // Maori
    mr?: string, // Marathi
    chm?: string, // Meadow Mari
    mni_Mtei?: string, // Meiteilon (Manipuri)
    min?: string, // Minang
    lus?: string, // Mizo
    mn?: string, // Mongolian
    my?: string, // Myanmar (Burmese)
    nr?: string, // Ndebele (South)
    new?: string, // Nepalbhasa (Newari)
    ne?: string, // Nepali
    nso?: string, // Northern Sotho (Sepedi)
    no?: string, // Norwegian
    nus?: string, // Nuer
    oc?: string, // Occitan
    or?: string, // Odia (Oriya)
    om?: string, // Oromo
    pag?: string, // Pangasinan
    pap?: string, // Papiamento
    ps?: string, // Pashto
    fa?: string, // Persian
    pl?: string, // Polish
    pt?: string, // Portuguese
    pt_PT?: string, // Portuguese (Portugal)
    pt_BR?: string, // Portuguese (Brazil)
    pa?: string, // Punjabi
    pa_Arab?: string, // Punjabi (Shahmukhi)
    qu?: string, // Quechua
    rom?: string, // Romani
    ro?: string, // Romanian
    rn?: string, // Rundi
    ru?: string, // Russian
    sm?: string, // Samoan
    sg?: string, // Sango
    sa?: string, // Sanskrit
    gd?: string, // Scots Gaelic
    sr?: string, // Serbian
    st?: string, // Sesotho
    crs?: string, // Seychellois Creole
    shn?: string, // Shan
    sn?: string, // Shona
    scn?: string, // Sicilian
    szl?: string, // Silesian
    sd?: string, // Sindhi
    si?: string, // Sinhala (Sinhalese)
    sk?: string, // Slovak
    sl?: string, // Slovenian
    so?: string, // Somali
    es?: string, // Spanish
    su?: string, // Sundanese
    sw?: string, // Swahili
    ss?: string, // Swati
    sv?: string, // Swedish
    tg?: string, // Tajik
    ta?: string, // Tamil
    tt?: string, // Tatar
    te?: string, // Telugu
    tet?: string, // Tetum
    th?: string, // Thai
    ti?: string, // Tigrinya
    ts?: string, // Tsonga
    tn?: string, // Tswana
    tr?: string, // Turkish
    tk?: string, // Turkmen
    ak?: string, // Twi (Akan)
    uk?: string, // Ukrainian
    ur?: string, // Urdu
    ug?: string, // Uyghur
    uz?: string, // Uzbek
    vi?: string, // Vietnamese
    cy?: string, // Welsh
    xh?: string, // Xhosa
    yi?: string, // Yiddish
    yo?: string, // Yoruba
    yua?: string, // Yucatec Maya
    zu?: string // Zulu
}
export type TransaltionResult = string | Array<string> | null;

export enum SupportedLanguages {
    AB = 'ab',
    ACE = 'ace',
    ACH = 'ach',
    AF = 'af',
    SQ = 'sq',
    ALZ = 'alz',
    AM = 'am',
    AR = 'ar',
    HY = 'hy',
    AS = 'as',
    AWA = 'awa',
    AY = 'ay',
    AZ = 'az',
    BAN = 'ban',
    BM = 'bm',
    BA = 'ba',
    EU = 'eu',
    BTX = 'btx',
    BTS = 'bts',
    BBC = 'bbc',
    BE = 'be',
    BEM = 'bem',
    BN = 'bn',
    BEW = 'bew',
    BHO = 'bho',
    BIK = 'bik',
    BS = 'bs',
    BR = 'br',
    BG = 'bg',
    BUA = 'bua',
    YUE = 'yue',
    CA = 'ca',
    CEB = 'ceb',
    NY = 'ny',
    ZH_CN = 'zh-CN',
    ZH_TW = 'zh-TW',
    CV = 'cv',
    CO = 'co',
    CRH = 'crh',
    HR = 'hr',
    CS = 'cs',
    DA = 'da',
    DIN = 'din',
    DV = 'dv',
    DOI = 'doi',
    DOV = 'dov',
    NL = 'nl',
    DZ = 'dz',
    EN = 'en',
    EO = 'eo',
    ET = 'et',
    EE = 'ee',
    FJ = 'fj',
    FIL = 'fil',
    FI = 'fi',
    FR = 'fr',
    FR_FR = 'fr-FR',
    FR_CA = 'fr-CA',
    FY = 'fy',
    FF = 'ff',
    GAA = 'gaa',
    GL = 'gl',
    LG = 'lg',
    KA = 'ka',
    DE = 'de',
    EL = 'el',
    GN = 'gn',
    GU = 'gu',
    HT = 'ht',
    CNH = 'cnh',
    HA = 'ha',
    HAW = 'haw',
    HE = 'he',
    HIL = 'hil',
    HI = 'hi',
    HMN = 'hmn',
    HU = 'hu',
    HRX = 'hrx',
    IS = 'is',
    IG = 'ig',
    ILO = 'ilo',
    ID = 'id',
    GA = 'ga',
    IT = 'it',
    JA = 'ja',
    JW = 'jw',
    KN = 'kn',
    PAM = 'pam',
    KK = 'kk',
    KM = 'km',
    CGG = 'cgg',
    RW = 'rw',
    KTU = 'ktu',
    GOM = 'gom',
    KO = 'ko',
    KRI = 'kri',
    KU = 'ku',
    CKB = 'ckb',
    KY = 'ky',
    LO = 'lo',
    LTG = 'ltg',
    LA = 'la',
    LV = 'lv',
    LIJ = 'lij',
    LI = 'li',
    LN = 'ln',
    LT = 'lt',
    LMO = 'lmo',
    LUO = 'luo',
    LB = 'lb',
    MK = 'mk',
    MAI = 'mai',
    MAK = 'mak',
    MG = 'mg',
    MS = 'ms',
    MS_ARAB = 'ms-Arab',
    ML = 'ml',
    MT = 'mt',
    MI = 'mi',
    MR = 'mr',
    CHM = 'chm',
    MNI_MTEI = 'mni-Mtei',
    MIN = 'min',
    LUS = 'lus',
    MN = 'mn',
    MY = 'my',
    NR = 'nr',
    NEW = 'new',
    NE = 'ne',
    NSO = 'nso',
    NO = 'no',
    NUS = 'nus',
    OC = 'oc',
    OR = 'or',
    OM = 'om',
    PAG = 'pag',
    PAP = 'pap',
    PS = 'ps',
    FA = 'fa',
    PL = 'pl',
    PT = 'pt',
    PT_PT = 'pt-PT',
    PT_BR = 'pt-BR',
    PA = 'pa',
    PA_ARAB = 'pa-Arab',
    QU = 'qu',
    ROM = 'rom',
    RO = 'ro',
    RN = 'rn',
    RU = 'ru',
    SM = 'sm',
    SG = 'sg',
    SA = 'sa',
    GD = 'gd',
    SR = 'sr',
    ST = 'st',
    CRS = 'crs',
    SHN = 'shn',
    SN = 'sn',
    SCN = 'scn',
    SZL = 'szl',
    SD = 'sd',
    SI = 'si',
    SK = 'sk',
    SL = 'sl',
    SO = 'so',
    ES = 'es',
    SU = 'su',
    SW = 'sw',
    SS = 'ss',
    SV = 'sv',
    TG = 'tg',
    TA = 'ta',
    TT = 'tt',
    TE = 'te',
    TET = 'tet',
    TH = 'th',
    TI = 'ti',
    TS = 'ts',
    TN = 'tn',
    TR = 'tr',
    TK = 'tk',
    AK = 'ak',
    UK = 'uk',
    UR = 'ur',
    UG = 'ug',
    UZ = 'uz',
    VI = 'vi',
    CY = 'cy',
    XH = 'xh',
    YI = 'yi',
    YO = 'yo',
    YUA = 'yua',
    ZU = 'zu'
}

export type TranslationDocument = {
    [key in SupportedLanguages]?: string;
}

export interface TranslationModuleResponse {
    translations: {
      [key: string]: Translations;
    };
}

export interface RowData {
    [key: string]: string | number;
}

export interface ExtendedTranslationDocument extends TranslationDocument {
    [key: string]: string | number | undefined;
    _id?: string;
    __v?: number;
}

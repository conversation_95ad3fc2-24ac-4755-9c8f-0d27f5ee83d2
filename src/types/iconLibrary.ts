import { Types } from 'mongoose';
export enum  iconLibrary_types{
  SVG = 'svg',

}
export interface iconLibrary{
    _id: Types.ObjectId;
    name:string,
    type:iconLibrary_types,
    category:string,
    iconURL: string,
    iconHeight: number,
    iconWidth: number,
    organization_id?: string; // Optional for global icons, required for org-level icons
}

export interface updateIconLibrary{
    icon_id?: Types.ObjectId;
    name?:string,
    type?:iconLibrary_types,
    category?:string,
}

export type allIcons = {
  [key: string]: updateIconLibrary;
};

export type QueryParams = {
  searchText?: string;
}

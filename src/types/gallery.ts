import { Types } from 'mongoose';
import { MediaType } from './amenity';
export type galleryItem = {
    _id: Types.ObjectId,
    name: string,
    category: string,
    type: MediaType,
    url?:string,
    thumbnail?: string,
    link?: string
    tour_id?: string
    modified: string
}
export type createGalleryItem = {
    id: Types.ObjectId,
    name: string,
    category: string,
    type: MediaType,
    url?:string,
    thumbnail?: string,
    link?: string,
    tour_id?: string
}
export type updateGalleryItemType = {
    category?: string,
    name?: string,
    type?: MediaType,
    url?:string | null,
    thumbnail?: string | null,
    link?: string | null
    tour_id?: string | null
    modified: string
}
export type bulkUpdateQuery = {
    id:string,
    order?:number,
    category?: string
}
export type bulkUpdateType = {
    query:bulkUpdateQuery[],
    project_id:string
}

import { Types } from 'mongoose';
import { Direction } from './direction';

// Export enum unitStatus {
//   AVAILABLE = 'available',
//   ONHOLD = 'onhold',
//   RESERVED = 'reserved',
//   SOLD = 'sold'
// }
export enum priceCurrency {
  AED = 'aed',
  ARS = 'ars',
  AUD = 'aud',
  BGN = 'bgn',
  BRL = 'brl',
  BSD = 'bsd',
  CAD = 'cad',
  CHF = 'chf',
  CLP = 'clp',
  CNY = 'cny',
  COP = 'cop',
  CZK = 'czk',
  DKK = 'dkk',
  DOP = 'dop',
  EGP = 'egp',
  EUR = 'eur',
  FJD = 'fjd',
  GBP = 'gbp',
  GTQ = 'gtq',
  HKD = 'hkd',
  HRK = 'hrk',
  HUF = 'huf',
  IDR = 'idr',
  ILS = 'ils',
  INR = 'inr',
  ISK = 'isk',
  JPY = 'jpy',
  KRW = 'krw',
  KZT = 'kzt',
  MXN = 'mxn',
  MYR = 'myr',
  NOK = 'nok',
  NZD = 'nzd',
  PAB = 'pab',
  PEN = 'pen',
  PHP = 'php',
  PKR = 'pkr',
  PLN = 'pln',
  PYG = 'pyg',
  RON = 'ron',
  RUB = 'rub',
  SAR = 'sar',
  SEK = 'sek',
  SGD = 'sgd',
  THB = 'thb',
  TRY = 'try',
  TWD = 'twd',
  UAH = 'uah',
  USD = 'usd',
  UYU = 'uyu',
  VND = 'vnd',
  ZAR = 'zar',
  LBP = 'lbp',
  QAR = 'qar',
  JOD = 'jod',
  NULL = ''
}

export enum measurementType {
  SQFT = 'sqft',
  SQMT = 'sqmt'
}
export type Units = {
  isNew?: any;
  _id: Types.ObjectId;
  unitplan_id: string;
  project_id:string;
  name: string;
  status: string;
  metadata: string;
  floor_id:string;
  building_id:string;
  price: string;
  max_price?:string,
  community_id:string;
  currency: priceCurrency;
  tour_id: string;
  measurement?:number;
  measurement_type?: measurementType;
  balcony_measurement?:number;
  cta_link?:string;
  balcony_measurement_type?: measurementType;
  suite_area?:number;
  suite_area_type?: measurementType;
  direction?: Direction | null;
  created_at?:string,
};

export type unitFilterQuery = {
  project_id?: string,
  min_price? : string,
  max_price? : string,
  unit_id?: string,
  floor?: string,
  building_id?: string,
  is_available?: boolean,
  unitplan_id?: string,
  status?: string,
  sortby?:string,
  orderby?:string,
  bedrooms?:string,
  min_area?:string,
  max_area?:string,
  unitplan_type?: string
  min_floor?: string,
  max_floor?:string
  style_types?:string
}

export type filterQuery = {
  community_id?: string,
  building_id?: string,
  floor_id?:string;
}

export type unitListResponse = {
  'totalCount': number,
  'searchCount': number,
  'data': object,
}

export type assets = {
    _id: string,
    name: string,
    media_type: string,
    type:string,
    url: string,
    created_at: string,
    updated_at: string,
}

export type updateAssets = {
    asset_id: string,
    file_name?:string,
    file_type?:string,
    file_url?: string,
    media_type?: string,
}

export type moveAssets = {
  sourceUrl: string,
  destinationPath:string,
}

export enum AssetType {
  MASTERPLAN = 'master_plan',
  ARCHITECTURAL_DRAWING = 'architectural_drawing',
  INTERIOR_RENDER = 'interior_render',
  EXTERIOR_RENDER = 'exterior_render',
  FLOOR_PLAN = 'floor_plan'
}

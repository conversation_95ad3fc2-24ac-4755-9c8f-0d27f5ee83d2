import mongoose from 'mongoose';
import { priceCurrency, measurementType } from './units';

export type tableType = {
    drupalID: string,
    organization: string,
    projectId: string
}

export type DamacUnits = {
    unitNumber: string;
    area: number;
    towerName:string;
    floorName: number;
    status: string;
    unitType: string;
    bedroomType:string;
    floorPlanImage?:string;
    floorPlan: string;
    price:number;
    currency: priceCurrency;
    unitImage: string;
    facing?:string;
    mirrored?: number;
    areaUnit?: measurementType;
};

export type UpdateUnit = {
    _id: mongoose.Types.ObjectId | string;
    name:string,
    status?:string,
    price?:string,
    measurement?:number
};

export type resultObject = {
    updatedUnits : Array<UpdateUnit>,
    unitsnotpresent: Record<string, DamacUnits>
}

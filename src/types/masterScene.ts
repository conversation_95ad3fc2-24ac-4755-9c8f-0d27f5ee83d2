import { Types } from 'mongoose';
import { transformedSVG } from './masterSVG';
import { projectScene } from './projectScene';
export enum masterSceneType {
    EARTH = 'earth',
    IMAGE = 'image'
}

export enum coordinateLinkType {
    PROJECT = 'project',
    MASTER = 'master',
    EXTERNAL = 'external'
}

export type coordinate = {
    _id: Types.ObjectId,
    lat: number,
    lng: number,
    name: string,
    linkType: string,
    project_id?: string,
    scene_id?:string,
    link?: string,
    active: boolean,
    country?: string,
    video?: string,

}
export type masterScene={
    _id:string,
    organization_id:string,
    type:masterSceneType,
    name:string,
    background:object,
    video:string,
    active:boolean,
    info_icon:string,
    parent:string,
    info_text:string,
    coordinates: coordinate[],
    coordinate_settings: object,
    root:boolean,
    clouds:boolean,
    earth_position:object,
    preview?: string,
}
export type transformedMasterScene = {
    [key: string]: {
      sceneData: projectScene;
      svgData: transformedSVG;
    };
  };

export type coordinateObj = {
    lat: number,
    lng: number,
    name: string,
    link?: string,
    linkType?: string
}

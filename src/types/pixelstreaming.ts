export type ScaleSetVerison = {
  version: number;
  instancesPerNode: number;
  resolutionWidth: number;
  resolutionHeight: number;
  pixelstreamingApplicationName: string;
  fps: number;
  unrealApplicationDownloadUri: string;
  msImprovedWebserversDownloadUri: string;
  msPrereqsDownloadUri: string;
  enableAutoScale: boolean;
  instanceCountBuffer: number;
  percentBuffer: number;
  minMinutesBetweenScaledowns: number;
  scaleDownByAmount: number;
  minInstanceCount: number;
  maxInstanceCount: number;
  stunServerAddress: string;
  turnServerAddress: string;
  turnUsername: string;
  turnPassword: string;
};
export type ScaleSet = {
  _id: string,
  versions: ScaleSetVerison;
  organization_id:string,
  project_id:string
};

export type accesstokenType = {
  token_type: string,
  expires_in: number,
  ext_expires_in: number,
  access_token: string
}

import { Types } from 'mongoose';
import { FormattedRate } from './organization';

export interface currencyLibrary{
    _id: Types.ObjectId;
    baseCurrency: string,
    exchangeRatio: FormattedRate[],
    timestamp: Date,
    source: string,
}

export interface currencyUpdate{
    exchangeRatio: FormattedRate[],
    timestamp: Date,
}

export interface BulkOperationResult {
  successful: currencyLibrary[];
  failed: Array<{
    currency: string;
    error: string;
  }>;
  totalProcessed: number;
  successCount: number;
  failureCount: number;
}

export enum currencyProvidersEnum {
    FASTFOREX = 'fastforex'
}

export const topCurrencies = ['usd', 'aed', 'inr', 'eur', 'gbp', 'cad', 'aud', 'sar', 'sek', 'ugx'];

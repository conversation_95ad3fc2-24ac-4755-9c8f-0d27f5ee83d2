import { coordinate } from './masterScene';
import { transformedSVG } from './projectSVG';
import { Direction } from './direction';
export enum projectSceneType {
  IMAGE = 'image',
  IDENTICAL_UNITPLAN = 'identical_unitplan',
  GSPLAT = 'gsplat',
  CAESIUM = 'caesium',
  NEIGHBOURHOOD = 'neighbourhood',
  ROTATABLE_IMAGE = 'rotatable_image',
  ROTATABLE_IMAGE_FRAME = 'rotatable_image_frame',
  DEEP_ZOOM = 'deep_zoom',
  EARTH = 'earth'
}
export enum  deep_zoom_status{
  NOT_STARTED = 'not_started',
  PROCESSING = 'processing',
  PROCESS_SUCCESS = 'process_success',
  PROCESS_FAILED = 'process_failed',
}
export enum convertSceneTypeAction{
  COPY = 'copy',
  REPLACE = 'replace',
}
export type projectScene = {
  _id: string;
  organization_id: string;
  type: projectSceneType;
  name: string;
  background: object;
  active: boolean;
  info_icon: string;
  parent: string;
  info_text?: string;
  root: boolean;
  building_id: string;
  floor_ids: string[];
  clouds: boolean;
  video: string;
  gsplat_link: string;
  order: number;
  deep_zoom_status:deep_zoom_status;
  deep_zoom_failed_info:string;
  coordinates?: coordinate[];
  coordinate_settings?: object;
  earth_position?:object;
  minZoomLevel?:number;
  maxZoomLevel?:number;
  direction?: Direction | null;
  north_direction?: {
    position: {
      x: number;
      y: number;
      z: number;
    };
    rotation: {
      x: number;
      y: number;
      z: number;
      w: number;
    };
  } | null;
  preview?: string;
};
export type transformedProjectScene = {
  [key: string]: {
    sceneData: projectScene;
    svgData: transformedSVG;
  };
};

export type updateProjectSceneObj = {
  type?: string;
  name?: string;
  lowRes?: string;
  highRes?: string;
  lowResNight?: string;
  highResNight?: string;
  file_url?:string;
  file_url_night?:string;
  active?: boolean;
  info_icon?: string;
  parent?: number;
  info_text?: string;
  root?: boolean;
  building_id?: string;
  clouds?: boolean;
  video?: string;
  gsplat_link?: string;
  category?: string;
  frame_id?: string;
  order?: number;
  id?: string;
  position?: object;
  polar_angle?: object;
  distance?: object;
  auto_rotate?: boolean;
  deep_zoom_status?:deep_zoom_status
  deep_zoom_failed_info?:string;
  high_resolution_copy?:string;
  high_resolution_night_copy?:string;
  minZoomLevel?:number;
  maxZoomLevel?:number;
  earth_position?:{
    x_axis:number,
    y_axis:number,
    z_axis:number
  },
  width?:number,
  height?:number,
  preview?: string,
  direction?: Direction | null;
  north_direction?: {
    position: {
      x: number;
      y: number;
      z: number;
    };
    rotation: {
      x: number;
      y: number;
      z: number;
      w: number;
    };
  } | null;
};

export type updateProjectSceneFrame = {
  order: number;
  id: string;
};

export type updateBulkSceneType = {
  query: updateProjectSceneFrame[];
  project_id: string;
};

export type background = {
  low_resolution: string;
  high_resolution: string;
  low_resolution_night?: string;
  high_resolution_night?: string;
};
export type convertSceneTypePayload = {
  scene_id: string
  toType: string,
  fromType: string,
  project_id:string,
  high_resolution:string,
  action:string
}

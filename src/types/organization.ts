import { FontType, theme } from './projects';
import { Theme } from './session';

export enum UserRole {
  ADMIN = 'admin',
  READER = 'reader',
  EDITOR = 'editor',
  SUPPORTER = 'supporter',
}
export type User = {
  profilePicture ?: string;
  user_id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  organization_id?: string[];
};
export type Role = {
  user_id: string;
  created_time: string;
  role: UserRole;
  email: string | undefined;
};
export type FormattedRate = {
  currency: string;
  rate: number | null;
};
export type share_scenes_type = {
  whatsapp: boolean,
  email: boolean,
  twitter: boolean,
  // Instagram: boolean,
  // Facebook: boolean,
}

export type pixelstreamingSettings = {
  max_concurrent_sessions: number,
  duration: number,
  lark_groupid?:string,
};
export type salestoolSettings = {
  slots:string[],
  timezone: string,
}
export type currencySettings = {
  baseCurrency: string,
  currency_provider: string,
  currency_provider_name:string,
  exchangeRatio?:FormattedRate[]
}

export type webliteSettings = {
  mastersvg_visibility: boolean,
  share_masterscenes?: share_scenes_type,
  is_broadcast_enabled: boolean,
  org_logo_click_type:string,
  org_logo_click_link?:string,
}
export type organizationSettingsInput = {
    pixelstreaming: pixelstreamingSettings,
    theme: theme,
    salestool: salestoolSettings,
    currency: currencySettings,
    weblite:webliteSettings
  }
export type Organization = {
  _id: string;
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  roles: Role[];
  thumbnail: string;
  unique_org_id: string;
  measurement_id?: string,
  organizationSettings:organizationSettingsInput
};
export type OrgThumbnail = {
  _id: string;
  thumbnail: string;
};
export type CreateOrganizationInput = {
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  thumbnail: string;
  organizationId: string;
  unique_org_id: string;
  measurement_id?: string,
  organizationSettings:organizationSettingsInput
};

export type migratedOrganizationRecords = {
  _doc: migratedOrganizationRecords;
    _id: string;
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  roles: Role[];
  thumbnail: string;
  unique_org_id: string,
  measurement_id?: string,
  max_concurrent_sessions: number,
  duration: number,
  lark_groupid?:string,
  slots:string[],
  timezone: string,
  baseCurrency: string,
  currency_provider: string,
  currency_provider_name:string,
  exchangeRatio?:FormattedRate[]
  mastersvg_visibility: boolean,
  is_broadcast_enabled: boolean,
  share_masterscenes: boolean,
  theme: Theme,
  primary:string,
  primary_text:string,
  secondary:string,
  secondary_text:string,
  font_type:FontType,
  font_url:string,
};

export type updateOrganizationDetails = {
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  thumbnail: string;
  organizationId: string;
  unique_org_id: string,
}
export type UpdateOrganizationInput ={
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  thumbnail: string;
  organizationId: string;
  unique_org_id: string,
  measurement_id?: string,
  organizationSettings:organizationSettingsInput
  // Is_org_logo_clickable?:boolean,
  // Org_logo_click_type?:string,
  // Org_logo_click_link?:string,
};

// Define other function inputs and outputs as needed

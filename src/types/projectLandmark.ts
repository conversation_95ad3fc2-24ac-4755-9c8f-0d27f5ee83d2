import { AddressGeometry, DistanceMatrixResponseData,
  PlaceEditorialSummary, PlacePhoto } from '@googlemaps/google-maps-services-js';
export enum landmarkCategory {
    HEALTH = 'health',
    EDUCATION = 'education',
    ENTERTAINMENT = 'entertainment',
    TRANSIT = 'transit',
    COMMERCIAL = 'commercial',
    RELIGIOUS = 'religious',
    GOVERNMENTAL = 'governmental',
    RECREATIONAL = 'recreational',
    LANDMARK = 'landmark',
    OTHER = 'other',
    OTHERPROJECTS = 'other projects',
    BANKS = 'banks',
    METROSTATIONS = 'metro stations',
    HOTELS = 'hotels',
    WORKSPACES = 'workspaces',
    RESTAURANTS = 'restaurants',
    SHOPPING = 'shopping',
    MALLS = 'malls',
    PARKS = 'parks'
}
export enum routeCategory {
  DRIVING = 'driving',
  TRANSIT = 'transit',
  WALKING = 'walking'
}
export type projectLandmark = {
    _id : string,
    name : string,
    thumbnail : string,
    icon : string,
    distance : number,
    category : landmarkCategory,
    walk_timing?: number,
    transit_timing?: number,
    car_timing : number,
    lat:number,
    long:number,
    description:string
}
export type updateProjectLandmark = {
    landmark_id:string,
    name? : string,
    distance? : number,
    category? : landmarkCategory,
    walk_timing? : number,
    transit_timing? : number,
    car_timing? : number,
    lat?:number,
    long?:number,
    description?:string,
    walking?: string,
    driving?: string,
    transit?: string
}
export type updateProjectLandmarkFiles = {
    landmark_id:string
}
export type getAllRoutesInput = {
    projLat:number,
    projLong:number
}
export type getRoutesInput = {
  projLat:number,
  projLong:number,
  landmarkId: string
}
export type Photos = {
    height: string,
    width: string,
    photo_reference: string,
    html_attributions: string
  }
export type Geometry = {
    location: {
      lat: number,
      lng: number
    },
    viewport: {
      northeast: {
        lat: string,
        lng: string
      },
       southwest: {
        lat: string,
        lng: string
       }
    }
  }

export type placeType = {
    name: string;
    place_id: string,
    photos?: Array<PlacePhoto>,
    geometry?: AddressGeometry,
    imageURL?: string,
    description?: PlaceEditorialSummary,
    distanceMatrix?: {
        driving?: DistanceMatrixResponseData['rows'][0]['elements'][0];
        walking?: DistanceMatrixResponseData['rows'][0]['elements'][0];
        transit?: DistanceMatrixResponseData['rows'][0]['elements'][0];
        bicycling?: DistanceMatrixResponseData['rows'][0]['elements'][0];
    };
}

export const gmap_types = [
  'hospital',
  'doctor',
  'dentist',
  'school',
  'secondary_school',
  'university',
  'amusement_park',
  'movie_theater',
  'zoo',
  'transit_station',
  'train_station',
  'taxi_stand',
  'bus_station',
  'convenience_store',
  'clothing_store',
  'supermarket',
  'department_store',
  'mosque',
  'church',
  'hindu_temple',
  'synagogue',
  'local_government_office',
  'police',
  'embassy',
  'park',
  'shopping_mall',
  'cafe',
  'restaurant',
  'tourist_attraction',
  'gym',
  'night_club',
  'hardware_store',
  'library',
];

export const categoryMap: { [key: string]: landmarkCategory } = {
  'hospital': landmarkCategory.HEALTH,
  'doctor': landmarkCategory.HEALTH,
  'dentist': landmarkCategory.HEALTH,
  'school': landmarkCategory.EDUCATION,
  'secondary_school': landmarkCategory.EDUCATION,
  'university': landmarkCategory.EDUCATION,
  'amusement_park': landmarkCategory.ENTERTAINMENT,
  'movie_theater': landmarkCategory.ENTERTAINMENT,
  'zoo': landmarkCategory.ENTERTAINMENT,
  'transit_station': landmarkCategory.TRANSIT,
  'train_station': landmarkCategory.TRANSIT,
  'taxi_stand': landmarkCategory.TRANSIT,
  'bus_station': landmarkCategory.TRANSIT,
  'convenience_store': landmarkCategory.COMMERCIAL,
  'clothing_store': landmarkCategory.COMMERCIAL,
  'supermarket': landmarkCategory.COMMERCIAL,
  'department_store': landmarkCategory.COMMERCIAL,
  'mosque': landmarkCategory.RELIGIOUS,
  'church': landmarkCategory.RELIGIOUS,
  'hindu_temple': landmarkCategory.RELIGIOUS,
  'synagogue': landmarkCategory.RELIGIOUS,
  'local_government_office': landmarkCategory.GOVERNMENTAL,
  'police': landmarkCategory.GOVERNMENTAL,
  'embassy': landmarkCategory.GOVERNMENTAL,
  'park': landmarkCategory.RECREATIONAL,
  'shopping_mall': landmarkCategory.RECREATIONAL,
  'cafe': landmarkCategory.RECREATIONAL,
  'restaurant': landmarkCategory.RECREATIONAL,
  'tourist_attraction': landmarkCategory.LANDMARK,
  'gym': landmarkCategory.OTHER,
  'night_club': landmarkCategory.OTHER,
  'hardware_store': landmarkCategory.OTHER,
  'library': landmarkCategory.OTHER,
};

import { Types } from 'mongoose';
import { UserDetails } from './user';

export type createMeetingInput = {
  user_id: string,
  is_scheduled: boolean,
  organization_id: string,
  schedule_time: string|null,
  config:object
}

export type Meeting = {
  _id: Types.ObjectId;
  organization_id: string; //
  user_id: string; //
  config:object,
  is_scheduled: boolean,
  schedule_time: string|null,
  start: string;
};

export interface UserDetailsWithFCM extends UserDetails {
  fcmToken?: string | string[];
}

export type sessionAnalyticsQuery = {
  organization_id? : string,
  user_id?: string,
}

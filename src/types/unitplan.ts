
export enum unitplan_type {
  STUDIO = 'studio',
  ONEBHK = '1BHK',
  TWOBHK = '2BHK',
  THREEBHK = '3BHK',
  FOURBHK = '4BHK',
  FIVEBHK = '5BHK',
  SIXBHK = '6BHK',
  SEVENBHK = '7BHK',
  EIGHTBHK = '8BHK',
  NINEBHK = '9BHK',
  TENBHK = '10BHK',
  ZEROBHK = '0BHK',
  PENTHOUSE = 'penthouse',
  TOWNHOUSE = 'townhouse',
  PODIUM = 'podium',
  SUITE = 'suite',
  PLOT = 'plot',
  DUPLEX = 'duplex',
  OFFICE = 'office',
  SHOP = 'shop',
  ONEPOINTFIVEBHK = '1.5BHK',
  TWOPOINTFIVEBHK ='2.5BHK',
  THREEPOINTFIVEBHK = '3.5BHK',
  FOURPOINTFIVEBHK = '4.5BHK',
  FIVEPOINTFIVEBHK = '5.5BHK',
  SIXPOINTFIVEBHK = '6.5BHK',
  SEVENPOINTFIVEBHK = '7.5BHK',
  EIGHTPOINTFIVEBHK = '8.5BHK',
  NINEPOINTFIVEBHK = '9.5BHK',
  TENPOINTFIVEBHK = '10.5BHK'
}
export enum measurementType {
  SQFT = 'sqft',
  SQMT = 'sqmt'
}

export type hotspots = {
  text: string;
  x: number;
  y: number;
  scale:string;
  type:string;
  image_id?:string;
  label_id?:string;
  group_id?:string;
  subGroup_id?:string;
}

export enum hotspotType {
  DEFAULT = 'default',
  IMAGE = 'image',
  LABEL = 'label',
  GROUP = 'group',
  SUBGROUP = 'subgroup',
}

export enum scaleType {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
}

export enum unitType {
  VILLA = 'villa',
  FLAT = 'flat',
  VILLA_FLOOR = 'villa_floor',
  PLOT = 'plot',
  OFFICE = 'office',
  SHOP = 'shop'
}
export type unitplanType = {
  _id: string;
  project_id: string;
  type?: string;
  name: string;
  thumbnail?: string;
  image_url?: string;
  measurement?:number;
  measurement_type?: measurementType;
  tour_id: string;
  bedrooms?: unitplan_type;
  is_residential?:boolean;
  is_commercial:boolean,
  bathrooms?:number;
  maid?:boolean;
  view?:string;
  is_furnished: boolean;
  balcony_measurement?: number;
  balcony_measurment_type?:measurementType;
  suite_area?: number;
  suite_area_type?: measurementType;
}
export enum exterior_type {
  SCENE = 'scene',
  GALLERY = 'gallery'
}
export type UnitplanInterface = {
  // Define the properties of the unit plan that can be edited
  type?: string;
  name?: string;
  measurement?: number;
  measurement_type?: string;
  tour_id?: string;
  bedrooms?: number;
  is_residential?: boolean;
  bathrooms?: number | null;
  is_furnished?: boolean;
  unit_type?:string,
  exterior_type?:string,
  scene_id?:string,
  gallery_id?:string,
  floor_unitplans?:string,
  building_id?: string,
  style?: string,
  balcony_measurement?: number | null,
  balcony_measurement_type?: string;
  suite_area?: number,
  suite_area_type?: measurementType;
  is_commercial?: boolean;
  thumbnail?: string,
  image_url?: string
}

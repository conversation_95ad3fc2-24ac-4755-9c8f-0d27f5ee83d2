import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import uploadHandler from '../helpers/uploadHandler';
import { CreateMiniMap } from '../controllers/miniMap/CreateMiniMap';
import { AddHotspot } from '../controllers/miniMap/AddHotspot';
import { GetLitsOfMiniMap } from '../controllers/miniMap/GetListOfMiniMap';
import { GetHotspots } from '../controllers/miniMap/GetHotspots';
import { GetMiniMap } from '../controllers/miniMap/GetMiniMap';
import { DeleteMiniMap } from '../controllers/miniMap/DeleteMiniMap';
import { DeleteHotSpot } from '../controllers/miniMap/DeleteHotspot';
import { UpdateMiniMap } from '../controllers/miniMap/UpdateMiniMap';
import { UpdateMiniMapMedia } from '../controllers/miniMap/UpdateMiniMapMedia';
import CreateMiniMapValidate from '../controllers/miniMap/CreateMiniMap/createMiniMapValidator';
import CreateHotSpotValidate from '../controllers/miniMap/AddHotspot/addHotspotValidator';
import GetHotspotsValidate from '../controllers/miniMap/GetHotspots/getHotspotsValidator';
import GetMiniMapValidate from '../controllers/miniMap/GetMiniMap/getMiniMapValidator';
import GetListOfMiniMapValidate from '../controllers/miniMap/GetListOfMiniMap/getListOfMiniMapValidator';
import DeleteMiniMapValidate from '../controllers/miniMap/DeleteMiniMap/deleteMiniMapValidator';
import DeleteHotSpotValidate from '../controllers/miniMap/DeleteHotspot/deleteHotspotValidator';
import UpdateMiniMapValidate from '../controllers/miniMap/UpdateMiniMap/updateMiniMapValidator';
import UpdateMiniMapMediaValidator from '../controllers/miniMap/UpdateMiniMapMedia/updateMiniMapMediaValidator';

const router = express.Router();
router.post('/AddHotspot',
  authMiddleware,
  organizationAccessMiddleware,
  CreateHotSpotValidate,
  AddHotspot,
);
router.post('/CreateMiniMap',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'low_res'}, {name: 'high_res' }], 'output'),
  CreateMiniMapValidate,
  CreateMiniMap,
);
router.get('/GetListOfMiniMap',
  authMiddleware,
  organizationAccessMiddleware,
  GetListOfMiniMapValidate,
  GetLitsOfMiniMap,
);
router.get('/GetHotspots',
  authMiddleware,
  organizationAccessMiddleware,
  GetHotspotsValidate,
  GetHotspots,
);
router.get('/GetMiniMap',
  authMiddleware,
  organizationAccessMiddleware,
  GetMiniMapValidate,
  GetMiniMap,
);
router.post('/DeleteMiniMap',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteMiniMapValidate,
  DeleteMiniMap,
);
router.post('/DeleteHotSpot',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteHotSpotValidate,
  DeleteHotSpot,
);
router.post('/UpdateMiniMap',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateMiniMapValidate,
  UpdateMiniMap,
);
router.post('/UpdateMiniMapMedia',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'low_res'}, {name: 'high_res' }], 'output'),
  UpdateMiniMapMediaValidator,
  UpdateMiniMapMedia,
);

export default router;

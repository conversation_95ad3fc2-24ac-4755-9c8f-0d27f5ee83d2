import express from 'express';
import { CreateVirtualTour } from '../controllers/virtualTour/CreateVirtualTour';
import { GetTourById } from '../controllers/virtualTour/GetTourById';
import { DeleteTour } from '../controllers/virtualTour/DeleteTour';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import CreateVirtualTourValidate from '../controllers/virtualTour/CreateVirtualTour/createVirtualTourValidator';
import getTourValidate from '../controllers/virtualTour/GetTourById/getTourValidate';
import deleteTourValidate from '../controllers/virtualTour/DeleteTour/deleteTourValidate';
import uploadHandler from '../helpers/uploadHandler';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import moveToTrashValidate from '../controllers/virtualTour/moveToTrash/moveToTrashValidator';
import { moveToTrash } from '../controllers/virtualTour/moveToTrash';
import restoreVirtualTourValidate from '../controllers/virtualTour/restoreVirtualTour/restoreVirtualTourValidator';
import { restoreVirtualTour } from '../controllers/virtualTour/restoreVirtualTour';
import getAllTourValidate from '../controllers/virtualTour/GetAllTours/getAllTourValidate';
import { GetAllTour } from '../controllers/virtualTour/GetAllTours';
import { GetStructuredTourById } from '../controllers/virtualTour/GetStructuredTourById';
import { AddImageToTour } from '../controllers/virtualTour/AddImageToTour';
import { UpdateTour } from '../controllers/virtualTour/UpdateTour';
import updateTourValidate from '../controllers/virtualTour/UpdateTour/updateTourValidate';
import { DeleteImageFromTour } from '../controllers/virtualTour/DeleteImageFromTour';
import { UpdateTourImage } from '../controllers/virtualTour/UpdateTourImage';
import { GetTourImage } from '../controllers/virtualTour/GetTourImage';
import { GetTourImages } from '../controllers/virtualTour/GetTourImages';
import { AddGroupToTour } from '../controllers/virtualTour/AddGroupToTour';
import { GetTourGroup } from '../controllers/virtualTour/GetTourGroup';
import { GetTourGroupById } from '../controllers/virtualTour/GetTourGroupById';
import { UpdateTourGroup } from '../controllers/virtualTour/UpdateTourGroup';
import { DeleteTourGroup } from '../controllers/virtualTour/DeleteTourGroup';
import GetTourGroupValidator from '../controllers/virtualTour/GetTourGroup/GetTourGroupValidator';
import GetTourGroupByIdValidator from '../controllers/virtualTour/GetTourGroupById/GetTourGroupByIdValidator';
import UpdateTourGroupValidator from '../controllers/virtualTour/UpdateTourGroup/UpdateTourGroupValidator';
import DeleteTourGroupValidator from '../controllers/virtualTour/DeleteTourGroup/DeleteTourGroupValidator';
import AddGroupToTourValidator from '../controllers/virtualTour/AddGroupToTour/AddGroupToTourValidator';
import { AddSubGroup } from '../controllers/virtualTour/AddSubGroup';
import AddSubGroupValidator from '../controllers/virtualTour/AddSubGroup/AddSubGroupValidator';
import { UpdateSubGroup } from '../controllers/virtualTour/UpdateSubGroup';
import UpdateSubGroupValidator from '../controllers/virtualTour/UpdateSubGroup/UpdateSubGroupValidator';
import DeleteSubGroupValidator from '../controllers/virtualTour/DeleteSubGroup/DeleteSubGroupValidator';
import { DeleteSubGroup } from '../controllers/virtualTour/DeleteSubGroup';
import { UpdateSubGroupOrder } from '../controllers/virtualTour/UpdateSubGroupOrder';
import UpdateSubGroupOrderValidator from '../controllers/virtualTour/UpdateSubGroupOrder/UpdateSubGroupOrderValidator';
import { UpdateGroupOrder } from '../controllers/virtualTour/UpdateGroupOrder';
import UpdateGroupOrderValidator from '../controllers/virtualTour/UpdateGroupOrder/UpdateGroupOrderValidator';
import { UpdateGoupIdImg } from '../controllers/virtualTour/UpdateGroupIdImg';
import UpdateGoupIdImgValidator from '../controllers/virtualTour/UpdateGroupIdImg/UpdateGroupIdImgValidator';
import { DeleteLink } from '../controllers/virtualTour/DeleteLink';
import DeleteLinkValidator from '../controllers/virtualTour/DeleteLink/DeleteLinkValidator';
import { AddLink } from '../controllers/virtualTour/AddLink';
import AddLinkValidator from '../controllers/virtualTour/AddLink/AddLinkValidator';
import { UpdateLink } from '../controllers/virtualTour/UpdateLink';
import UpdateLinkValidator from '../controllers/virtualTour/UpdateLink/UpdateLinkValidator';
import { UpdateSubGroupIdImg } from '../controllers/virtualTour/UpdateSubGroupIdImg';
import UpdateSubGroupIdImgValidator from '../controllers/virtualTour/UpdateSubGroupIdImg/UpdateSubGroupIdImgValidator';
import DuplicateTourImageValidator from '../controllers/virtualTour/DuplicateTourImage/DuplicateTourImageValidator';
import { DuplicateTourImage } from '../controllers/virtualTour/DuplicateTourImage';
import convertExternalTourValidate from '../controllers/virtualTour/ConvertExternalTour/convertExternalTourValidator';
import { ConvertExternalTour } from '../controllers/virtualTour/ConvertExternalTour';
import { UpdateTourImageAPI } from '../controllers/virtualTour/UpdateTourImageAPI';
import MoveToUnCategoryValidator from '../controllers/virtualTour/MoveToUnCategory/MoveToUnCategoryValidator';
import { MoveToUnCategory } from '../controllers/virtualTour/MoveToUnCategory';
import { AddTourLabel } from '../controllers/virtualTour/AddTourLabel';
import AddTourLabelValidate from '../controllers/virtualTour/AddTourLabel/AddTourLabelValidator';
import { UpdateTourLabel } from '../controllers/virtualTour/UpdateTourLabel';
import UpdateTourLabelValidate from '../controllers/virtualTour/UpdateTourLabel/UpdateTourLabelValidator';
import DeleteTourLabelValidate from '../controllers/virtualTour/DeleteTourLabel/DeleteTourLabelValidator';
import { DeleteTourLabel } from '../controllers/virtualTour/DeleteTourLabel';

const router = express.Router();

router.post(
  '/createVirtualTour',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'model' }, { name: 'camera' }], 'output'),
  CreateVirtualTourValidate,
  CreateVirtualTour,
);
router.get(
  '/getTourById/:project_id/:tour_id',
  authMiddleware,
  organizationAccessMiddleware,
  getTourValidate,
  GetTourById,
);
router.get(
  '/getAllTour/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  getAllTourValidate,
  GetAllTour,
),
router.get(
  '/getStructuredTourById/:project_id/:tour_id',
  authMiddleware,
  organizationAccessMiddleware,
  GetStructuredTourById,
),
router.post(
  '/updateTour',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'model' }, { name: 'camera' }], 'output'),
  updateTourValidate,
  UpdateTour,
),
router.post(
  '/deleteTour',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  deleteTourValidate,
  DeleteTour,
);
router.post(
  '/moveToTrash',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreVirtualTour',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreVirtualTourValidate,
  restoreVirtualTour,
);
router.post(
  '/addImageToTour',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  AddImageToTour,
);
router.post(
  '/updateTourImage',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'url' }, { name: 'thumbnail' }], 'output'),
  UpdateTourImage,
);
router.post(
  '/deleteImageFromTour',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteImageFromTour,
);
router.post(
  '/duplicateTourImage',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DuplicateTourImageValidator,
  DuplicateTourImage,
);
router.get(
  '/getTourImage/:project_id/:tour_id/:image_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  GetTourImage,
);
router.get(
  '/getTourImages/:project_id/:tour_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  GetTourImages,
);
router.post(
  '/addGroupToTour',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  AddGroupToTourValidator,
  AddGroupToTour,
);
router.get(
  '/getTourGroup/:project_id/:tour_id',
  authMiddleware,
  organizationAccessMiddleware,
  GetTourGroupValidator,
  GetTourGroup,
);
router.get(
  '/getTourGroupById/:project_id/:tour_id/:group_id',
  authMiddleware,
  organizationAccessMiddleware,
  GetTourGroupByIdValidator,
  GetTourGroupById,
);
router.post(
  '/updateTourGroup',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateTourGroupValidator,
  UpdateTourGroup,
),
router.post(
  '/deleteTourGroup',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteTourGroupValidator,
  DeleteTourGroup,
);
router.post(
  '/addSubGroup',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  AddSubGroupValidator,
  AddSubGroup,
);
router.post(
  '/updateSubGroup',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateSubGroupValidator,
  UpdateSubGroup,
);
router.post(
  '/deleteSubGroup',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteSubGroupValidator,
  DeleteSubGroup,
);
router.post(
  '/updateSubGroupOrder',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateSubGroupOrderValidator,
  UpdateSubGroupOrder,
);
router.post(
  '/updateGroupOrder',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateGroupOrderValidator,
  UpdateGroupOrder,
);

router.post(
  '/updateGoupIdImg',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateGoupIdImgValidator,
  UpdateGoupIdImg,
);

router.post(
  '/updateSubGroupIdImg',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateSubGroupIdImgValidator,
  UpdateSubGroupIdImg,
);

router.post(
  '/addLink',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  AddLinkValidator,
  AddLink,
);

router.post(
  '/updateLink',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateLinkValidator,
  UpdateLink,
);

router.post(
  '/deleteLink',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteLinkValidator,
  DeleteLink,
);

router.post(
  '/convertExternalTour',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  convertExternalTourValidate,
  ConvertExternalTour,
);

router.post(
  '/UpdateTourImageAPI',
  UpdateTourImageAPI,
);

router.post(
  '/moveToUnCategory',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  MoveToUnCategoryValidator,
  MoveToUnCategory,
);

router.post(
  '/AddTourLabel',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  AddTourLabelValidate,
  AddTourLabel,
);

router.post(
  '/UpdateTourLabel',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateTourLabelValidate,
  UpdateTourLabel,
);

router.post(
  '/DeleteTourLabel',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteTourLabelValidate,
  DeleteTourLabel,
);

export default router;

import express from 'express';
import createScene from '../controllers/projectScenes/createScene';
import { getAllScenes } from '../controllers/projectScenes/getAllScenes';
import { getScene } from '../controllers/projectScenes/getScene';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import getAllScenesValidate from '../controllers/projectScenes/getAllScenes/getAllScenesValidators';
import getSceneValidate from '../controllers/projectScenes/getScene/getSceneValidator';
import CreateSceneValidate from '../controllers/projectScenes/createScene/createSceneValidator';
import { authMiddleware } from '../middlewares/DashboardAccess';
import uploadHandler from '../helpers/uploadHandler';
import { updateFloors } from '../controllers/projectScenes/updateFloors';
import UpdateSceneValidate from '../controllers/projectScenes/updateScene/updateSceneValidate';
import updateScene from '../controllers/projectScenes/updateScene';
import UpdateSceneFilesValidate from '../controllers/projectScenes/updateSceneFile/updateSceneFilesValidate';
import updateSceneFiles from '../controllers/projectScenes/updateSceneFile';
import deleteSceneFiles from '../controllers/projectScenes/deleteSceneFiles';
import { moveToTrash } from '../controllers/projectScenes/moveToTrash';
import { restoreScenes } from '../controllers/projectScenes/restoreScenes';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { updateBulkSceneFrames } from '../controllers/projectScenes/updateSceneFrame';
import updateBulkSceneFrameValidate from '../controllers/projectScenes/updateSceneFrame/updateBulkSceneFrameValidator';
import updateSceneAPI from '../controllers/projectScenes/updateSceneAPI';
import convertSceneType from '../controllers/projectScenes/convertSceneType';
import moveToTrashValidate from '../controllers/projectScenes/moveToTrash/moveToTrashValidator';
import restoreScenesValidate from '../controllers/projectScenes/restoreScenes/restoreScenesValidator';
import converSceneTypeValidate from '../controllers/projectScenes/convertSceneType/convertSceneTypeValidator';
const router = express.Router();

router.post(
  '/createScene',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler(
    [
      { name: 'info_icon' },
      { name: 'lowRes' },
      { name: 'highRes' },
      { name: 'lowResNight' },
      { name: 'highResNight' },
      { name: 'video' },
      { name: 'gsplat' },
      { name: 'file' },
    ],
    'masterScenes/',
  ),
  CreateSceneValidate,
  createScene,
);
router.post(
  '/updateScene',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateSceneValidate,
  updateScene,
);
router.post(
  '/updateScene/api',
  updateSceneAPI,
);
router.post(
  '/updateSceneFiles',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler(
    [
      { name: 'info_icon' },
      { name: 'lowRes' },
      { name: 'lowResNight' },
      { name: 'highRes' },
      { name: 'highResNight' },
      { name: 'video' },
    ],
    'masterScenes/',
  ),
  UpdateSceneFilesValidate,
  updateSceneFiles,
);
router.post(
  '/deleteSceneFiles',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateSceneFilesValidate,
  deleteSceneFiles,
);
router.get(
  '/getAllScenes/:project_id',
  getAllScenesValidate,
  authMiddleware,
  organizationAccessMiddleware,
  getAllScenes,
);
router.get(
  '/:project_id/getScene/:scene_id',
  getSceneValidate,
  authMiddleware,
  organizationAccessMiddleware,
  getScene,
);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreScenes/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreScenesValidate,
  restoreScenes,
);

router.post(
  '/updateFloors',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateFloors,
);

router.post(
  '/updateBulkSceneFrames',
  authMiddleware,
  organizationAccessMiddleware,
  updateBulkSceneFrameValidate,
  updateBulkSceneFrames,
);
router.post(
  '/convertSceneType',
  authMiddleware,
  organizationAccessMiddleware,
  converSceneTypeValidate,
  convertSceneType,
);

export default router;

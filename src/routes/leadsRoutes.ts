import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { CreateLead } from '../controllers/leads/CreateLeads';
import { UpdateLeadDuration } from '../controllers/leads/UpdateDuration';
import { GetLeads } from '../controllers/leads/GetLeads';
import { JoinSession } from '../controllers/leads/JoinSession';
import { LeaveSession } from '../controllers/leads/LeaveSession';
import CreateLeadValidate from '../controllers/leads/CreateLeads/createLeadValidate';
import GetLeadValidate from '../controllers/leads/GetLeads/getLeadValidator';
import { GetAnalytics } from '../controllers/leads/GetAnalytics';
import updateLeadDurationValidate from '../controllers/leads/UpdateDuration/UpdateLeadValidator';
import joinSessionValidate from '../controllers/leads/JoinSession/joinSessionValidator';
import leaveSessionValidate from '../controllers/leads/LeaveSession/leaveSessionValidator';
import getLeadAnalyticsValidator from '../controllers/leads/GetAnalytics/getAnalyticsValidator';
import { DeleteLead } from '../controllers/leads/DeleteLeads';
import DeleteLeadValidate from '../controllers/leads/DeleteLeads/DeleteLeadValidate';
import UpdateLeadValidate from '../controllers/leads/UpdateLeads/UpdateLeadValidate';
import { UpdateLead } from '../controllers/leads/UpdateLeads';
const router = express.Router();
router.post('/CreateLead', CreateLeadValidate, authMiddleware, organizationAccessMiddleware, CreateLead);
router.post('/UpdateDuration', updateLeadDurationValidate, UpdateLeadDuration);
router.post('/JoinSession', joinSessionValidate, JoinSession);
router.post('/LeaveSession', leaveSessionValidate, LeaveSession);
router.post(
  '/GetLeads',
  GetLeadValidate,
  authMiddleware,
  organizationAccessMiddleware,
  GetLeads,
);
router.post('/DeleteLead/:lead_id', DeleteLeadValidate, authMiddleware, organizationAccessMiddleware, DeleteLead);
router.post('/UpdateLead', UpdateLeadValidate, authMiddleware, organizationAccessMiddleware, UpdateLead);
router.get(
  '/GetAnalytics',
  getLeadAnalyticsValidator,
  authMiddleware,
  organizationAccessMiddleware,
  GetAnalytics,
);
router.post(
  '/CreateLeadsFromWeblite',
  CreateLead,
);
export default router;

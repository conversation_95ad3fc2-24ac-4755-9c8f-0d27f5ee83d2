import express from 'express';
import { AuthenticateUser } from '../controllers/user/AuthenticateUser';
import { ResetPassword } from '../controllers/user/ResetPassword';
import { GetUserDetails } from '../controllers/user/GetUserDetails';
import { CreateUser } from '../controllers/user/CreateUser';
import { authMiddleware } from '../middlewares/DashboardAccess';
import userValidate from '../controllers/user/AuthenticateUser/userValidate';
import resetUserValidate from '../controllers/user/ResetPassword/resetUserValidate';
import getUserValidate from '../controllers/user/GetUserDetails/getUserValidate';
import { UpdateUserDetails } from '../controllers/user/UpdateUserDetail/index';
import { GetUserRole } from '../controllers/user/GetUserRole';
import UpdateUserDetailsValidate from '../controllers/user/UpdateUserDetail/updateUserValidator';
import { UploadProfilePicture } from '../helpers/uploadFirebase';

import { updateFcmTokenController } from '../controllers/user/fcmToken/Index';
import { validateFcmTokenUpdate } from '../controllers/user/fcmToken/fcmTokenValidate';
import { SendEmailVerification } from '../controllers/user/SendEmailVerification';
import sendEmailVerificationValidate from '../controllers/user/SendEmailVerification/sendEmailVerificationValidate';

const router = express.Router();
router.post('/CreateUser', authMiddleware, getUserValidate, CreateUser);
router.post('/AuthenticateUser', userValidate, AuthenticateUser);
router.post('/ResetPassword', resetUserValidate, ResetPassword);
router.post('/GetUserDetails', authMiddleware, getUserValidate, GetUserDetails);
router.post('/GetUserRole', getUserValidate, authMiddleware, GetUserRole);
router.post('/fcmToken', authMiddleware, validateFcmTokenUpdate, updateFcmTokenController);
router.put('/UpdateUserDetails', UploadProfilePicture, authMiddleware, UpdateUserDetailsValidate, UpdateUserDetails);
router.post('/SendEmailVerification', authMiddleware, sendEmailVerificationValidate, SendEmailVerification);
export default router;

import express from 'express';
const router = express.Router();
import { CreateProject } from '../controllers/projects/CreateProject';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { GetProject } from '../controllers/projects/GetProject';
import { GetListOfProjects } from '../controllers/projects/GetListOfProjects';
import GetProjectValidate from '../controllers/projects/GetListOfProjects/GetProjectValidate';
import { UpdateProjectSettings } from '../controllers/projects/UpdateProject';
import CreateProjectValidate from '../controllers/projects/CreateProject/CreateProjectValidate';
import UpdateProjectValidate from '../controllers/projects/UpdateProject/UpdateProjectValidate';
import { UploadProjectSettingFiles } from '../controllers/projects/UploadSettingFiles';
import uploadHandler from '../helpers/uploadHandler';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { updateGallerySettings } from '../controllers/projects/updateGallerySettings';
import updateGallerySettingsValidate
  from '../controllers/projects/updateGallerySettings/updateGallerySettingsValidator';
import updateAmenitySettingsValidate
  from '../controllers/projects/updateAmenitySettings/updateAmenitySettingsValidator';
import { updateAmenitySettings } from '../controllers/projects/updateAmenitySettings';
import { authChecker } from '../middlewares/AuthChecker';
import moveToTrashValidate from '../controllers/projects/moveToTrash/moveToTrashValidator';
import { moveToTrash } from '../controllers/projects/moveToTrash';
import { Editproject } from '../controllers/projects/EditProjects';
import EditProjectValidate from '../controllers/projects/EditProjects/EditProjectValidate';
import { restoreProjects } from '../controllers/projects/RestoreProjects';
import restoreProjectValidate from '../controllers/projects/RestoreProjects/restoreProjectValidator';
import { ExportProject } from '../controllers/projects/ExportProject';
import { ImportProject } from '../controllers/projects/ImportProject';

router.post(
  '/CreateProject',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'project_thumbnail' }], 'output'),
  CreateProjectValidate,
  CreateProject,
);
router.post(
  '/EditProject',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'project_thumbnail' }], 'output'),
  EditProjectValidate,
  Editproject,
);
router.get(
  '/GetProject/:project_id',
  GetProjectValidate,
  authMiddleware,
  organizationAccessMiddleware,
  GetProject,
);
router.get(
  '/GetListOfProjects',
  GetProjectValidate,
  authChecker,
  GetListOfProjects,
);
router.get(
  '/GetListOfProjects',
  GetProjectValidate,
  GetListOfProjects,
);
router.post('/uploadSettingFiles',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'branding_logo' },
    {name: 'branding_logo_dark'},
    {name: 'thumbnail'}, {name: 'file'},
    {name: 'hologram_project_logo'},
    {name: 'welcome_thumbnail'},
    { name: 'font_url' },
    {name: 'welcome_video'}], 'output'), // Project logo - hologram project
  UploadProjectSettingFiles,
);
router.post(
  '/UpdateProjectSettings',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'welcome_video' }, { name: 'welcome_thumbnail' }], 'output'),
  UpdateProjectValidate,
  UpdateProjectSettings,
);
router.post('/updateGallerySettings',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateGallerySettingsValidate,
  updateGallerySettings,
);

router.post('/updateAmenitySettings',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateAmenitySettingsValidate,
  updateAmenitySettings,
);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);

router.post(
  '/restoreProject',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreProjectValidate,
  restoreProjects,
);

router.get(
  '/ExportProject/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  ExportProject,
);

router.post(
  '/ImportProject',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  ImportProject,
);

export default router;

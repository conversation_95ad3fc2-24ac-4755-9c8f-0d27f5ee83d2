import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { getStatus } from '../controllers/status/getStatuses';
import { createSubstatus } from '../controllers/status/createSubstatus';
import { createSubstatusValidator } from '../controllers/status/createSubstatus/createSubstatusValidator';
import { getAllSubstatus } from '../controllers/status/getAllSubstatus';

const router = express.Router();

router.get(
  '/getStatus/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  getStatus,
);

router.get(
  '/:project_id/getAllSubstatus',
  authMiddleware,
  organizationAccessMiddleware,
  getAllSubstatus,
);

router.post(
  '/:project_id/createSubstatus',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  createSubstatusValidator,
  createSubstatus,
);

export default router;

import express from 'express';
import createLandmark from '../controllers/masterLandmarks/createLandmark';
import { getListofLandmark } from '../controllers/masterLandmarks/getListofLandmark';
import getListofLandmarksValidate from '../controllers/masterLandmarks/getListofLandmark/getListofLandmarks';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import CreateLandmarkValidate from '../controllers/masterLandmarks/createLandmark/createLandmarkValidator';
import uploadHandler from '../helpers/uploadHandler';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import updateLandmark from '../controllers/masterLandmarks/updateLandmark';
import updateLandmarkValidate from '../controllers/masterLandmarks/updateLandmark/updateLandmarkValidate';
import { moveToTrash } from '../controllers/masterLandmarks/moveToTrash';
import moveToTrashValidate from '../controllers/masterLandmarks/moveToTrash/moveToTrashValidator';
import { restoreLandmark } from '../controllers/masterLandmarks/restoreLandmark';
import restoreLandmarkValidate from '../controllers/masterLandmarks/restoreLandmark/restoreLandmarkValidator';
const router = express.Router();

router.post(
  '/createLandmark',
  authMiddleware, organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }], 'masterLandmarks/'),
  CreateLandmarkValidate,
  createLandmark,
);
router.get('/getListofLandmark',
  getListofLandmarksValidate,
  authMiddleware,
  organizationAccessMiddleware,  getListofLandmark);
router.post('/updateLandmark',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'thumbnail' }], 'masterLandmarks/'),
  updateLandmarkValidate,
  updateLandmark);
router.post(
  '/moveToTrash',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreLandmark',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreLandmarkValidate,
  restoreLandmark,
);

export default router;

import express from 'express';
import CreateUnitplan from '../controllers/unitplan/createUnitplans';
import CreateUnitplanValidate from '../controllers/unitplan/createUnitplans/CreateUnitplanValidate';
import uploadHandler from '../helpers/uploadHandler';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';

const router = express.Router();

router.post(
  '/CreateUnitPlan',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'lowRes' }, { name: 'highRes' }], 'output'),
  CreateUnitplanValidate,
  CreateUnitplan,
);

export default router;

import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import uploadHandler from '../helpers/uploadHandler';
import { createGalleryItem } from '../controllers/gallery/CreateGalleryItem';
import CreateGalleryItemValidate from '../controllers/gallery/CreateGalleryItem/createGalleryItemValidator';
import { getGallery } from '../controllers/gallery/getGallery';
import getGalleryValidator from '../controllers/gallery/getGallery/getGalleryValidator';
import { DeleteGalleryItem } from '../controllers/gallery/deleteGalleryItem';
import DeleteGalleryItemValidator from '../controllers/gallery/deleteGalleryItem/deleteGalleryValidator';
import updateGalleryItemValidate from '../controllers/gallery/updateGalleryItem/updateGalleryItemValidator';
import { updateGalleryItem } from '../controllers/gallery/updateGalleryItem';
import { getCategories } from '../controllers/gallery/GetCategories';
import { updateBulkGalleryItems } from '../controllers/gallery/updateBulkGalleryItems';
import updateBulkGalleryItemsValidate
  from '../controllers/gallery/updateBulkGalleryItems/updateBulkGalleryItemsValidator';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { moveToTrash } from '../controllers/gallery/moveToTrash';
import moveToTrashValidate from '../controllers/gallery/moveToTrash/moveToTrashValidator';
import { restoreGallery } from '../controllers/gallery/restoreGallery';
import restoreGalleryValidate from '../controllers/gallery/restoreGallery/restoreGalleryValidator';
import GallerySyncUpValidate from '../controllers/gallery/SyncUpGallery/syncUpGalleryValidator';
import { SyncUpGalleryData } from '../controllers/gallery/SyncUpGallery';

const router = express.Router();
router.post('/createGalleryItem',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'thumbnail' }, { name: 'file' }], 'output'),
  CreateGalleryItemValidate,
  createGalleryItem);
router.post('/updateGalleryItem',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'thumbnail' }, { name: 'file' }], 'output'),
  updateGalleryItemValidate,
  updateGalleryItem);
router.get('/getGallery',
  authMiddleware,
  organizationAccessMiddleware,
  getGalleryValidator,
  getGallery);
router.get('/getCategories',
  authMiddleware,
  organizationAccessMiddleware,
  getGalleryValidator,
  getCategories);
router.post('/deleteGalleryItem',
  authMiddleware,
  organizationAccessMiddleware,
  DeleteGalleryItemValidator,
  DeleteGalleryItem);
router.post('/updateBulkGalleryItems',
  authMiddleware,
  organizationAccessMiddleware,
  updateBulkGalleryItemsValidate,
  updateBulkGalleryItems);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreGallery/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreGalleryValidate,
  restoreGallery,
);

router.get('/syncUpGalleryData/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  GallerySyncUpValidate,
  SyncUpGalleryData,
);
export default router;

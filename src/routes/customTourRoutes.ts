import express from 'express';
import {createImage} from '../controllers/customTour/createImage';
import {organizationAccessMiddleware} from '../middlewares/OrganizationAccess';
import { authMiddleware } from '../middlewares/DashboardAccess';
import uploadHandler from '../helpers/uploadHandler';
import { updateImage } from '../controllers/customTour/updateImage';
import { replaceImage } from '../controllers/customTour/replaceImage';
import { deleteImage } from '../controllers/customTour/deleteImage';
import createImageValidate from '../controllers/customTour/createImage/createImageValidator';
import updateImageValidate from '../controllers/customTour/updateImage/updateImageValidator';
import replaceImageValidate from '../controllers/customTour/replaceImage/replaceImageValidator';
import deleteImageValidate from '../controllers/customTour/deleteImage/deleteImagevalidator';
import { getImages } from '../controllers/customTour/getImages';
import getImagesValidate from '../controllers/customTour/getImages/getImagesValidator';
import createHotspotValidator from '../controllers/customTour/CreateHotspot/createHotspotValidator';
import { CreateHotspot } from '../controllers/customTour/CreateHotspot';
import { UpdateHotspot } from '../controllers/customTour/UpdateHotspot';
import updateHotspotValidator from '../controllers/customTour/UpdateHotspot/updateHotspotValidator';
import { DeleteHotspot } from '../controllers/customTour/DeleteHotspot';
import deleteHotspotValidator from '../controllers/customTour/DeleteHotspot/deleteHotspotValidator';
import { accessControlMiddleware } from '../middlewares/AccessControl';

const router = express.Router();

router.post(
  '/createImage',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'thumbnail' }, {name: 'url'}], 'tour_thumbnail'),
  createImageValidate,
  createImage,
);
router.post(
  '/updateImage',
  authMiddleware,
  organizationAccessMiddleware,
  updateImageValidate,
  updateImage,
);
router.post(
  '/replaceImage',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'thumbnail' }, {name: 'url'}], 'tour_thumbnail'),
  replaceImageValidate,
  replaceImage,
);
router.post(
  '/deleteImage',
  authMiddleware,
  organizationAccessMiddleware,
  deleteImageValidate,
  deleteImage,
);
router.get(
  '/getImages',
  authMiddleware,
  organizationAccessMiddleware,
  getImagesValidate,
  getImages,
  createImage,
);
router.post(
  '/createHotspot',
  authMiddleware,
  organizationAccessMiddleware,
  createHotspotValidator,
  accessControlMiddleware(['admin']),
  CreateHotspot,
);
router.post(
  '/updateHotspot',
  authMiddleware,
  organizationAccessMiddleware,
  updateHotspotValidator,
  UpdateHotspot,
);
router.post(
  '/deleteHotspot',
  authMiddleware,
  organizationAccessMiddleware,
  deleteHotspotValidator,
  DeleteHotspot,
);
export default router;

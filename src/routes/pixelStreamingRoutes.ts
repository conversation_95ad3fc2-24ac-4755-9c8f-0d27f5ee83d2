import express from 'express';
import { JWTAuthControl } from '../middlewares/jwtController';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { getLatestScaleSetVersion } from '../controllers/pixelStreaming/getLatestScaleSetVersion';
import getLastScaleSetVersionValidate from
  '../controllers/pixelStreaming/getLatestScaleSetVersion/getLatestScaleSetVersionValidator';
import addScaleSetVersionValidate from '../controllers/pixelStreaming/addScaleSetVersion/addScaleSetVersionValidator';
import { addScaleSetVersion } from '../controllers/pixelStreaming/addScaleSetVersion';
import { addScaleSet } from '../controllers/pixelStreaming/addScaleSet';
import { sendToSlack } from '../controllers/pixelStreaming/sendMessageToSlack';
import { handleScaleUp } from '../controllers/pixelStreaming/handleScaleUp';
import { handleScaleDown } from '../controllers/pixelStreaming/handleScaleDown';

const router = express.Router();

router.post(
  '/getlatestscalesetversion',
  JWTAuthControl,
  getLastScaleSetVersionValidate,
  getLatestScaleSetVersion,
);
router.post(
  '/addscalesetversion',
  JWTAuthControl,
  addScaleSetVersionValidate,
  addScaleSetVersion,
);
router.post('/addscaleset', authMiddleware, addScaleSet);
router.post('/toSlack', sendToSlack);
router.post('/handlescaleup',
  handleScaleUp,
);
router.post('/handlescaledown',
  handleScaleDown,
);
export default router;

import express from 'express';
import CreateCurrencyRatioValidate from '../controllers/providerCurrency/CreateCurrency/CreateCurrencyRatioVaidator';
import BulkBaseCurrencyRatioValidate
  from '../controllers/providerCurrency/BulkCreateCurrency/BulkBaseCurrencyRatioValidate';
import providerExchangeRatesUpdate from '../controllers/providerCurrency/UpdateCurrency';
import providerExchangeRatesCreate from '../controllers/providerCurrency/CreateCurrency';
import BulkCurrencyRatioCreate from '../controllers/providerCurrency/BulkCreateCurrency';
import BulkCurrencyRatioUpdate from '../controllers/providerCurrency/BulkUpdateCurrency';
import getListOfCurrencies from '../controllers/providerCurrency/GetAllCurrencies';
import getCurrencyByBase from '../controllers/providerCurrency/GetCurrency';

const router = express.Router();

router.post(
  '/createCurrency',
  CreateCurrencyRatioValidate,
  providerExchangeRatesCreate,
);
router.post(
  '/bulkCreateCurrency',
  BulkBaseCurrencyRatioValidate,
  BulkCurrencyRatioCreate,
);
router.post(
  '/updateCurrency',
  CreateCurrencyRatioValidate,
  providerExchangeRatesUpdate,
);
router.post(
  '/bulkUpdateCurrency',
  BulkBaseCurrencyRatioValidate,
  BulkCurrencyRatioUpdate,
);
router.get(
  '/:provider_name/getListofCurrencies',
  getListOfCurrencies,
);
router.get(
  '/:provider_name/:base_currency/getCurrency',
  getCurrencyByBase,
);

export default router;

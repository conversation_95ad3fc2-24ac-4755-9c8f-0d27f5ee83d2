import express from 'express';
import { Translate } from '../controllers/translation/Translate/translate';
import { GetAllTranslation } from '../controllers/translation/GetAllTranslations/GetAllTranslation';
import { GetTranslation } from '../controllers/translation/GetTranslation';
import { AddNewLanguage } from '../controllers/translation/AddNewLanguage/AddNewLanguage';
import { UpdateTranslationById } from '../controllers/translation/UpdateTranslationById/UpdateTranslationById';
import { RemoveLanguage } from '../controllers/translation/RemoveLanguage/RemoveLanguage';
import { DeleteTranslationById } from '../controllers/translation/DeleteTranslationById/DeleteTranslationById';
import AddNewLanguageValidate from '../controllers/translation/AddNewLanguage/AddNewLanguageValidator';
import TranslateValidator from '../controllers/translation/Translate/TranslateValidator';
import RemoveLanguageValidator from '../controllers/translation/RemoveLanguage/RemoveLanguageValidator';
import UpdateTranslationByIdValidate
  from '../controllers/translation/UpdateTranslationById/UpdateTranslationByIdValidator';
import DeleteTranslationByIdValidator
  from '../controllers/translation/DeleteTranslationById/DeleteTranslationByIdValidator';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { TranslateProjectData } from '../controllers/translation/TranslateProjectData';
import AddTranslationValidator from '../controllers/translation/Addtranslation/AddTranslationValidator';
import { AddTranslation } from '../controllers/translation/Addtranslation/AddTranslation';
import { DownloadTranslations } from '../controllers/translation/Downloadtranslations/DownloadTranslate';
import { ImportTranslations } from '../controllers/translation/ImportTranslations/ImportTranslations';
import ImportTranslationsValidator from '../controllers/translation/ImportTranslations/ImportTranslationsValidator';

const router = express.Router();

router.get(
  '/translate',
  authMiddleware,
  organizationAccessMiddleware,
  TranslateValidator,
  Translate,
);
router.get(
  '/GetTranslation',
  authMiddleware,
  organizationAccessMiddleware,
  GetTranslation,
);
router.get(
  '/GetAllTranslation',
  authMiddleware,
  organizationAccessMiddleware,
  GetAllTranslation,
);
router.get(
  '/AddNewLanguage',
  AddNewLanguageValidate,
  authMiddleware,
  organizationAccessMiddleware,
  AddNewLanguage,
);
router.post(
  '/UpdateTranslationById',
  authMiddleware,
  organizationAccessMiddleware,
  UpdateTranslationByIdValidate,
  UpdateTranslationById,
);
router.get(
  '/RemoveLanguage',
  authMiddleware,
  organizationAccessMiddleware,
  RemoveLanguageValidator,
  RemoveLanguage,
);
router.get(
  '/DeleteTranslationById',
  authMiddleware,
  organizationAccessMiddleware,
  DeleteTranslationByIdValidator,
  DeleteTranslationById,
);
router.get(
  '/TranslateProjectData',
  authMiddleware,
  organizationAccessMiddleware,
  TranslateProjectData,
);
router.post(
  '/AddTranslation',
  authMiddleware,
  organizationAccessMiddleware,
  AddTranslationValidator,
  AddTranslation,
);

router.get(
  '/download',
  authMiddleware,
  organizationAccessMiddleware,
  DownloadTranslations,
);

router.post(
  '/translations/import',
  authMiddleware,
  organizationAccessMiddleware,
  ImportTranslationsValidator,
  ImportTranslations,
);
export default router;

import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { CreateInvitation } from '../controllers/invitations/CreateInvitation';
import { GetInvitations } from '../controllers/invitations/GetInvitations';
import { DeleteInvitation } from '../controllers/invitations/DeleteInvitation';
import { AcceptInvitation } from '../controllers/invitations/AcceptInvitation';
import createInvitationValidate from '../controllers/invitations/CreateInvitation/createinvitationValidator';
import acceptInviteValidate from '../controllers/invitations/AcceptInvitation/acceptInviteValidator';
import deleteInviteValidate from '../controllers/invitations/DeleteInvitation/deleteInviteValidator';
import { moveToTrash } from '../controllers/invitations/MoveToTrash';
import { SendInvitation } from '../controllers/invitations/SendInvitation';
const router = express.Router();
router.post('/CreateInvitation', createInvitationValidate, CreateInvitation);
router.get(
  '/GetInvitations',
  authMiddleware,
  organizationAccessMiddleware,
  GetInvitations,
);
router.post(
  '/AcceptInvitation',
  acceptInviteValidate,
  authMiddleware,
  AcceptInvitation,
);
router.post(
  '/SendInvitation',
  authMiddleware,
  organizationAccessMiddleware,
  SendInvitation,
);
router.post(
  '/DeleteInvitation',
  deleteInviteValidate,
  authMiddleware,
  organizationAccessMiddleware,
  DeleteInvitation,
);
router.post(
  '/moveToTrash/:invitation_id',
  authMiddleware,
  organizationAccessMiddleware,
  moveToTrash,
);
export default router;

import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import {  CreateIconLibrary, CreateOrgIconLibrary } from '../controllers/iconLibrary/CreateIcon';
import uploadHandler from '../helpers/uploadHandler';
import { UpdateIconLibrary, UpdateOrgIconLibrary } from '../controllers/iconLibrary/UpdateIcon';
import { GetIconsLibrary, GetOrgIconsLibrary } from '../controllers/iconLibrary/GetIcons';
import { SearchIcon, SearchOrgIcon } from '../controllers/iconLibrary/SearchIcons';
import getIconValidate from '../controllers/iconLibrary/GetIcons/getIconValidate';
import updateIconValidate from '../controllers/iconLibrary/UpdateIcon/updateIconValidate';
import createIconValidate from '../controllers/iconLibrary/CreateIcon/createIconValidate';
import searchIconValidate from '../controllers/iconLibrary/SearchIcons/searchIconValidate';
import { moveIconToTrash } from '../controllers/iconLibrary/moveToTrash';
import moveToTrashValidate from '../controllers/iconLibrary/moveToTrash/moveToTrashValidator';
import { restoreIcon } from '../controllers/iconLibrary/restoreIcon';
import restoreIconValidate from '../controllers/iconLibrary/restoreIcon/restoreIconValidator';

const router = express.Router();

router.post(
  '/createIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler(
    [
      { name: 'icon' },
    ],
    'masterScenes/',
  ),
  createIconValidate,
  CreateIconLibrary,
);

router.post(
  '/updateIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateIconValidate,
  UpdateIconLibrary,
);
router.get(
  '/getIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  getIconValidate,
  GetIconsLibrary,
);

router.get(
  '/searchIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  searchIconValidate,
  SearchIcon,
);

router.post(
  '/moveToTrash',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveIconToTrash,
);

router.post(
  '/restoreIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreIconValidate,
  restoreIcon,
);

// Organization-level icon routes
router.post(
  '/org/createIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler(
    [
      { name: 'icon' },
    ],
    'masterScenes/',
  ),
  createIconValidate,
  CreateOrgIconLibrary,
);

router.post(
  '/org/updateIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateIconValidate,
  UpdateOrgIconLibrary,
);

router.get(
  '/org/getIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  getIconValidate,
  GetOrgIconsLibrary,
);

router.get(
  '/org/searchIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  searchIconValidate,
  SearchOrgIcon,
);

export default router;

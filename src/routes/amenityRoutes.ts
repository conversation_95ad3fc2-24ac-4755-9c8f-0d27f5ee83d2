import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { CreateAmenity } from '../controllers/amenities/CreateAmenity';
import { UpdateAmenity } from '../controllers/amenities/UpdateAmenity';
import { DeleteAmenity } from '../controllers/amenities/DeleteAmenity';
import { GetAmenities } from '../controllers/amenities/GetAmenities';
// Import { CreateMedia } from '../controllers/amenities/CreateMedia';
// Import { UpdateMedia } from '../controllers/amenities/UpdateMedia';
import { DeleteMedia } from '../controllers/amenities/DeleteMedia';
import { updateBulkAmenities } from '../controllers/amenities/UpdatebulkAmenities';
import updateBulkAmenityValidate from '../controllers/amenities/UpdatebulkAmenities/updateBulkAmenitiesValidator';
import CreateAmenityValidate from '../controllers/amenities/CreateAmenity/createAmenityValidator';
import UpdateAmenityValidate from '../controllers/amenities/UpdateAmenity/updateAmenityValidator';
import DeleteAmenityValidate from '../controllers/amenities/DeleteAmenity/DeleteAmenityValidator';
// Import CreateMediaValidate from '../controllers/amenities/CreateMedia/createMediaValidator';
import DeleteMediaValidate from '../controllers/amenities/DeleteMedia/deleteMediaValidator';
import getAmenitiesValidator from '../controllers/amenities/GetAmenities/getAmenitiesValidator';
import uploadHandler from '../helpers/uploadHandler';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { getCategories } from '../controllers/amenities/getCategories';
import getCategoriesValidate from '../controllers/amenities/getCategories/getCategoriesValidator';
import { moveToTrash } from '../controllers/amenities/moveToTrash';
import moveToTrashValidate from '../controllers/amenities/moveToTrash/moveToTrashValidator';
import { restoreAmenity } from '../controllers/amenities/restoreAmenity';
import restoreAmenityValidate from '../controllers/amenities/restoreAmenity/restoreAmenityValidator';
import { SyncUpAmenityData } from '../controllers/amenities/SyncUpAmenity';
import AmenitySyncUpValidate from '../controllers/amenities/SyncUpAmenity/SyncUpAmenityValidator';
import AddLinkValidator from '../controllers/amenities/AddLink/AddLinkValidator';
import { AddLink } from '../controllers/amenities/AddLink';
import UpdateLinkValidator from '../controllers/amenities/UpdateLink/UpdateLinkValidator';
import { UpdateLink } from '../controllers/amenities/UpdateLink';
import { DeleteLink } from '../controllers/amenities/DeleteLink';
import DeleteLinkValidator from '../controllers/amenities/DeleteLink/DeleteLinkValidator';

const router = express.Router();
router.post('/CreateAmenity',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail'}, {name: 'file' }], 'output'),
  CreateAmenityValidate,
  CreateAmenity);
router.post('/UpdateAmenity',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([
    { name: 'thumbnail' },
    { name: 'file' },
  ], '/output'),
  UpdateAmenityValidate,
  UpdateAmenity);
router.post('/DeleteAmenity',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteAmenityValidate,
  DeleteAmenity);

// Router.post('/CreateMedia', // Add validator
//   AuthMiddleware,
//   OrganizationAccessMiddleware,
//   AccessControlMiddleware(['admin']),
//   UploadHandler([{ name: 'media_file' }], 'output/'),
//   CreateMediaValidate,
//   CreateMedia);
// Router.post('/UpdateMedia', // Add validator
//   AuthMiddleware,
//   OrganizationAccessMiddleware,
//   AccessControlMiddleware(['admin']),
//   UploadHandler([{ name: 'thumbnail' }], 'output/amenities/'),
//   CreateMediaValidate,
//   UpdateMedia);

router.post('/DeleteMedia',
  DeleteMediaValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteMedia);
router.get('/GetAmenities',
  getAmenitiesValidator,
  authMiddleware,
  organizationAccessMiddleware,
  GetAmenities);
router.get('/getCategories',
  authMiddleware,
  organizationAccessMiddleware,
  getCategoriesValidate,
  getCategories);
router.post('/updateBulkAmenity',
  authMiddleware,
  organizationAccessMiddleware,
  updateBulkAmenityValidate,
  updateBulkAmenities);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreAmenity/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreAmenityValidate,
  restoreAmenity,
);
router.get('/syncUpAmenityData/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  AmenitySyncUpValidate,
  SyncUpAmenityData,
);
router.post('/addLink',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  AddLinkValidator,
  AddLink,
);
router.post('/updateLink',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateLinkValidator,
  UpdateLink,
);
router.post('/deleteLink',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteLinkValidator,
  DeleteLink,
);
export default router;

import express  from 'express';
import { updateUnitByMetadata } from '../controllers/units/Api/UpdateUnitByMetadata';
import { JWTAuthControl, JWTProjectChecker } from '../middlewares/jwtController';
import updateUnitByMetadataValidator from '../controllers/units/Api/UpdateUnitByMetadata/UpdateUnitByMetadataValidator';
import CreateFilterUnitsValidator from '../controllers/units/Api/CreateFilterUnits/CreateFilterUnitsValidator';
import { createFilterUnits } from '../controllers/units/Api/CreateFilterUnits';
import FiltersDropValidator from '../controllers/units/Api/FiltersDrop/FiltersDropValidator';
import { filterDropCollection } from '../controllers/units/Api/FiltersDrop';
import uploadHandler from '../helpers/uploadHandler';
import CreateUnitplanValidate from '../controllers/unitplan/API/createUnitplans/createUnitsplansValidate';
import CreateUnitplan from '../controllers/unitplan/API/createUnitplans';
import getUnitplansValidate from '../controllers/unitplan/API/getUnitplans/getUnitplansValidate';
import { GetUnitplans } from '../controllers/unitplan/API/getUnitplans';
import deleteUnitplanValidate from '../controllers/unitplan/API/deleteUnitplan/deleteUnitplanValidate';
import { deleteUnitplan } from '../controllers/unitplan/API/deleteUnitplan';
import UpdateUnitplanValidate from '../controllers/unitplan/API/updateUnitplan/UpdateUnitplanValidate';
import UpdateUnitplan from '../controllers/unitplan/API/updateUnitplan';

const router = express.Router();

router.post('/unit/updateUnitByMetadata',
  updateUnitByMetadataValidator,
  JWTAuthControl,
  updateUnitByMetadata,
);

router.post('/unit/filters',
  CreateFilterUnitsValidator,
  JWTProjectChecker,
  createFilterUnits,
);

router.post('/unit/filtersDrop',
  FiltersDropValidator,
  JWTProjectChecker,
  filterDropCollection,
);

router.post(
  '/createUnitplan',
  JWTAuthControl,
  uploadHandler([{ name: 'unitplan_image' }], 'output'),
  CreateUnitplanValidate,
  CreateUnitplan,
);
router.get(
  '/getUnitplans/:project_id',
  JWTAuthControl,
  getUnitplansValidate,
  GetUnitplans,
);
router.post(
  '/deleteUnitplan/:project_id',
  JWTAuthControl,
  deleteUnitplanValidate,
  deleteUnitplan,
);
router.post(
  '/updateUnitplan',
  uploadHandler([{ name: 'unitplan_image' }], 'output'),
  UpdateUnitplanValidate,
  UpdateUnitplan,
);
export default router;

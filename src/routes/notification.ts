import express from 'express';
import { validateNotificationPayload } from '../controllers/notification/trigger/TriggerValidator';
import { sendNotificationController } from '../controllers/notification/trigger/Index';
import { GetNotifications } from '../controllers/notification/GetNotifications/Index';
import { GetNotificationsValidate } from '../controllers/notification/GetNotifications/GetValidator';
import getOrgValidate from '../controllers/org/GetOrganization/getOrgValidator';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { validateNotificationUpdatePayload } from '../controllers/notification/UpdateNotification/UpdateValidator';
import { UpdateNotifications } from '../controllers/notification/UpdateNotification';

const router = express.Router();
router.post(
  '/send',
  validateNotificationPayload,
  sendNotificationController,
);
router.get(
  '/getNotifications',
  getOrgValidate,
  authMiddleware,
  GetNotificationsValidate,
  GetNotifications,
);
router.post(
  '/updateNotifications/:id',
  getOrgValidate,
  authMiddleware,
  validateNotificationUpdatePayload,
  UpdateNotifications,
);
export default router;

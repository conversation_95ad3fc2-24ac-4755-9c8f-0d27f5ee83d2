import express from 'express';
import CreateUnitplan from '../controllers/unitplan/createUnitplans';
import CreateUnitplanValidate from '../controllers/unitplan/createUnitplans/CreateUnitplanValidate';
import uploadHandler from '../helpers/uploadHandler';
import { GetListofUnitplan } from '../controllers/unitplan/getListofUnitplan';
import { GetUnitplan } from '../controllers/unitplan/getUnitplan';
import getListOfUnitplanValidate from '../controllers/unitplan/getListofUnitplan/getListOfUnitplanValidate';
import getUnitplanValidate from '../controllers/unitplan/getUnitplan/getUnitplanValidate';
import createHotspotsValidate from '../controllers/unitplan/createHotspots/createHotspotValidate';
import EditUnitplanValidate from '../controllers/unitplan/editUnitplans/EditUniplanValidate';
import EditUnitplan from '../controllers/unitplan/editUnitplans';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { moveToTrash } from '../controllers/unitplan/moveToTrash';
import moveToTrashValidate from '../controllers/unitplan/moveToTrash/moveToTrashValidator';
import restoreUnitplanValidate from '../controllers/unitplan/restoreUnitplan/restoreUnitplanValidator';
import { restoreUnitplan } from '../controllers/unitplan/restoreUnitplan';
import { getStyles } from '../controllers/unitplan/getStyles';
import getStylesValidator from '../controllers/unitplan/getStyles/getStylesValidator';
import CreateHotspots from '../controllers/unitplan/createHotspots';
import editHotspotValidator from '../controllers/unitplan/editHotspots/editHotspotValidate';
import EditHotspots from '../controllers/unitplan/editHotspots';
import getTypesValidator from '../controllers/unitplan/getType/getTypesValidator';
import { getTypes } from '../controllers/unitplan/getType';
import deleteHotspotValidator from '../controllers/unitplan/deleteHotspots/deleteHotspotValidate';
import DeleteHotspots from '../controllers/unitplan/deleteHotspots';
import { getUnitplanWithSearch } from '../controllers/unitplan/getUnitplanWithSearch';
import getUnitplanWithSearchValidate from '../controllers/unitplan/getUnitplanWithSearch/getUnitplanWithSearchValidate';
import deleteVillaFloorValidate from '../controllers/unitplan/deleteVillaFloor/deleteVillaFloorValidate';
import { deleteVillaFloor } from '../controllers/unitplan/deleteVillaFloor';

const router = express.Router();

router.post(
  '/createUnitplan',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'lowRes' }, { name: 'highRes' }], 'output'),
  CreateUnitplanValidate,
  CreateUnitplan,
);
router.get(
  '/getListOfUnitplan/:project_id',
  getListOfUnitplanValidate,
  GetListofUnitplan,
);
router.post('/getUnitplanWithSearch', authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  getUnitplanWithSearchValidate,
  getUnitplanWithSearch,
);
router.get('/getStyles',
  authMiddleware,
  organizationAccessMiddleware,
  getStylesValidator,
  getStyles,
);
router.get('/getTypes',
  authMiddleware,
  organizationAccessMiddleware,
  getTypesValidator,
  getTypes,
);
router.post(
  '/getUnitplan/:project_id/:unitplan_id',
  getUnitplanValidate,
  GetUnitplan,
);
router.post(
  '/editUnitplan',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'lowRes' }, { name: 'highRes' }], 'output'),
  EditUnitplanValidate,
  EditUnitplan,
);
router.post(
  '/deleteVillaFloor/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  deleteVillaFloorValidate,
  deleteVillaFloor,
);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreUnitplan/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreUnitplan,
  restoreUnitplanValidate,
);
router.post(
  '/createHotspots',
  authMiddleware,
  organizationAccessMiddleware,
  createHotspotsValidate,
  CreateHotspots,
);
router.post(
  '/editHotspots',
  authMiddleware,
  organizationAccessMiddleware,
  editHotspotValidator,
  EditHotspots,
);
router.post(
  '/deleteHotspots',
  authMiddleware,
  organizationAccessMiddleware,
  deleteHotspotValidator,
  DeleteHotspots,
);
export default router;

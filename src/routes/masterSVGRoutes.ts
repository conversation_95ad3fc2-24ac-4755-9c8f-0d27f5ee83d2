import express from 'express';
import createSVG from '../controllers/masterSVG/createSVG';
import { GetSvg } from '../controllers/masterSVG/GetSvg';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import updateLayers from '../controllers/masterSVG/updateLayers';
import getSvgValidate from '../controllers/masterSVG/GetSvg/getSvgValidate';
import updateLayerValidate from '../controllers/masterSVG/updateLayers/updateLayersValidator';
import CreateSVGValidate from '../controllers/masterSVG/createSVG/createSVGValidator';
import uploadHandler from '../helpers/uploadHandler';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { createLayers } from '../controllers/masterSVG/createLayers';
import createLayersValidate from '../controllers/masterSVG/createLayers/createLayersValidator';
import updateSVGLayerValidate from '../controllers/masterSVG/updateSVGLayers/updateSVGLayersValidate';
import { updateSVGLayers } from '../controllers/masterSVG/updateSVGLayers';
import moveToTrashValidate from '../controllers/masterSVG/moveToTrash/moveToTrashValidator';
import { moveToTrash } from '../controllers/masterSVG/moveToTrash';
import restoreSVGValidate from '../controllers/masterSVG/restoreSVG/restoreSVGValidator';
import { restoreSVG } from '../controllers/masterSVG/restoreSVG';
import updateLayersVideoValidate from '../controllers/masterSVG/updateLayersVideoTag/updatelayersVideoValidate';
import updateLayersVideoTag from '../controllers/masterSVG/updateLayersVideoTag';
import { deleteMasterLayers } from '../controllers/masterSVG/deleteLayers';
import deleteMasterLayersValidator from '../controllers/masterSVG/deleteLayers/deleteMasterLayersValidator';
import editSVG from '../controllers/masterSVG/editSVG';
import editSVGValidator from '../controllers/masterSVG/editSVG/editSVGValidator';

const router = express.Router();
router.post('/createSVG',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin'])
  , uploadHandler([{ name: 'svgFile' }], 'svg/')
  , CreateSVGValidate
  , createSVG);
router.post('/updateLayers', authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']), updateLayerValidate, updateLayers);
router.post('/deleteLayer', authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']), deleteMasterLayersValidator, deleteMasterLayers);
router.get(
  '/GetSvg/:scene_id',
  getSvgValidate,
  authMiddleware,
  organizationAccessMiddleware,
  GetSvg,
);
router.post('/createLayers',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'svgFile' }], 'svg/'),
  createLayersValidate,
  createLayers);
router.post('/updatesvgLayer',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'svgFile' }], 'svg/'),
  updateSVGLayerValidate,
  updateSVGLayers);
router.post(
  '/moveToTrash',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreSVG',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreSVGValidate,
  restoreSVG,
);
router.post('/updateLayersVideoTag', authMiddleware, uploadHandler([{name: 'video_tag'}], 'svg/'),
  organizationAccessMiddleware, accessControlMiddleware(['admin']),
  updateLayersVideoValidate, updateLayersVideoTag);
router.post('/editSVG',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  editSVGValidator,
  editSVG);
export default router;

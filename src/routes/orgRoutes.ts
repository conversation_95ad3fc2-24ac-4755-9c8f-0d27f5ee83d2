import express from 'express';
import CreateOrganization from '../controllers/org/CreateOrganization';
import GetOrganization from '../controllers/org/GetOrganization';
import UpdateOrganization from '../controllers/org/UpdateOrganization';
import AddUserToOrganization from '../controllers/org/AddUserToOrganization';
import RemoveUserFromOrganization from '../controllers/org/RemoveUserFromOrganization';
import ListUsersInOrganization from '../controllers/org/ListUsersInOrganization';
import AssignRoleToUser from '../controllers/org/AssignRoleToUser';
import ListProjectsFromOrganization from '../controllers/org/ListProjectsFromOrganization';
import GetProjectInOrganization from '../controllers/org/GetProjectInOrganization';
import { GetUserRole } from '../controllers/org/GetRolesFromUser';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import addUserOrgValidate from '../controllers/org/AddUserToOrganization/addUserOrgValidate';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import uploadHandler from '../helpers/uploadHandler';
import { HandleThumbnailOrg } from '../controllers/org/HandleThumbnailOrg';
import createOrganizationValidate from '../controllers/org/CreateOrganization/createOrgValidator';
import getOrgValidate from '../controllers/org/GetOrganization/getOrgValidator';
import updateOrgValidate from '../controllers/org/UpdateOrganization/updateOrgValidator';
import removeUserOrgValidate from '../controllers/org/RemoveUserFromOrganization/removeUserOrgValidate';
import listUsersOrgValidate from '../controllers/org/ListUsersInOrganization/listUserOrgValidate';
import assignRoletoUserValidate from '../controllers/org/AssignRoleToUser/assignRoletoUserValidate';
import listProjectOrgValidate from '../controllers/org/ListProjectsFromOrganization/listProjectOrgValidate';
import getProjectOrgValidate from '../controllers/org/GetProjectInOrganization/getProjectOrgValidate';
import getUserRoleValidate from '../controllers/org/GetRolesFromUser/getUserRoleValidate';
import checkUserExistence from '../controllers/org/CheckUserID/checkUserExistence';
import getOrganizationByID from '../controllers/org/GetOrganizationByID/getOrganizationByID';
import getOrganizationByUniqueOrgid from
  '../controllers/org/GetOrganizationByUniqueUserID/getOrganizationByUniqueOrgid';
import exchangeRatesSyncUp from '../controllers/org/exchangeRatesSyncUp';
import { verifyGoogleToken } from '../middlewares/GoogleTokenChecker';
import filterProjectValidate from '../controllers/org/FilterProjectFromOrganization/filterProjectsValidate';
import FilterProjectsFromOrganization  from '../controllers/org/FilterProjectFromOrganization';
import UpdateOrgSettings from '../controllers/org/UpdateOrgSettings';
const router = express.Router();
router.post(
  '/CreateOrganization',
  authMiddleware,
  uploadHandler([{ name: 'thumbnail' }], 'output'),
  createOrganizationValidate,
  CreateOrganization,
);
router.post(
  '/GetOrganization',
  getOrgValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin', 'editor', 'reader']),
  GetOrganization,
);
router.post(
  '/UpdateOrganization',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }], 'output'),
  updateOrgValidate,
  UpdateOrganization,
);

router.post(
  '/UpdateOrganizationSettings',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }], 'output'),
  updateOrgValidate,
  UpdateOrgSettings,
);

router.post(
  '/AddUserToOrganization',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  addUserOrgValidate,
  AddUserToOrganization,
);
router.post(
  '/RemoveUserFromOrganization',
  removeUserOrgValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  RemoveUserFromOrganization,
);
router.post(
  '/ListUsersInOrganization',
  listUsersOrgValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  ListUsersInOrganization,
);
router.post(
  '/AssignRoleToUser',
  assignRoletoUserValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin', 'editor']),
  AssignRoleToUser,
);
router.post(
  '/ListProjectsFromOrganization',
  listProjectOrgValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin', 'editor', 'reader']),
  ListProjectsFromOrganization,
);
router.post(
  '/FilterProjectsFromOrganization',
  filterProjectValidate,
  FilterProjectsFromOrganization,
);
router.post(
  '/GetProjectInOrganization',
  getProjectOrgValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin', 'editor', 'reader']),
  GetProjectInOrganization,
);
router.post(
  '/GetUserRole',
  getUserRoleValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin', 'editor']),
  GetUserRole,
);
router.post(
  '/HandleThumbnailOrg',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'thumbnail' }], 'output'),
  HandleThumbnailOrg,
);
router.get('/:unique_org_id/checkUserIDExistence', checkUserExistence);
router.get('/:id', getOrganizationByID);
router.get(
  '/:unique_org_id/getOrganizationByunique_org_id',
  getOrganizationByUniqueOrgid,
);
router.post(
  '/syncExchangeRates',
  verifyGoogleToken,
  exchangeRatesSyncUp,
);
export default router;

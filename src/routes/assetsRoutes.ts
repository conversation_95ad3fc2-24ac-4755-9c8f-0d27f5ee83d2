import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import createAssetValidate from '../controllers/asset/CreateAsset/createAssetValidator';
import createAsset from '../controllers/asset/CreateAsset/index';
import { getListofAssets } from '../controllers/asset/GetAssets';
import getAssetsValidate from '../controllers/asset/GetAssets/getAssetsValidator';
import { DeleteAsset } from '../controllers/asset/DeleteAsset';
import deleteAssetValidate from '../controllers/asset/DeleteAsset/deleteAssetValidator';
import { updateAsset } from '../controllers/asset/UpdateAsset';
import updateAssetValidate from '../controllers/asset/UpdateAsset/UpdateAssetValidator';
import moveAssetValidate from '../controllers/asset/MoveAsset/moveAssetValidator';
import { moveAsset } from '../controllers/asset/MoveAsset';
import searchAssetValidate from '../controllers/asset/SearchAsset/SearchAssetValidate';
import { searchAssets } from '../controllers/asset/SearchAsset';

const router = express.Router();
router.post('/createAsset',
  authMiddleware,
  organizationAccessMiddleware,
  createAssetValidate,
  createAsset);

router.post('/deleteAsset',
  authMiddleware,
  organizationAccessMiddleware,
  deleteAssetValidate,
  DeleteAsset);

router.get('/getAssetsList/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  getAssetsValidate,
  getListofAssets);

router.post('/updateAsset',
  authMiddleware,
  organizationAccessMiddleware,
  updateAssetValidate,
  updateAsset);

router.post('/moveAsset',
  authMiddleware,
  organizationAccessMiddleware,
  moveAssetValidate,
  moveAsset,
);

router.get('/searchAsset/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  searchAssetValidate,
  searchAssets);

export default router;

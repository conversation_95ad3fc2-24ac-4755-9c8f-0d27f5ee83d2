import express  from 'express';
import { CreateBuilding } from '../controllers/building/createBuilding';
import { GetListOfBuildings } from '../controllers/building/getListOfBuilding';
import { GetBuilding } from '../controllers/building/getBuilding';
import { DeleteBuilding } from '../controllers/building/deleteBuilding';
import { updateBuilding } from '../controllers/building/updateBuilding';
import { UpdateFloor } from '../controllers/building/updateFloor';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import createBuildingValidate from '../controllers/building/createBuilding/createBuildingValidator';
import updateBuildingValidate from '../controllers/building/updateBuilding/updateBuildingValidator';
import deleteBuildingValidate from '../controllers/building/deleteBuilding/deleteBuildingValidator';
import updateFloorValidate from '../controllers/building/updateFloor/updateFloorValidator';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { moveToTrash } from '../controllers/building/moveToTrash';
import moveToTrashValidate from '../controllers/building/moveToTrash/moveToTrashValidator';
import { restoreBuilding } from '../controllers/building/restoreBuilding';
import restoreBuildingValidate from '../controllers/building/restoreBuilding/restoreBuildingValidator';
import bulkUpdateFloorsValidate from '../controllers/building/bulkUpdateFloors/bulkUpdateFloorsValidate';
import  bulkUpdateFloors from '../controllers/building/bulkUpdateFloors';
import uploadHandler from '../helpers/uploadHandler';
import deleteFloorValidator from '../controllers/building/deleteFloor/deleteFloorValidator';
import { DeleteFloor } from '../controllers/building/deleteFloor';
import renameFloorValidator from '../controllers/building/renameFloor/renameFloorValidator';
import { RenameFloor } from '../controllers/building/renameFloor';
import addNewFloorValidator from '../controllers/building/addNewFloor/addNewFloorValidate';
import { AddNewFloor } from '../controllers/building/addNewFloor';

const  router = express.Router();

router.post('/createBuilding',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }], 'output'),
  createBuildingValidate,
  CreateBuilding);
router.get('/getListOfBuildings/:project_id', authMiddleware,
  organizationAccessMiddleware,
  GetListOfBuildings);
router.post('/getBuilding/:building_id', authMiddleware,
  organizationAccessMiddleware, GetBuilding);
router.post('/updateBuilding/:building_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }], 'output'),
  updateBuildingValidate,
  updateBuilding);
router.post('/deleteBuilding/:building_id', deleteBuildingValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  DeleteBuilding);
router.post('/updateFloor/:project_id/:building_id/:floor_id',
  updateFloorValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateFloor);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreBuilding/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreBuildingValidate,
  restoreBuilding,
);

router.post('/updateBulkFloor',
  authMiddleware,
  organizationAccessMiddleware,
  bulkUpdateFloorsValidate,
  bulkUpdateFloors,
);

router.post('/deleteFloor',
  authMiddleware,
  organizationAccessMiddleware,
  deleteFloorValidator,
  DeleteFloor,
);
router.post('/renameFloor',
  authMiddleware,
  organizationAccessMiddleware,
  renameFloorValidator,
  RenameFloor,
);
router.post('/addNewFloor',
  authMiddleware,
  organizationAccessMiddleware,
  addNewFloorValidator,
  AddNewFloor,
);

export default router;

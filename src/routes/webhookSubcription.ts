import express from 'express';
import { SetWebhook } from '../controllers/WebhookSubcription/setWebhooks';
import { GetWebhook } from '../controllers/WebhookSubcription/getWebhook';
import createWebhookValidate from '../controllers/WebhookSubcription/createWebhook/createWebhookValidate';
import { CreateWebhook } from '../controllers/WebhookSubcription/createWebhook';
import setWebhookValidate from '../controllers/WebhookSubcription/setWebhooks/setWebhookValidator';
import { DeleteWebhook } from '../controllers/WebhookSubcription/deleteWebhook';
import { CreateEvents } from '../controllers/WebhookSubcription/createEvents';
import createEventValidate from '../controllers/WebhookSubcription/createEvents/createEventsValidator';
import { ExecuteEvents } from '../controllers/WebhookSubcription/executeEvents';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { GetEvents } from '../controllers/WebhookSubcription/getEvents';

const router = express.Router();

router.post(
  '/createWebhook',
  authMiddleware,
  organizationAccessMiddleware,
  createWebhookValidate,
  CreateWebhook,
);

router.post(
  '/setWebhook',
  authMiddleware,
  organizationAccessMiddleware,
  setWebhookValidate,
  SetWebhook,
);

router.get(
  '/getWebhook/:organization_id',
  authMiddleware,
  organizationAccessMiddleware,
  GetWebhook,
);

router.post(
  '/deleteWebhook',
  authMiddleware,
  organizationAccessMiddleware,
  DeleteWebhook,
);

router.post(
  '/createEvent',
  authMiddleware,
  organizationAccessMiddleware,
  createEventValidate,
  CreateEvents,
);

router.post(
  '/executeEvent',
  ExecuteEvents,
);

router.post(
  '/getEvents',
  authMiddleware,
  organizationAccessMiddleware,
  GetEvents,
);

export default router;

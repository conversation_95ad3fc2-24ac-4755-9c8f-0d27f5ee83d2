import { landmarkCategory } from '../../types/projectLandmark';
import { measurementType } from '../../types/unitplan';
import { priceCurrency } from '../../types/units';
import { BuildingType } from '../../types/building';
export const enumsToTranslate = [
  { name: 'BuildingType', enumType: BuildingType },
  { name: 'LandmarkCategory', enumType: landmarkCategory },
  { name: 'MeasurementType', enumType: measurementType },
  { name: 'PriceCurrency', enumType: priceCurrency },
];

import { createClient, RedisClientType } from 'redis';
import logger from './logger';

class RedisConfig {
  private client: RedisClientType | null = null;
  private isConnected: boolean = false;

  constructor () {
    this.initializeRedis();
  }

  private async initializeRedis (): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL;

      this.client = createClient({
        url: redisUrl,
        socket: {
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              logger.error('Redis connection failed after 10 retries');
              return new Error('Redis connection failed');
            }
            return Math.min(retries * 50, 1000);
          },
        },
      });

      this.client.on('error', (err) => {
        logger.error('Redis Client Error:', err);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        logger.info('Redis client connected');
        this.isConnected = true;
      });

      this.client.on('ready', () => {
        logger.info('Redis client ready');
        this.isConnected = true;
      });

      this.client.on('end', () => {
        logger.info('Redis client disconnected');
        this.isConnected = false;
      });

      await this.client.connect();
    } catch (error) {
      logger.error('Failed to initialize Redis:', error);
      this.isConnected = false;
    }
  }

  public getClient (): RedisClientType | null {
    return this.client;
  }

  public isRedisConnected (): boolean {
    return this.isConnected && this.client !== null;
  }

  public async disconnect (): Promise<void> {
    if (this.client) {
      await this.client.disconnect();
      this.isConnected = false;
    }
  }

  // Cache helper methods
  public async set (key: string, value: string, ttlSeconds?: number): Promise<boolean> {
    if (!this.isRedisConnected()) {
      logger.warn('Redis not connected, skipping cache set');
      return false;
    }

    try {
      if (ttlSeconds) {
        await this.client!.setEx(key, ttlSeconds, value);
      } else {
        await this.client!.set(key, value);
      }
      return true;
    } catch (error) {
      logger.error('Redis set error:', error);
      return false;
    }
  }

  public async get (key: string): Promise<string | null> {
    if (!this.isRedisConnected()) {
      logger.warn('Redis not connected, skipping cache get');
      return null;
    }

    try {
      return await this.client!.get(key);
    } catch (error) {
      logger.error('Redis get error:', error);
      return null;
    }
  }

  public async del (key: string): Promise<boolean> {
    if (!this.isRedisConnected()) {
      logger.warn('Redis not connected, skipping cache delete');
      return false;
    }

    try {
      await this.client!.del(key);
      return true;
    } catch (error) {
      logger.error('Redis delete error:', error);
      return false;
    }
  }

  public async exists (key: string): Promise<boolean> {
    if (!this.isRedisConnected()) {
      logger.warn('Redis not connected, skipping cache exists check');
      return false;
    }

    try {
      const result = await this.client!.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis exists error:', error);
      return false;
    }
  }

  public async flushAll (): Promise<boolean> {
    if (!this.isRedisConnected()) {
      logger.warn('Redis not connected, skipping cache flush');
      return false;
    }

    try {
      await this.client!.flushAll();
      return true;
    } catch (error) {
      logger.error('Redis flush error:', error);
      return false;
    }
  }

  // JSON helper methods
  public async setJSON (key: string, value: any, ttlSeconds?: number): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(value);
      return await this.set(key, jsonString, ttlSeconds);
    } catch (error) {
      logger.error('Redis setJSON error:', error);
      return false;
    }
  }

  public async getJSON<T> (key: string): Promise<T | null> {
    try {
      const jsonString = await this.get(key);
      if (jsonString) {
        return JSON.parse(jsonString) as T;
      }
      return null;
    } catch (error) {
      logger.error('Redis getJSON error:', error);
      return null;
    }
  }
}

// Create singleton instance
const redisConfig = new RedisConfig();

export default redisConfig;

import { createLogger, format, transports } from 'winston';
import asyncLocalStorage from './asyncLocalStorage';

const { combine, errors, json, printf} = format;

const textFormat = printf(({ level, message, ...metadata }) => {
  const store = asyncLocalStorage.getStore();
  const apiEndpoint = store?.get('apiEndpoint');
  let msg = `${apiEndpoint ? apiEndpoint : ''} [${level}]: 
  ${typeof message === 'object' ? JSON.stringify(message) : message}`;
  if (metadata) {
    msg += ` ${JSON.stringify(metadata)}`;
  }
  return msg;
});

const customFormat = format((info) => {
  const store = asyncLocalStorage.getStore();
  const apiEndpoint = store?.get('apiEndpoint') || 'no-endpoint';
  const controllerEndpoint = store?.get('controllerEndpoint') || 'no-controllerEndpoint';
  const requestId = store?.get('requestId') || 'no-requestId';

  const logData = {
    requestId: requestId,
    module: apiEndpoint.replace('/', ''),
    controller: controllerEndpoint,
    ...info,
    message: info[Symbol.for('message')],
  };

  const finalLog = {
    ...logData,
  };

  return finalLog;
});

const isProduction = process.env.WINSTON_ENV === 'production';

const logger = isProduction
  ? createLogger({
    level: 'info',
    format: combine(
      errors({ stack: true }),
      textFormat,
      customFormat(),
      json(),
    ),
    transports: [
      new transports.Console({
        handleExceptions: true,
      }),
    ],
  })
  : {
    info: () => undefined,
    error: () => undefined,
    warn: () => undefined,
  };

export default logger;

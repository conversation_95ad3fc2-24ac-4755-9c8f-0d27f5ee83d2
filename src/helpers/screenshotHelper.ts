import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import path from 'path';
import fs from 'fs';
import { storageUpload } from './storageUpload';
import logger from '../config/logger';

let browser: Browser | null = null;

const getBrowser = async (): Promise<Browser> => {
  if (!browser || !browser.isConnected()) {
    if (browser) {
      try {
        await browser.close();
      } catch (error) {
        logger.error('Error closing disconnected browser', { error });
      }
    }

    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--no-first-run',
        '--no-zygote',
      ],
      timeout: 30000,
    });

    browser.on('disconnected', () => {
      logger.warn('<PERSON>rowser disconnected, will recreate on next request');
      browser = null;
    });
  }
  return browser;
};

export async function takeScreenshotAndUpload (
  url: string,
  destinationPath: string,
): Promise<string> {
  let page: Page | null = null;
  let screenshotPath: string | null = null;

  try {
    const browserInstance = await getBrowser();
    page = await browserInstance.newPage();

    await page.setViewport({ width: 1200, height: 630 });

    await page.setCacheEnabled(false);

    logger.info('Navigating to URL for screenshot', { url });
    try {
      await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: 30000,
      });
    } catch (gotoError: any) {
      const errorMessage = gotoError?.message || '';
      if (errorMessage.includes('ERR_NAME_NOT_RESOLVED')) {
        throw new Error(`Cannot resolve domain name. The URL "${url}" is not accessible from the server. Please use a publicly accessible URL.`);
      }
      logger.warn('networkidle0 failed, trying domcontentloaded', { error: gotoError });
      await page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: 30000,
      });
    }

    await page.evaluate(() => {
      return new Promise((resolve) => {
        if (document.readyState === 'complete') {
          setTimeout(resolve, 8000);
        } else {
          window.addEventListener('load', () => setTimeout(resolve, 8000), { once: true });
        }
      });
    });

    await page.evaluate(() => {
      const style = document.createElement('style');
      style.textContent = `
        .hide-in-share, #hide-in-share, #controls {
          display: none !important;
        }
      `;
      document.head.appendChild(style);
    });

    const tempDir = path.join(process.cwd(), 'temp', 'screenshots');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const timestamp = Date.now();
    screenshotPath = path.join(tempDir, `screenshot_${timestamp}.jpeg`);

    if (page.isClosed()) {
      throw new Error('Page was closed before screenshot could be taken');
    }

    await page.screenshot({ path: screenshotPath, type: 'jpeg', quality: 85 });

    if (!page.isClosed()) {
      await page.close().catch((err) => logger.warn('Error closing page', { error: err }));
    }
    page = null;

    const cleanBasePath = destinationPath
      .replace(/^\/+/, '')
      .replace(/\/+$/, '')
      .replace(/\/+/g, '/');

    const destination = `${cleanBasePath}/preview_${timestamp}.jpeg`;

    logger.info('Uploading screenshot to storage', { destination });
    const firebaseUrl = await storageUpload({ destination }, screenshotPath);
    return firebaseUrl;
  } catch (error) {
    logger.error('Error taking screenshot', { error, url });

    if (page && !page.isClosed()) {
      await page.close().catch((err) => logger.warn('Error closing page in cleanup', { error: err }));
    }

    if (error instanceof Error && error.message.includes('Connection closed') && browser) {
      await browser.close().catch((err) => logger.warn('Error closing browser', { error: err }));
      browser = null;
    }

    if (screenshotPath && fs.existsSync(screenshotPath)) {
      await fs.promises.unlink(screenshotPath).catch((err) => logger.warn('Error cleaning up screenshot file', { error: err }));
    }

    throw new Error(`Failed to take screenshot: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

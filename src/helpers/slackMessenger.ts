import axios from 'axios';
export async function sendMessageToChannel (webhookUrl: string, heading: string, msg: string): Promise<boolean> {
  try {
    const result = await axios.post(webhookUrl, {
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*${heading}*`,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*Check it out here!*',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: msg,
          },
        },
      ],
      // Text: heading + msg,
    });

    console.log('Message sent successfully: ', result.data);
    return true;
  } catch (error) {
    console.error('Error sending message:', error);
    return false;
  }
}

import { admin, bucketName} from '../config/firebase';
import fs from 'fs';
import sharp from 'sharp';
import path from 'path';
import { downloadImageAsFile, extractNameFromUrl, removeTimestampFromBaseName } from './downloadFile';
import { UploadUnitplanFiles } from './uploadFirebase';
import logger from '../config/logger';
type UploadOptions ={
  destination:string
}
export async function storageUpload (
  uploadOptions:UploadOptions, filePath:string,
):Promise<string> {
  return new Promise((resolve, reject) => {
    const timestamp = Date.now();
    const destination = uploadOptions.destination;
    const extension = destination.substring(destination.lastIndexOf('.'));

    // Destination + timestamp
    const updatedDestination = destination.substring(0, destination.lastIndexOf('.')) + '_' + timestamp + extension;

    admin.storage().bucket(bucketName)
      .upload(filePath, {...uploadOptions, destination: updatedDestination}, (error, uploadedFile) => {
        if (error || uploadedFile===undefined || uploadedFile === null) {
          reject(error);
          return;
        }
        if (fs.existsSync(filePath)) {
          fs.promises.unlink(filePath).catch(console.error);
        }
        const thumbnailUrl =
          'https://firebasestorage.googleapis.com/v0/b/' +
          bucketName +
          '/o/' +
          encodeURIComponent(
            uploadedFile?uploadedFile.name:'',
          ) +
          '?alt=media';
        resolve(thumbnailUrl);
      });
  });
}

export async function resizeImage (
  file: { [fieldname: string]: Express.Multer.File[] } | Express.Multer.File[] | Express.Multer.File,
  maxWidth: number,
  maxHeight: number,
): Promise<Express.Multer.File> {
  // Extract the actual file from different input types
  let actualFile: Express.Multer.File;

  if (Array.isArray(file)) {
    // If it's an array, take the first file
    actualFile = file[0];
  } else if (file && typeof file === 'object' && 'fieldname' in file) {
    // If it's a single Multer file
    actualFile = file as Express.Multer.File;
  } else if (file && typeof file === 'object' && !Array.isArray(file)) {
    // If it's an object with fieldname keys, get the first file from the first field
    const fieldNames = Object.keys(file);
    if (fieldNames.length > 0) {
      const firstField = file[fieldNames[0]];
      actualFile = firstField[0];
    } else {
      throw new Error('No files found in the provided object');
    }
  } else {
    throw new Error('Invalid file type provided');
  }

  if (!actualFile || !actualFile.path) {
    throw new Error('File path not found');
  }

  // Create resized file path
  const originalPath = actualFile.path;
  const fileExtension = path.extname(originalPath);
  const fileName = path.basename(originalPath, fileExtension);
  const dirName = path.dirname(originalPath);
  const resizedPath = path.join(dirName, `${fileName}_resized${fileExtension}`);

  try {
    // Get image metadata to determine the best resize strategy
    const metadata = await sharp(originalPath).metadata();

    // Calculate new dimensions using the same logic as your original function
    let newWidth = metadata.width || maxWidth;
    let newHeight = metadata.height || maxHeight;

    // Keep halving dimensions until they fit within max dimensions (like your original function)
    while (newWidth > maxWidth || newHeight > maxHeight) {
      newWidth = Math.floor(newWidth / 2);
      newHeight = Math.floor(newHeight / 2);
    }

    // Only resize if dimensions actually changed
    if (newWidth !== metadata.width || newHeight !== metadata.height) {
      // Resize the image using Sharp with calculated dimensions
      await sharp(originalPath)
        .resize(newWidth, newHeight, {
          fit: 'fill', // This ensures the image is resized to exact dimensions
        })
        .jpeg({ quality: 90 })
        .toFile(resizedPath);
    } else {
      // If no resizing needed, copy the original file
      await fs.promises.copyFile(originalPath, resizedPath);
    }

    // Create a new Multer file object for the resized image
    const resizedFile: Express.Multer.File = {
      ...actualFile,
      fieldname: 'unitplan_thumbnail', // Set the fieldname to distinguish from original
      path: resizedPath,
      filename: `${fileName}_resized${fileExtension}`,
      originalname: `${fileName}_resized${fileExtension}`,
      size: fs.statSync(resizedPath).size,
    };

    return resizedFile;

  } catch (sharpError) {
    // If resizing fails, return the original file
    return actualFile;
  }
}

// Function to crop image by height/2 (keep full width, crop height to half)
export async function cropImageToHalf (
  file: Express.Multer.File,
): Promise<Express.Multer.File> {
  if (!file || !file.path) {
    throw new Error('File path not found');
  }

  const originalPath = file.path;
  const fileExtension = path.extname(originalPath);
  const fileName = path.basename(originalPath, fileExtension);
  const dirName = path.dirname(originalPath);
  const croppedPath = path.join(dirName, `${fileName}_cropped${fileExtension}`);

  try {
    const metadata = await sharp(originalPath).metadata();
    const width = metadata.width || 0;
    const height = metadata.height || 0;

    if (width === 0 || height === 0) {
      throw new Error('Invalid image dimensions');
    }

    // Crop by height/2: keep full width, crop from top to half of image size
    const newHeight = Math.floor(height / 2);
    const left = 0;
    const top = 0;
    await sharp(originalPath)
      .extract({
        left,
        top,
        width: width,
        height: newHeight,
      })
      .jpeg({ quality: 90 })
      .toFile(croppedPath);

    const croppedFile: Express.Multer.File = {
      ...file,
      fieldname: file.fieldname,
      path: croppedPath,
      filename: `${fileName}_cropped${fileExtension}`,
      originalname: `${fileName}_cropped${fileExtension}`,
      size: fs.statSync(croppedPath).size,
    };

    return croppedFile;
  } catch (error) {
    console.error('Error cropping image by height/2:', error);
    return file;
  }
}

export async function processStereoImages (
  url: string,
  thumbnail: string,
  storagePath: string,
  imageId: string,
): Promise<{
  finalUrl: string;
  finalThumbnail: string;
  stereoUrl: string;
  stereoThumbnailUrl: string;
}> {
  let finalUrl = url;
  let finalThumbnail = thumbnail;
  let stereoThumbnailUrl = '';
  let stereoUrl = '';

  try {
    console.log('Processing stereo images for:', url, thumbnail);
    // Extract base names from URLs
    const extractedUrlName = extractNameFromUrl(url);
    const extractedThumbnailName = extractNameFromUrl(thumbnail);
    // Remove timestamps if present
    const baseNameFromUrl = removeTimestampFromBaseName(extractedUrlName);
    const baseNameFromThumbnail = removeTimestampFromBaseName(extractedThumbnailName);
    console.log('Base names:', baseNameFromUrl, baseNameFromThumbnail);
    const outputDir = path.join(process.cwd(), 'output');
    const imageSubfolder = path.join(outputDir, imageId);
    if (!fs.existsSync(imageSubfolder)) {
      fs.mkdirSync(imageSubfolder, { recursive: true });
    }

    // Download original images from Firebase URLs
    const downloadedThumbnailFile = await downloadImageAsFile(thumbnail, 'thumbnail', imageSubfolder);
    const downloadedImageFile = await downloadImageAsFile(url, 'url', imageSubfolder);
    if (downloadedThumbnailFile && downloadedImageFile) {
      const thumbnailExt = path.extname(downloadedThumbnailFile.originalname) || '.jpg';
      const imageExt = path.extname(downloadedImageFile.originalname) || '.jpg';

      // Crop thumbnail to half height
      const croppedThumbnail = await cropImageToHalf(downloadedThumbnailFile);
      const originalThumbnailFile = {
        ...downloadedThumbnailFile,
        fieldname: 'stereoThumbnail',
        originalname: `${baseNameFromThumbnail}_stereo_thumbnail${thumbnailExt}`,
        filename: `${baseNameFromThumbnail}_stereo_thumbnail${thumbnailExt}`,
      };
      // Upload original thumbnail as stereoThumbnail
      const originalThumbnailFiles = { 'stereoThumbnail': [originalThumbnailFile] };
      const uploadedStereoThumbnail = await UploadUnitplanFiles(originalThumbnailFiles, storagePath);
      stereoThumbnailUrl = uploadedStereoThumbnail.stereoThumbnail || '';
      const croppedThumbnailWithName = {
        ...croppedThumbnail,
        originalname: `${baseNameFromThumbnail}${thumbnailExt}`,
        filename: `${baseNameFromThumbnail}${thumbnailExt}`,
      };

      // Upload cropped thumbnail as regular thumbnail
      const croppedThumbnailFiles = { 'thumbnail': [croppedThumbnailWithName] };
      const uploadedCroppedThumbnail = await UploadUnitplanFiles(croppedThumbnailFiles, storagePath);
      finalThumbnail = uploadedCroppedThumbnail.thumbnail || '';

      // Crop main image to half height
      const croppedImage = await cropImageToHalf(downloadedImageFile);
      const originalImageFile = {
        ...downloadedImageFile,
        fieldname: 'stereoUrl',
        originalname: `${baseNameFromUrl}_stereo${imageExt}`,
        filename: `${baseNameFromUrl}_stereo${imageExt}`,
      };
      // Upload original image as stereoUrl
      const originalImageFiles = { 'stereoUrl': [originalImageFile] };
      const uploadedStereoImage = await UploadUnitplanFiles(originalImageFiles, storagePath);
      stereoUrl = uploadedStereoImage.stereoUrl || '';
      const croppedImageWithName = {
        ...croppedImage,
        originalname: `${baseNameFromUrl}${imageExt}`,
        filename: `${baseNameFromUrl}${imageExt}`,
      };

      // Upload cropped image as regular url (for tile generation)
      const croppedImageFiles = { 'url': [croppedImageWithName] };
      const uploadedCroppedImage = await UploadUnitplanFiles(croppedImageFiles, storagePath);
      finalUrl = uploadedCroppedImage.url || '';
    }
  } catch (error) {
    logger.error('Error processing stereo images:', error);
  }

  return {
    finalUrl,
    finalThumbnail,
    stereoUrl,
    stereoThumbnailUrl,
  };
}

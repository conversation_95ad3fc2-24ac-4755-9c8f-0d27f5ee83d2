import { sessionSchema } from './../schema/sessionSchema';
import mongoose from 'mongoose';
import moment from 'moment-timezone';
import nodemailer from 'nodemailer';
const SessionModel = mongoose.model('Session', sessionSchema);

export async function generateSessionId (): Promise<string> {
  const alphabet =
    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let sessionId = '';

  for (let i = 0; i < 6; i++) {
    const randomIndex = Math.floor(Math.random() * alphabet.length);
    sessionId += alphabet[randomIndex];
  }

  return sessionId;
}

export async function generateCode (): Promise<string> {
  const num = '0123456789';
  let newCode = '';

  while (newCode.length < 6) {
    const randomIndex = Math.floor(Math.random() * num.length);
    newCode += num[randomIndex];
  }

  // Check if the generated newCode exists in the "sessions" collection
  const sessionsCollection = await SessionModel.findOne({_id: newCode});
  if (sessionsCollection){
    generateCode();
  }
  return newCode;
}

export function adjustForTimezone (date:Date, offset:number):Date{
  const timeOffsetInMS:number = offset * 60000;
  date.setTime(date.getTime() + timeOffsetInMS);
  return date;
}

export function convertToLocalDateTime (isoString: string, timezone: string):
{ localDate: string, localTime: string } {
  const momentObj = moment(isoString).tz(timezone || 'Asia/Kolkata');
  const localDate = momentObj.format('YYYY-MM-DD');
  const localTime = momentObj.format('HH:mmA');
  return { localDate, localTime };
}
export async function sendEmail (body:object): Promise<nodemailer.SentMessageInfo>  {
  // Configure SMTP transporter
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST as string,  // Replace with your SMTP host
    port: 587,
    secure: false,              // True for 465, false for other ports
    auth: {
      user: process.env.SMTP_USERNAME as string, // Replace with SMTP username
      pass: process.env.SMTP_PASSWORD as string,          // Replace with SMTP password
    },
    requireTLS: true,             // Enforce TLS as the security protocol
  });

  // Define email parameters
  const info = await transporter.sendMail(body);

  // Log the result
  console.log('Message sent: %s', info.messageId);
  return info;
}

import mongoose, { Types } from 'mongoose';
// Const classList:{[key:string]:string} = {
//   'project': 'areaSVGCls',
//   'landmarks': 'landmarkSvgCls',
//   'location': 'locationSvgCls',
//   'media': 'mediaSvgCls',
//   'route': 'routeSvgCls',
//   'radius': 'radiusSvgCls',
//   'VRTour': 'vrTourSVgCls',
//   'labels': 'labelsSvgCls',
//   'inventory': 'InventoryCls',
//   'amenities': '',

// };
function decodeIllustratorId (id:string) {
  // Only decode up to the first non-pattern part (i.e., stop after name portion)
  // Regex to decode all _xNN_ sequences like _x20_ for space, _x2D_ for dash, etc.
  return id.replace(/_x([0-9A-Fa-f]{2})_/g, (_, hex) => {
    return String.fromCharCode(parseInt(hex, 16));
  });
}

function decodeCleanIllustratorId (id:string) {
  const decoded = decodeIllustratorId(id);
  // Optional: Cut off extra hash if it starts after digits/underscore
  const cleaned =  decoded.replace(/(_\d{20,}_?)$/, '');
  console.log('cleaned', cleaned);
  return cleaned.replace(/_/g, ' '); // Removed '_'
}

export async function svgManipulationHelper (
  svg:string, type:string, svgrandomId:Types.ObjectId,
)
  :Promise<{[key:string]:string|object}> {
  let idCounter=0;
  const layers:{[key:string]:object}={};
  let depth = 0;
  let width, height;
  const svgWithoutComments = svg.replace(/<!--(.*?)-->/g, '');
  const viewBoxMatch = svg.match(/viewBox="[\d\s]+"/);
  if (viewBoxMatch) {
    const viewBoxValues = viewBoxMatch[0].match(/[\d.]+/g);
    if (viewBoxValues && viewBoxValues.length === 4) {
      width = parseFloat(viewBoxValues[2]);
      height = parseFloat(viewBoxValues[3]);
    }
  }
  const classAddedSVG=svgWithoutComments.replace(/<g(\s+id="[^"]*")?|<\/g>/g, (match) => {
    if (match.startsWith('<g')) {
      if (depth === 0) {
        const originalIdMatch = match.match(/id="([^"]+)"/);
        const name = originalIdMatch ? decodeCleanIllustratorId(originalIdMatch[1]) : '';
        const id = `${new mongoose.Types.ObjectId()}${idCounter++}`;
        layers[id]={ layer_id: id, name: name };
        match = `<g id="${id}" class="svg_${svgrandomId}"`;
      }
      depth++;
    } else if (match === '</g>') {
      depth--;
    }
    return match;
  });
  const finalSvg = classAddedSVG.replace(/<svg\b/, '<svg preserveAspectRatio="xMidYMid slice"');
  const customClass = '.svg_'+svgrandomId+' ';
  const updatedSvgString = finalSvg.replace(/\.(\w+)\s*\{/g, customClass + '.$1 {');
  const data={modifiedSvg: updatedSvgString, layers: layers, viewbox: {
    width: width,
    height: height,
  }};
  return (data);
}

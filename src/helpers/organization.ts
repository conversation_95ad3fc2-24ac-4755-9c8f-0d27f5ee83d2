import { migratedOrganizationRecords, Organization, organizationSettingsInput } from '../types/organization';
export async function generateOrganizationId (): Promise<string> {
  const alphabet =
    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let organizationId = '';

  for (let i = 0; i < 6; i++) {
    const randomIndex = Math.floor(Math.random() * alphabet.length);
    organizationId += alphabet[randomIndex];
  }

  return organizationId;
}

/**
 * Converts old organization schema to new organization schema format.
 * @param {Object} oldOrg - Document with the old schema structure (the one containing "data" field)
 * @returns {Object} - Transformed document following the new schema
 */
export function migrateOrgSchema (oldOrg:migratedOrganizationRecords): Organization {
  if (!oldOrg) {
    throw new Error('Invalid old organization format — missing \'data\' field.');
  }

  const d: any = (oldOrg as any)?._doc ?? oldOrg;
  console.log('d keys:', Object.keys(d));

  return {
    _id: d._id,
    name: d.name,
    founding_date: d.founding_date,
    contact_email: d.contact_email,
    phone_number: d.phone_number,
    thumbnail: d.thumbnail,
    address: d.address,
    website: d.website,
    max_users: d.max_users,
    roles: d.roles,
    measurement_id: d.measurement_id || '',
    unique_org_id: d.unique_org_id,

    organizationSettings: {
      pixelstreaming: {
        lark_groupid: d.lark_groupid,
        max_concurrent_sessions: d.max_concurrent_sessions,
        duration: d.duration,
      },
      salestool: {
        slots: d.slots,
        timezone: d.timezone,
      },
      currency: {
        baseCurrency: d.baseCurrency,
        exchangeRatio: d.exchangeRatio,
        currency_provider: d.currency_provider,
        currency_provider_name: d.currency_provider_name,
      },
      theme: {
        theme: d.theme,
        primary: d.primary,
        primary_text: d.primary_text,
        secondary: d.secondary,
        secondary_text: d.secondary_text,
        font_type: d.font_type,
        font_url: d.font_url,
      },
      weblite: {
        mastersvg_visibility: d.mastersvg_visibility || false,
        share_masterscenes: d.share_masterscenes || false,
        is_broadcast_enabled: d.is_broadcast_enabled || false,
        org_logo_click_type: d.org_logo_click_type || 'default',
        org_logo_click_link: d.org_logo_click_link || '',
      },
    },
  } as Organization;
}

// Used in Controller
/**
 * Type guard to check if an organization object has the new schema format
 * @param {Object} org - Organization object to check
 * @returns {boolean} - True if it has organizationSettings (new schema), false otherwise
 */
export function isOrganization (
  org: Organization | migratedOrganizationRecords | null | undefined,
): org is Organization {
  if (!org || typeof org !== 'object') {
    return false;
  }

  // Ensure the property exists before accessing it to satisfy TypeScript
  if (!Object.prototype.hasOwnProperty.call(org, 'organizationSettings')) {
    return false;
  }

  const settings = (org as any).organizationSettings;
  return settings !== null;
}

export function transformUpdatePayloadForOldSchema (updateData: organizationSettingsInput): any {
  const result:Record<string, any> = {};

  for (const [key, value] of Object.entries(updateData)) {
    // Handle nested group objects
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      // Flatten: theme: { font_type: "X" } => font_type: "X"
      for (const [nestedKey, nestedValue] of Object.entries(value)) {
        result[nestedKey] = nestedValue;
      }
    } else {
      // Top-level fields remain as-is
      result[key] = value;
    }
  }

  return result;
}

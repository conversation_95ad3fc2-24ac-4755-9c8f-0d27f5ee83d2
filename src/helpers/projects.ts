import { FontType } from '../types/projects';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import os from 'os';
import { storageUpload } from './storageUpload';

interface GoogleFontsResponse {
  items: Array<{
    family: string;
    variants: string[];
  }>;
}

async function getGoogleFontsList (): Promise<string[]> {
  const apiKey = process.env.FONTS_API_KEY ;
  const url = `https://www.googleapis.com/webfonts/v1/webfonts?key=${apiKey}`;

  try {
    const response = await axios.get<GoogleFontsResponse>(url);

    if (response.status !== 200) {
      throw new Error('Error fetching Google Fonts');
    }

    const fontFamily = response.data.items
      .filter((item) => item.variants && item.variants.length >= 1)
      .map((item) => item.family);

    return fontFamily;
  } catch (error) {
    console.error('Error fetching Google Fonts:', error);
    throw error;
  }
}

export async function generateProjectId (): Promise<string> {
  const alphabet =
      '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let organizationId = 'P';

  for (let i = 0; i < 5; i++) {
    const randomIndex = Math.floor(Math.random() * alphabet.length);
    organizationId += alphabet[randomIndex];
  }

  return organizationId;
}

export async function generateFontLink (fontName: FontType): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      if (typeof fontName !== 'string' || !fontName.trim()) {
        reject('Invalid type: fontName must be a non-empty string');
      }

      const formattedFontName = fontName
        .split(' ')
        .map((word) => {
          // If the word is all uppercase (like "SC"), preserve it as is
          if (word === word.toUpperCase()) {
            return word;
          }
          // Otherwise, convert to title case (first letter uppercase, rest lowercase)
          return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        })
        .join('+');
      const fontLink = 'https://fonts.googleapis.com/css2?family='+
      formattedFontName
      +':ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap'.trim();
      resolve(fontLink);
    } catch (error) {
      reject(error);
    }
  });
}
function extractPrettyFontName (url: string): string {
  // Decode URL so %2F becomes /
  const decodedUrl = decodeURIComponent(url);

  // Get the last part after the last slash
  const filename = decodedUrl.split('/').pop()?.split('?')[0] || '';

  // Remove extension
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');

  const baseName = nameWithoutExt.split(/[-_]/)[0];

  // Insert a space before each capital letter (except the first)
  const prettyName = baseName.replace(/([a-z])([A-Z])/g, '$1 $2');

  return prettyName;
}

async function generateAndUploadFontCSS (fontFileUrl: string, name :string): Promise<string> {
  const cssContent = `@font-face {
  font-family: 'custom';
  src: url('${fontFileUrl}') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}`;

  // The CSS file will contain @font-face that references the .ttf file
  const cssFileName = `${name}.css`;
  const tempFilePath = path.join(os.tmpdir(), cssFileName);

  // Write CSS to file
  await fs.promises.writeFile(tempFilePath, cssContent, 'utf8');
  const destination = `fonts/css/${cssFileName}`;
  const cssUrl = await storageUpload({ destination }, tempFilePath);

  // Clean up temp file
  try {
    if (fs.existsSync(tempFilePath)) {
      await fs.promises.unlink(tempFilePath);
    }
  } catch (cleanupError) {
    console.warn('Failed to cleanup temp CSS file:', cleanupError);
  }

  return cssUrl;
}

export async function generateCustomFont (link: string): Promise<string> {
  // Validate the link is a non-empty string
  if (typeof link !== 'string' || !link.trim()) {
    throw new Error('Invalid type: fontName must be a non-empty string');
  }
  const trimmedLink = link.trim();

  // Extract font name from the URL
  const extractedFontName = extractPrettyFontName(trimmedLink);

  try {
    const googleFontsList = await getGoogleFontsList();
    const normalizedExtractedName = extractedFontName.trim().toLowerCase();

    // Check if the font exists in Google Fonts
    const fontExists = googleFontsList.some(
      (font) => font.trim().toLowerCase() === normalizedExtractedName,
    );

    if (fontExists) {
      const fontLink = 'https://fonts.googleapis.com/css2?family='+
      extractedFontName
      +':ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap'.trim();
      return fontLink;
    }
    const cssUrl = await generateAndUploadFontCSS(trimmedLink, extractedFontName);
    return cssUrl;

  } catch (error) {
    console.error('Error generating fonturl:', error);
    throw new Error('Failed to generate fonturl');
  }

}

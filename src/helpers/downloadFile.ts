
import fs from 'fs';
import axios from 'axios';
import path from 'path';

interface chunk{
    length:number
}

export async function downloadFile (fileUrl:string, outputLocationPath:string):Promise<string> {
  const writer = fs.createWriteStream(outputLocationPath);

  try {
    // Get the content length from the headers
    const headResponse = await axios.head(fileUrl);
    const totalLength = parseInt(headResponse.headers['content-length'], 10);

    const response = await axios({
      url: fileUrl,
      method: 'GET',
      responseType: 'stream',
    });

    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      let downloadedLength = 0;

      response.data.on('data', (chunk:chunk) => {
        downloadedLength += chunk.length;
      });

      writer.on('finish', () => {
        if (downloadedLength === totalLength) {
          console.log(outputLocationPath);
          resolve(outputLocationPath);
        } else {
          reject(new Error('Downloaded file size does not match expected size.'));
        }
      });

      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`Error downloading the file: ${error}`);
    throw error; // Rethrow the error to ensure the promise is rejected
  }
}

// Helper function to extract name from Firebase URL (removes timestamp)
export function extractNameFromUrl (url: string): string {
  try {
    const decodedUrl = decodeURIComponent(url);
    const urlPath = new URL(decodedUrl).pathname;
    const filename = path.basename(urlPath);

    // Remove file extension
    const nameWithoutExt = path.parse(filename).name;

    // Remove timestamp pattern (digits followed by hyphen at the beginning)
    const nameWithoutTimestamp = nameWithoutExt.replace(/^\d+-/, '');

    // Decode any remaining URL encoded characters in the name itself
    const fullyDecodedName = decodeURIComponent(nameWithoutTimestamp);

    // Replace spaces with underscores
    const nameWithUnderscores = fullyDecodedName.replace(/\s+/g, '_');

    return nameWithUnderscores || 'image';
  } catch (error) {
    console.error('Error extracting name from URL:', error);
    return 'image';
  }
}
// Helper function to remove timestamp from base name if present
export function removeTimestampFromBaseName (baseName: string): string {
  try {
    let nameWithoutTimestamp = baseName.replace(/_\d{10}$/, '');
    nameWithoutTimestamp = nameWithoutTimestamp.replace(/_\d{13}$/, '');
    nameWithoutTimestamp = nameWithoutTimestamp.replace(/\d{10}$/, '');
    nameWithoutTimestamp = nameWithoutTimestamp.replace(/\d{13}$/, '');

    return nameWithoutTimestamp || 'image';
  } catch (error) {
    console.error('Error removing timestamp from base name:', error);
    return baseName || 'image';
  }
}
// Helper function to download image from URL and create Multer file object
export async function downloadImageAsFile (imageUrl: string | undefined, fieldName: string, customTempDir?: string): Promise<Express.Multer.File | null> {
  if (!imageUrl) {
    console.log('No URL provided');
    return null;
  }

  try {
    // Extract name from URL to use as fieldname
    const extractedName = extractNameFromUrl(imageUrl);
    const actualFieldName = extractedName;

    // Download the image
    const response = await axios({
      method: 'GET',
      url: imageUrl,
      responseType: 'stream',
      timeout: 30000, // 30 second timeout
    });
    console.log('url', imageUrl);
    console.log('extracted name:', extractedName);

    // Use the custom directory or default to output directory
    const tempDir = customTempDir || path.join(process.cwd(), 'output');

    const urlPath = new URL(imageUrl).pathname;
    const extension = path.extname(urlPath) || '.jpg';

    const originalName = `${actualFieldName}${fieldName === 'thumbnail'?'_thumb_':''}${extension}`;
    const tempFilePath = path.join(tempDir, originalName);

    // Save image to temporary file
    const writer = fs.createWriteStream(tempFilePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', async () => {
        try {
          // Verify file exists and get stats
          if (!fs.existsSync(tempFilePath)) {
            throw new Error(`File was not created: ${tempFilePath}`);
          }

          const stats = await fs.promises.stat(tempFilePath);
          console.log('originalName', originalName);
          // Create Multer file object
          const multerFile: Express.Multer.File = {
            fieldname: fieldName,
            originalname: originalName,
            encoding: '7bit',
            mimetype: response.headers['content-type'] || 'image/jpeg',
            size: stats.size,
            destination: tempDir,
            filename: originalName,
            path: tempFilePath,
            buffer: Buffer.alloc(0),
            stream: fs.createReadStream(tempFilePath),
          };

          resolve(multerFile);
        } catch (error) {
          console.error('Error creating Multer file object:', error);
          reject(error);
        }
      });

      writer.on('error', (error) => {
        console.error('Error writing temporary file:', error);
        reject(error);
      });
    });
  } catch (error) {
    console.error(`Error downloading image from ${imageUrl}:`, error);
    throw new Error(`Failed to download image from ${imageUrl}: ${(error as Error).message}`);
  }
}

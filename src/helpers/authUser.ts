import admin from 'firebase-admin';

export async function getUserByEmail (
  email: string,
): Promise<admin.auth.UserRecord | null> {
  try {
    const user = await admin.auth().getUserByEmail(email);
    return user;
  } catch (error) {
    // If (error.code === "auth/user-not-found") {
    //   // User with the specified email not found
    //   Return null;
    // }
    return null;
    console.error('Error getting user by email:', error);
    throw error;
  }
}

export async function getUser (
  uid: string,
): Promise<admin.auth.UserRecord | null> {
  try {
    const user = await admin.auth().getUser(uid);
    return user;
  } catch (error) {
    //   If (error.code === "auth/user-not-found") {
    //     // User with the specified email not found
    //     Return null;
    //   }
    return null;

    console.error('Error getting user by email:', error);
    throw error;
  }
}
export async function singlesigningOn (email:string, organization:string, domain:string):Promise<string> {
  return new Promise<string>((resolve, reject) => {
    const actionCodeSettings = {
      url: domain+'/sso?organization_id='+organization+'&email='+email,
      handleCodeInApp: true,
    };
    admin.auth().generateSignInWithEmailLink(email, actionCodeSettings).then((link) => {
      resolve(link);
    })
      .catch(() => {
        reject('An error occurred while generating the sign in link.');
      });
  });
}

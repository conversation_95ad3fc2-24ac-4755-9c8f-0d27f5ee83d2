import axios from 'axios';
import https from 'https';
export async function GetRequest (url:string): Promise<object | void> {
  return new Promise((resolve, reject) => {
    const config = {
      httpsAgent: new https.Agent({
        rejectUnauthorized: false,
      }),
      method: 'get',
      url: url,
    };
    axios(config)
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

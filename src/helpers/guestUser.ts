import { v4 as uuidv4 } from 'uuid';
import logger from '../config/logger';

/**
 * Generate a broadcast host user ID from a provided name
 *
 * Used for creating broadcast sessions where the HOST is not authenticated.
 * This creates a temporary user ID for the HOST (not the guests who join later).
 *
 * Flow:
 * 1. HOST creates broadcast session without auth → gets broadcast_host_id
 * 2. HOST shares invite link with GUESTS
 * 3. GUESTS join via CreateLead API → leads are associated with broadcast_host_id
 *
 * @param name - The display name of the broadcast host
 * @returns A unique broadcast host user ID in format: guest_<uuid>
 */
export function generateGuestUserId (name: string): string {
  // Create a consistent but unique ID format for broadcast hosts
  // Format: guest_<uuid>
  const broadcastHostId = `guest_${uuidv4()}`;
  logger.info('Generated broadcast host user ID', { name, broadcastHostId });
  return broadcastHostId;
}

/**
 * Check if a user ID is a broadcast host (guest user)
 * @param userId - The user ID to check
 * @returns True if the user is a broadcast host/guest user
 */
export function isGuestUser (userId: string): boolean {
  return userId.startsWith('guest_');
}

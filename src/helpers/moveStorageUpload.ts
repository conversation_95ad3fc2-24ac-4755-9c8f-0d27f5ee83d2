import { admin, bucketName} from '../config/firebase';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import os from 'os';
import * as mime from 'mime-types';
function extractNameFromUrl (url: string): string {
  try {
    const urlObj = new URL(url);

    // For Firebase Storage URLs
    if (url.includes('firebasestorage.googleapis.com')) {
      const match = url.match(/\/o\/(.+?)(\?|$)/);
      if (match) {
        const filename = decodeURIComponent(match[1]);
        const lastSegment = filename.substring(filename.lastIndexOf('/') + 1);
        return lastSegment;
      }
    }

    const pathname = decodeURIComponent(urlObj.pathname);
    const filename = pathname.substring(pathname.lastIndexOf('/') + 1);
    return filename || `file_${Date.now()}`;
  } catch (error) {
    console.error('Error extracting filename from URL:', error);
    return `file_${Date.now()}`;
  }
}

export async function CopyFirebaseItem (
  sourceUrl: string,
  destinationPath: string,
): Promise<string> {
  const bucket = admin.storage().bucket(bucketName);
  // Download the file first to get content type
  const response = await axios({
    url: sourceUrl,
    method: 'GET',
    responseType: 'stream',
  });

  // Get content type from response
  const contentType = response.headers['content-type'] || 'application/octet-stream';

  // Get extension from content type
  const extension = mime.extension(contentType);

  // Extract base filename
  let fileName = extractNameFromUrl(sourceUrl);

  // Add extension if not present
  if (!fileName.includes('.') && extension) {
    fileName = `${fileName}.${extension}`;
  }

  const firebaseFilePath = path.posix.join(destinationPath, fileName);

  const tempFilePath = path.join(os.tmpdir(), fileName);

  // Save the file locally
  const writer = fs.createWriteStream(tempFilePath);
  response.data.pipe(writer);

  await new Promise<void>((resolve, reject) => {
    writer.on('finish', resolve);
    writer.on('error', reject);
  });

  // Upload the file to Firebase Storage
  await bucket.upload(tempFilePath, {
    destination: firebaseFilePath,
    metadata: {
      contentType: contentType,
    },
  });

  // Clean up temp file
  fs.unlinkSync(tempFilePath);

  const downloadUrl =
    `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodeURIComponent(firebaseFilePath)}?alt=media`;

  return downloadUrl;
}

import { admin, bucketName } from '../config/firebase';
import fs from 'fs';
import admZip from 'adm-zip';
import path from 'path';
import multer from 'multer';
import { Request, Response, NextFunction } from 'express';

export async function UploadUnitplanFiles (
  requestFiles:
    | { [fieldname: string]: Express.Multer.File[] }
    | Express.Multer.File[] | Express.Multer.File,
  Destination: string,
): Promise<{ [key: string]: string }> {
  const urlObject: { [key: string]: string } = {};

  return new Promise<{ [key: string]: string }>((resolve, reject) => {
    try {
      const uploadPromises = Object.values(requestFiles).map(
        async (files: Express.Multer.File[]) => {
          for (const file of files) {
            const uploadOptions = {
              destination: Destination + '/' + file.originalname,
            };
            if (file.mimetype === 'application/zip'
                  || file.mimetype === 'application/x-rar-compressed'
                  || file.mimetype === 'application/x-zip-compressed') {
              const zip = new admZip(file.path);
              const zipEntries = zip.getEntries();

              for (const zipEntry of zipEntries) {
                if (!zipEntry.isDirectory) {
                  const extractPath = file.fieldname+'/'+zipEntry.entryName;
                  zip.extractEntryTo(zipEntry.entryName, extractPath, false, true);
                  const thumbnailUrl = 'https://firebasestorage.googleapis.com/v0/b/' +
                    bucketName + '/o/' +Destination+'/'+
                    encodeURIComponent(file.fieldname) +
                    '?alt=media';
                  urlObject[file.fieldname] = thumbnailUrl;
                  // Upload the extracted file to Firebase Storage
                  await admin.storage()
                    .bucket(bucketName)
                    .upload(
                      extractPath+'/'+zipEntry.entryName,
                      { destination: Destination + '/gsplat/'+ zipEntry.entryName });
                }
              }
              // Delete only the specific extracted files that were just uploaded
              // This prevents race conditions when multiple zip files are processed concurrently
              try {
                for (const zipEntry of zipEntries) {
                  if (!zipEntry.isDirectory) {
                    const extractPath = file.fieldname+'/'+zipEntry.entryName;
                    const fullPath = extractPath+'/'+zipEntry.entryName;
                    if (fs.existsSync(fullPath)) {
                      await fs.promises.unlink(fullPath);
                    }
                  }
                }
                // Delete the original zip file
                if (fs.existsSync(file.path)) {
                  await fs.promises.unlink(file.path);
                }
              } catch (error) {
                console.log('Error deleting specific gsplat files:', error);
              }
            } else {

              const timestamp = Date.now();
              const destination = uploadOptions.destination;
              const extension = destination.substring(destination.lastIndexOf('.'));

              // Destination + timestamp
              const updatedDestination = destination.substring(0, destination.lastIndexOf('.')) + '_'
              + timestamp + extension;

              const uploadedFile = await admin.storage()
                .bucket(bucketName)
                .upload(file.path, {...uploadOptions, destination: updatedDestination});
              // Delete only the individual file, not the entire directory
              // This prevents race conditions when multiple files are uploaded concurrently
              try {
                if (fs.existsSync(file.path)) {
                  await fs.promises.unlink(file.path);
                }
              } catch (error) {
                console.log('Error deleting individual file:', error);
              }
              const thumbnailUrl =
                  'https://firebasestorage.googleapis.com/v0/b/' +
                  bucketName +
                  '/o/' +
                  encodeURIComponent(uploadedFile[0].name) +
                  '?alt=media';
              urlObject[file.fieldname] = thumbnailUrl;
            }
          }
        },
      );

      Promise.all(uploadPromises).then(async () => {
        resolve(urlObject);
      }).catch((error) => {
        reject(error);
      });
    } catch (error) {
      reject(error);
    }
  });
}

export function UploadProfilePicture (req: Request, res: Response, next: NextFunction): void {
  const uploadDir = path.join(__dirname, 'uploads');

  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir);
  }

  const upload = multer({
    dest: uploadDir,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    fileFilter: (_req, file, cb) => {
      const fileTypes = /jpeg|jpg|png|gif/;
      const extname = fileTypes.test(path.extname(file.originalname).toLowerCase());
      const mimeType = fileTypes.test(file.mimetype);

      if (extname && mimeType) {
        return cb(null, true);
      }
      return cb(new Error('Only images are allowed'));
    },
  }).single('profilePicture');

  upload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ status: 0, error: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ status: 0, error: 'No file uploaded' });
    }

    try {
      const file = req.file;
      const timestamp = Date.now();
      const extname = path.extname(file.originalname);
      const newFileName = `profile_${timestamp}${extname}`;
      const destinationPath = `profile-pictures/${newFileName}`;

      // Upload file to Firebase Storage
      await admin.storage().bucket(bucketName).upload(file.path, {
        destination: destinationPath,
      });

      // Generate public URL
      const fileUrl = `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodeURIComponent(destinationPath)}?alt=media`;

      // Attach file URL to request body
      req.body.profilePicture = fileUrl;

      // Remove local file after upload
      await fs.promises.unlink(file.path);

      return next(); // Move to the next middleware
    } catch (uploadError) {
      return res.status(500).json({ status: 0, error: 'File upload failed' });
    }
  });
}

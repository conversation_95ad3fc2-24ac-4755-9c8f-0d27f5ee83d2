import axios from 'axios';
import { admin, bucketName } from '../config/firebase';
export async function fetchAndUploadImage
(imageUrl: string, destinationFilename: string, filePath: string)
: Promise<string>{
  try {
    // Fetch the image
    const response = await axios({
      url: imageUrl,
      responseType: 'stream',
    });

    // Create a unique filename
    const destination = filePath+ destinationFilename + '.jpeg';

    // Upload the image to Firebase Storage
    const file = admin.storage().bucket(bucketName).file(destination);
    const stream = file.createWriteStream({
      metadata: {
        contentType: response.headers['content-type'],
      },
    });

    response.data.pipe(stream);

    return new Promise((resolve, reject) => {
      stream.on('finish', async () => {
        // Get download URL
        const downloadUrl =
        `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodeURIComponent(destination)}?alt=media`;
        resolve(downloadUrl);
      });

      stream.on('error', (err: string) => {
        reject(err);
      });
    });
  } catch (error) {
    console.error('Error fetching or uploading image:', error);
    throw error;
  }
}

import  {TranslationServiceClient} from '@google-cloud/translate';
import { TransaltionResult, SupportedLanguages } from '../types/translation';
// Instantiates a client
const translationClient = new TranslationServiceClient({
  credentials: {
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    private_key: (process.env.FIREBASE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
  },
});
console.log({
  credentials: {
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    private_key: (process.env.FIREBASE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
  },
});
export async function translateText
(sourceLang:SupportedLanguages, targetLang:SupportedLanguages, text:string | Array<string>)
  :Promise<TransaltionResult>  {
  const request = {
    parent: `projects/${process.env.FIREBASE_PROJECT_ID}/locations/${'us-central1'}`,
    contents: typeof text==='string'?[text]:text,
    mimeType: 'text/plain', // Mime types: text/plain, text/html
    sourceLanguageCode: sourceLang,
    targetLanguageCode: targetLang,
  };
    // AIzaSyBso7DjwuLXXIQ8fF8beOUoIiccoOe76Hc
  // Run request
  const [response] = await translationClient.translateText(request);
  if (response && response.translations){
    if (response.translations.length === 1){
      return (response.translations[0].translatedText as string);
    }

    return (Object.values(response.translations).map((translated) => {
      return translated.translatedText;
    }) as Array<string>);
  }
  return text;
  // Construct request

}

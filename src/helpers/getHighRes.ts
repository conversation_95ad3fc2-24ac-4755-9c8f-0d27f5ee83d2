// Import { response } from 'express';
// Import fs from 'fs';
// Import path from 'path';
// Import { admin, bucketName } from '../config/firebase';
// Import { background } from '../types/projectScene';

// Async function uploadFolder(
//   FolderPath: string,
//   DestinationRoot: string,
//   FileName: string
// ): Promise<string | void> {
//   Const files = fs.readdirSync(folderPath);

//   Console.log('files', files);

//   Let output = '';
//   Const fullPath = path.join(folderPath, fileName);
//   Console.log('1', fullPath);
//   Const storagePath = path.join(destinationRoot, fullPath).replace(/\\/g, '/');
//   Console.log('2', storagePath);
//   Await admin
//     .storage()
//     .bucket(bucketName)
//     .upload(fullPath, {
//       Destination: storagePath,
//     })
//     .then((res) => {
//       Output = res[0].metadata.mediaLink;
//       // Console.log('output', output);
//     });
//   // Console.log('output2', output);
//   Return output;
// }

// Export async function getHighRes(
//   StoragePath: string,
//   ImageFile:
//     | { [fieldname: string]: Express.Multer.File[] }
//     | Express.Multer.File[]
//     | Express.Multer.File
// ): Promise<background> {
//   Let backgroundfiles = {} as background;
//   Let fileName = '';
//   Try {
//     Console.log('inside');

//     Object.values(ImageFile).map(async (files: Express.Multer.File[]) => {
//       For (const file of files) {
//         FileName = file.filename;
//       }
//     });
//     Const path_to_img = './masterScenes';
//     Const ImageUrl = await uploadFolder(path_to_img, storagePath, fileName);
//     Console.log('typeOF ImageURL', typeof ImageUrl);
//     Console.log('ImageURL', ImageUrl);

//     Const formdata = new FormData();

//     Formdata.append('path', 'openseadragon/test8');
//     Formdata.append('file_url', ImageUrl as string);

//     Const requestOptions = {
//       Method: 'POST',
//       Body: formdata,
//       Redirect: 'follow' as RequestRedirect,
//     };

//     Try {
//       Console.log('Fetching from API');
//       Const fetchResponse = await fetch(
//         'http://localhost:5006/api/openseadragon',
//         RequestOptions
//       );

//       If (fetchResponse.ok) {
//         Const responseData = await fetchResponse.json();
//         Console.log('Response Data:', responseData);

//         If (responseData.status === 200) {
//           Backgroundfiles = {
//             Low_resolution: ImageUrl as string,
//             High_resolution: responseData.firebase_Link as string,
//           };
//           Console.log('Files generated successfully', backgroundfiles);
//         } else {
//           Throw new Error(responseData.message);
//           Console.log(responseData);
//         }
//         // console.log(
//         //   'Error in Openseadragon API response:',
//         //   responseData.message
//         // );
//         // Throw new Error(responseData.message);
//       } else {
//         // Throw new Error(`Fetch error: ${fetchResponse.statusText}`);
//         Console.log('Fetch error', fetchResponse.statusText);
//       }
//     } catch (fetchError) {
//       Console.error('Error in fetch:', fetchError);
//       // Throw new Error('Error receiving Highres Image from API');
//     }
//   } catch (error) {
//     Response.status(400).send({ message: 'Error in getHighResolution' });
//   }
//   Return backgroundfiles;
// }

import fs from 'fs';
import path from 'path';
import axios from 'axios';
import FormData from 'form-data';
import { admin, bucketName } from '../config/firebase';
import { background } from '../types/projectScene';
import logger from '../config/logger';
import { UploadUnitplanFiles } from './uploadFirebase';

async function uploadFolder (
  folderPath: string,
  destinationRoot: string,
  fileName: string,
): Promise<string> {
  logger.info('uploadFolder Function Called');
  // Const files = fs.readdirSync(folderPath);
  // Console.log('files', files);

  let output = '';
  const fullPath = path.join(folderPath, fileName);
  // Console.log('1', fullPath);
  const storagePath = path.join(destinationRoot, fileName).replace(/\\/g, '/'); // Fixed
  // Console.log('2', storagePath);

  try {
    const [file] = await admin.storage().bucket(bucketName).upload(fullPath, {
      destination: storagePath,
    });

    output = file.metadata.mediaLink as string;
    logger.info('uploadFolder Function Successfull');
    // Console.log('output', output);
  } catch (error) {
    console.error('Error uploading to Firebase Storage:', error);
  }
  return output;
}
const downloadImage = async (url: string, filePath: string) => {
  console.log('DownloadImage Called');

  const writer = fs.createWriteStream(filePath);

  const response = await axios({
    url,
    method: 'GET',
    responseType: 'stream',
  });

  response.data.pipe(writer);

  return new Promise((resolve, reject) => {
    writer.on('finish', () => resolve(true));
    writer.on('error', (err) => reject(err));
  });
};

export async function getHighRes (
  storagePath: string,
  ImageFile:
    | { [fieldname: string]: Express.Multer.File[] }
    | Express.Multer.File[]
    | Express.Multer.File,
  file_url : string,
): Promise<background | null> {
  logger.info('getHighRes Called');

  let backgroundfiles: background = {
    low_resolution: '',
    high_resolution: '',
  };

  try {
    // If File_url is given
    if (file_url){
      const localImagePath = 'masterScenes/downloaded_image.jpg';
      logger.info('File_url Function Called');
      logger.info('StoragePath: ', {message: storagePath});
      logger.info('file_url: ', {message: file_url});

      try {
        await downloadImage(file_url, localImagePath);
        console.log('DownloadImage Successfull');
      } catch (error) {
        // Response.send({status:0, message: "Error in downloadImage" });
        throw new Error('Error in downloadImage');
      }
      const path_to_img = './masterScenes';
      const mediaLink: string = await uploadFolder(
        path_to_img,
        storagePath,
        'downloaded_image.jpg',
      );
      console.log('mediaLink', mediaLink);

      const ImageUrl = mediaLink.replace(
        'https://storage.googleapis.com/download/storage/v1/b',
        'https://firebasestorage.googleapis.com/v0/b',
      );

      const formdata = new FormData();
      formdata.append('path', storagePath);
      formdata.append('file_url', ImageUrl as string);
      try {
        console.log('Fetching from API');
        if (!process.env.KUBE_LUMA_ENDPOINT) {
          console.log('Openseadragon API link: Not available');
          // Throw new Error('Open sea dragon api is not available');
          return null;
        }

        const axiosResponse = await axios.post(
          process.env.KUBE_LUMA_ENDPOINT,
          formdata,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              ...formdata.getHeaders(),
            },
          },
        );

        if (axiosResponse.status === 200) {
          const responseData = axiosResponse.data;
          console.log('Response Data:', responseData);

          backgroundfiles = {
            low_resolution: ImageUrl as string,
            high_resolution: responseData.firebase_Link as string,
          };
          console.log('Files generated successfully', backgroundfiles);
        } else {
          console.log('Fetch error', axiosResponse.statusText);
        }
      } catch (fetchError) {
        console.log('Error in fetch:', fetchError);
      }
    } else {
      logger.info('lowRes Function Called');
      // If lowRes is given
      let lowRes_url = '';
      let filesToUpload: Express.Multer.File | Express.Multer.File[] |
      { [fieldname: string]: Express.Multer.File[]; } = [];

      Object.values(ImageFile).forEach((filesArray: Express.Multer.File[]) => {
        filesArray.forEach( (file: Express.Multer.File) => {

          if (file.fieldname === 'lowRes') {
            console.log('111', file);

            // Wrap the file in an object with the field name as key
            filesToUpload = { [file.fieldname]: [file] };
          }
        });
      });
      await UploadUnitplanFiles(filesToUpload, storagePath)
        .then(   (urlObject: { [key: string]: string }) => {
          console.log('url', urlObject.lowRes);
          lowRes_url =  urlObject.lowRes;
        }).catch((err) => console.log('Error in UploadUnitPlanFiles ' + err));

      console.log('lowres', lowRes_url);

      const formdata = new FormData();
      formdata.append('path', storagePath);
      formdata.append('file_url', lowRes_url as string);

      try {
        console.log('Fetching from API');
        if (!process.env.KUBE_LUMA_ENDPOINT) {
          console.log('Openseadragon API link: Not available');
          // Throw new Error('Open sea dragon api is not available');
          return null;
        }

        const axiosResponse = await axios.post(
          process.env.KUBE_LUMA_ENDPOINT,
          formdata,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
              ...formdata.getHeaders(),
            },
          },
        );

        if (axiosResponse.status === 200) {
          const responseData = axiosResponse.data;
          console.log('Response Data:', responseData);

          backgroundfiles = {
            low_resolution: lowRes_url as string,
            high_resolution: responseData.firebase_Link as string,
          };
          console.log('Files generated successfully', backgroundfiles);
        } else {
          console.log('Fetch error', axiosResponse.statusText);
        }
      } catch (fetchError) {
        console.log('Error in fetch:', fetchError);
      }
    }

  } catch (error) {
    console.log('Error in getHighResolution:', error);
  }

  return backgroundfiles;

}

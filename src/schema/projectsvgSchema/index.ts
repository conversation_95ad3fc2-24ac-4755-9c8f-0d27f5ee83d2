import mongoose from 'mongoose';
import { projectSVGType } from '../../types/projectSVG';
import { layersSchema } from '../layersSchema';
export const projectsvgSchema = new mongoose.Schema({
  _id: String,
  scene_id: String,
  svg_url: String,
  building_id: String,
  layers: { type: Object, of: layersSchema },
  type: {
    type: String,
    enum: projectSVGType,
    default: projectSVGType.LANDMARK,
  },
  name: String,
  rotation: {
    x: Number,
    y: Number,
    z: Number,
  },
  scale: {
    width: Number,
    height: Number,
  },
  position: {
    x: Number,
    y: Number,
    z: Number,
  },
  order: {
    immutable: false,
    type: Number,
  },
  viewbox: {
    width: Number,
    height: Number,
  },
  minZoomLevel: Number, // For deepzoom functionality
  maxZoomLevel: Number, // For deepzoom functionality
  applyOnLayers: Boolean, // For deepzoom functionality
});
const commonKeys = [
  'g',
  'x',
  'y',
  'title',
  'category',
  'placement',
  'reSize',
  'zIndex',
  'maxZoomLevel',
  'minZoomLevel',
  'name',
  'rotation',
  'position',
  'scale.x',
  'scale.y',
  'scale',
  'svg_url',
  'width',
  'height',
  'video_tag',
  'type',
  'layer_id',
  '_id',
];
export const checkFieldsSchema = {
  'project': ['project_id', ...commonKeys],
  'image': ['image_id', ...commonKeys],
  'project_scene': ['project_id', 'scene_id', ...commonKeys],
  'scene': ['scene_id', ...commonKeys],
  'pin': ['scene_id', ...commonKeys],
  'amenity': ['amenity_id', ...commonKeys],
  'community': ['community_id', 'scene_id', 'showLabel', ...commonKeys],
  'tower': ['building_id', 'scene_id', ...commonKeys],
  'building': ['building_id', 'scene_id', 'showLabel', ...commonKeys],
  'toweroverlay': ['floor_id', 'scene_id', 'building_id', ...commonKeys],
  'floor': ['floor_id', 'building_id', 'scene_id', ...commonKeys],
  'units': ['units', ...commonKeys],
  'label': ['title', 'category', ...commonKeys],
  'amenitycategory': ['amenity_category', ...commonKeys],
  'grouped_units': ['showLabel', 'bedrooms', ...commonKeys],
  'zoom_target': [...commonKeys],
};

import mongoose, {Types} from 'mongoose';
// Import { SceneType } from '../../types/virtualTour';
import { hotspotsSchema } from '../hotspotsSchema';
export const scenesSchema = new mongoose.Schema({
  _id: Types.ObjectId,
  title: String,
  hfov: Number,
  pitch: Number,
  yaw: Number,
  // Scene_type: {type: String, enum: SceneType},
  panorama: String,
  hot_spots: {type: {}, of: hotspotsSchema},
});

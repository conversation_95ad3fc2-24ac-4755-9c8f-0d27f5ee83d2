import mongoose from 'mongoose';
import { unitplan_type, measurementType, unitType, exterior_type } from '../../types/unitplan';
import { unitplanhotspotSchema } from '../unitplanhotspotSchema';

export const unitplanSchema = new mongoose.Schema({
  _id: String,
  project_id: String,
  building_id: String,
  type: String,
  name: String,
  thumbnail: String,
  image_url: String,
  measurement: {
    type: Number,
    default: 0,
  },
  measurement_type: {
    type: String,
    enum: measurementType,
  },
  tour_id: String,
  bedrooms: {
    type: String,
    required: false,
    default: null,
    validate: {
      validator: function (value: string | null) {
        // Allow null, undefined, or valid enum values
        if (value === null || value === undefined || value === '') {
          return true;
        }
        return Object.values(unitplan_type).includes(value as unitplan_type);
      },
      message: 'Bedrooms must be a valid enum value or null',
    },
  },
  // Is_residential: Boolean,
  is_commercial: {
    type: Boolean,
    default: false,
  },
  bathrooms: Number,
  is_furnished: Boolean,
  unit_type: {
    type: String,
    enum: unitType,
    default: unitType.FLAT,
  },
  floor_unitplans: Array<string>,
  exterior_type: {
    type: String,
    enum: exterior_type,
  },
  scene_id: String,
  gallery_id: Array<string>,
  style: String,
  balcony_measurement: Number,
  balcony_measurement_type: {
    type: String,
    enum: measurementType,
  },
  suite_area: Number,
  suite_area_type: {
    type: String,
    enum: measurementType,
  },
  hotspots: {type: {}, of: unitplanhotspotSchema},
});

import mongoose from 'mongoose';
import { bedroom_format, ctaType, FontType, logoType, measurement_type, PropertyType, Theme, unitCardType, StatusDefaultColor } from '../../types/projects';
// Define sub-schemas for each type of project setting
export const projectSchema = new mongoose.Schema({
  _id: {
    type: String,
    immutable: true,
  },
  units: {
    type: Object,
    immutable: false,
  },
  name: {
    type: String,
    immutable: false,
  },
  description: {
    type: String,
    immutable: false,
  },
  experience: {
    type: Array,
    immutable: false,
  },
  property_type: {
    type: String,
    enum: PropertyType,
  },
  is_public: {
    type: Boolean,
    immutable: false,
  },
  organization_id: {
    type: String,
    immutable: false,
  },
  role: {
    type: String,
    immutable: false,
  },
  city: {
    type: String,
    immutable: false,
  },
  country: {
    type: String,
    immutable: false,
  },
  project_thumbnail: {
    type: String,
    immutable: false,
  },
  unique_project_id: {
    type: String,
    immutable: false,
  },
  data_sync: {
    number_of_units: {
      type: Number,
      immutable: false,
    },
    number_of_floors: {
      type: Number,
      immutable: false,
    },
    number_of_amenities: {
      type: Number,
      immutable: false,
    },
    number_of_buildings: {
      type: Number,
      immutable: false,
    },
  },
  projectSettings: {
    general: {
      slots: {
        type: Array,
        immutable: false,
      },
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
      branding_logo: {
        type: String,
        immutable: false,
      },
      branding_logo_dark: {
        type: String,
        immutable: false,
      },
      lat: {
        type: Number,
        immutable: false,
      },
      long: {
        type: Number,
        immutable: false,
      },
      hideStatus: {
        type: Boolean,
        immutable: false,
      },
      timezone: {
        type: String,
        immutable: false,
      },
      updated_at: {
        type: String,
        immutable: false,
      },
    },
    pixelstreaming: {
      max_concurrent_sessions: {
        type: Number,
        immutable: false, // Immutable fields are not updatable
      },
      pixel_streaming_endpoint: {
        type: String,
        immutable: false,
      },
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
      session_duration: {
        type: Number,
        immutable: false,
      },
      resource_group: {
        type: String,
        immutable: false,
      },
      vm_scaleset_name: {
        type: String,
        immutable: false,
      },
      min_instances: {
        type: Number,
        immutable: false,
      },
      auto_scale: {
        type: Boolean,
        immutable: false,
      },
      application_id: {
        type: String,
        immutable: false,
      },
      generated_key: {
        type: Array,
        immutable: false,
      },
    },
    salestool: {
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
      session_duration: {
        type: Number,
        immutable: false,
      },
      default_experience: {
        type: String,
        immutable: false,
      },
      tags: {
        type: Array,
      },
    },
    ale: {
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
      initial_scene_type: {
        type: String,
        immutable: false,
      },
      initial_scene_id: {
        type: String,
        immutable: false,
      },
      welcome_video: {
        type: String,
        immutable: false,
      },
      welcome_thumbnail: {
        type: String,
        immutable: false,
      },
      shortened_link: {
        type: String,
        immutable: false,
      },
      default_language: {
        type: String,
        immutable: false,
      },
      supported_languages: {
        type: Array,
        immutable: false,
      },
      currency_support: {
        type: Boolean,
        default: false,
        immutable: false,
      },
      share_scenes: {
        whatsapp: {
          type: Boolean,
          default: false,
          immutable: false,
        },
        email: {
          type: Boolean,
          default: false,
          immutable: false,
        },
        twitter: {
          type: Boolean,
          default: false,
          immutable: false,
        },
        // Instagram: {
        //   Type: Boolean,
        //   Default: false,
        //   Immutable: false,
        // },
        // Facebook: {
        //   Type: Boolean,
        //   Default: false,
        //   Immutable: false,
        // },
      },
      svg_visibility: {
        type: Boolean,
        default: false,
        immutable: false,
      },
      cta_name: {
        type: String,
        default: 'Book Unit',
        immutable: false,
      },
      cta_type: {
        type: String,
        default: ctaType.DEFAULT,
        enum: ctaType,
        immutable: false,
      },
      is_cta_enabled: {
        type: Boolean,
        immutable: false,
      },
      is_call_enabled: {
        type: Boolean,
        immutable: false,
      },
      is_unitplan_cta_enabled: {
        type: Boolean,
        immutable: false,
      },
      unitplan_cta_type: {
        type: String,
        default: ctaType.DEFAULT,
        enum: ctaType,
        immutable: false,
      },
      unitplan_cta_name: {
        type: String,
        default: 'Book Unit',
        immutable: false,
      },
      unitplan_cta_link: {
        type: String,
        immutable: false,
      },
      unit_card_customize_type: {
        type: String,
        default: unitCardType.DEFAULT,
        enum: unitCardType,
        immutable: false,
      },
      unitcard_config: {
        type: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        measurement: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        bedrooms: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        bathrooms: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        status: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        style: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        price: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        view: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        maid: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        building_id: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        floor_id: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        units: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        favIcon: {
          type: Boolean,
          default: true,
          immutable: false,
        },
      },
      measurement_type: {
        type: String,
        default: measurement_type.SQFT,
        enum: measurement_type,
        immutable: false,
      },
      bedroom_format: {
        type: String,
        default: bedroom_format.BR,
        enum: bedroom_format,
        immutable: false,
      },
      unit_label: {
        type: Boolean,
        default: true,
        immutable: false,
      },
      is_logo_clickable: {
        type: Boolean,
        default: false,
        immutable: false,
      },
      logo_click_type: {
        type: String,
        default: logoType.DEFAULT,
        enum: logoType,
      },
      logo_click_link: {
        type: String,
        immutable: false,
      },
    },
    embed: {
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
    },
    theme: {
      theme: {
        type: String,
        enum: Theme,
      },
      primary: {
        type: String,
        immutable: false,
      },
      primary_text: {
        type: String,
        immutable: false,
      },
      secondary: {
        type: String,
        immutable: false,
      },
      secondary_text: {
        type: String,
        immutable: false,
      },
      font_type: {
        type: String,
        enum: FontType,
      },
      font_url: {
        type: String,
        immutable: false,
      },
    },
    gallery: {
      type: Object,
    },
    amenity: {
      type: Object,
    },
    hologram: {
      project_logo: {
        type: String,
        immutable: false,
      },
      project_type: {
        type: String,
        immutable: false,
      },
      project_location: {
        type: String,
        immutable: false,
      },
      amount: {
        type: String,
        immutable: false,
      },
      bedrooms: {
        type: Array,
        immutable: false,
      },
      thumbnail: {
        type: String,
        immutable: false,
      },
      file: {
        type: String,
        immutable: false,
      },
      tags: {
        type: Array,
      },
    },
    metadata: {
      type: Object,
    },
    statusCategory: {
      type: Object,
    },
    customization: {
      status_config: {
        'available': {
          color: {
            mode: {
              type: String,
              enum: ['colored', 'transparent'],
              required: true,
            },
            defaultColor: {
              type: String,
              enum: Object.values(StatusDefaultColor),
              required: true,
            },
            selectedColor: {
              type: String,
              required: false,
            },
          },
          interaction: {
            type: String,
            enum: ['visible_clickable', 'visible_not_clickable', 'not_visible'],
            required: true,
          },
        },
        'onhold': {
          color: {
            mode: {
              type: String,
              enum: ['colored', 'default'],
              required: true,
            },
            defaultColor: {
              type: String,
              enum: Object.values(StatusDefaultColor),
              required: true,
            },
            selectedColor: {
              type: String,
              required: false,
            },
          },
          interaction: {
            type: String,
            enum: ['visible_clickable', 'visible_not_clickable', 'not_visible'],
            required: true,
          },
        },
        'not available': {
          color: {
            mode: {
              type: String,
              enum: ['colored', 'default'],
              required: true,
            },
            defaultColor: {
              type: String,
              enum: Object.values(StatusDefaultColor),
              required: true,
            },
            selectedColor: {
              type: String,
              required: false,
            },
          },
          interaction: {
            type: String,
            enum: ['visible_clickable', 'visible_not_clickable', 'not_visible'],
            required: true,
          },
        },
      },
    },
  },
});

// Return type for default customization (matches schema usage)
type DefaultCustomizationReturn = {
  status_config: {
    'available': {
      color: { mode: 'transparent'; defaultColor: StatusDefaultColor };
      interaction: 'visible_clickable';
    };
    'onhold': {
      color: { mode: 'default'; defaultColor: StatusDefaultColor };
      interaction: 'visible_clickable';
    };
    'not available': {
      color: { mode: 'default'; defaultColor: StatusDefaultColor };
      interaction: 'visible_clickable';
    };
  };
};

// Helper function to get default customization
export const getDefaultCustomization = (): DefaultCustomizationReturn => ({
  status_config: {
    'available': {
      color: {
        mode: 'transparent',
        defaultColor: StatusDefaultColor.AVAILABLE_TRANSPARENT,
      },
      interaction: 'visible_clickable',
    },
    'onhold': {
      color: {
        mode: 'default',
        defaultColor: StatusDefaultColor.ONHOLD_TRANSPARENT,
      },
      interaction: 'visible_clickable',
    },
    'not available': {
      color: {
        mode: 'default',
        defaultColor: StatusDefaultColor.NOT_AVAILABLE_TRANSPARENT,
      },
      interaction: 'visible_clickable',
    },
  },
});

// Transform to ensure customization is always included in JSON output
projectSchema.set('toJSON', {
  transform: function (doc: any, ret: any) {
    // Ensure customization is set if missing
    if (!ret.projectSettings?.customization || !ret.projectSettings?.customization?.status_config) {
      ret.projectSettings = ret.projectSettings || {};
      ret.projectSettings.customization = getDefaultCustomization();
    }
    return ret;
  },
});

// Post-init hook to set default customization in memory when document is loaded
// This fires for non-lean queries when documents are initialized
projectSchema.post('init', function (doc) {
  if (doc && (!doc.projectSettings?.customization || !doc.projectSettings?.customization?.status_config)) {
    doc.projectSettings = doc.projectSettings || {};
    doc.projectSettings.customization = getDefaultCustomization();
    // Only mark as modified if it's a Mongoose document (not a lean query)
    if (typeof (doc as any).markModified === 'function') {
      (doc as any).markModified('projectSettings.customization');
    }
  }
});

// Post-find hooks to ensure customization is set in database for existing projects
projectSchema.post(['find', 'findOne', 'findOneAndUpdate'], function (docs: any) {
  const documents = Array.isArray(docs) ? docs : [docs];
  const updates: Promise<any>[] = [];

  // Resolve the Model so we can persist to DB (dynamic model name per org)
  let Model: any = null;
  // 1. Query context (this = Query, has .model in Mongoose)
  if ((this as any).model && typeof (this as any).model.updateOne === 'function') {
    Model = (this as any).model;
  }
  // 2. From document constructor.modelName (Mongoose doc only)
  if (!Model && documents.length > 0 && documents[0]) {
    const firstDoc = documents[0];
    const modelName = (firstDoc as any).constructor?.modelName;
    if (modelName) {
      try {
        Model = mongoose.model(modelName);
      } catch (e) {
        // Model not registered with that name
      }
    }
  }
  // 3. From collection name (lean docs may have .collection on the query result)
  if (!Model && documents.length > 0 && (documents[0] as any).collection?.name) {
    try {
      Model = mongoose.model((documents[0] as any).collection.name);
    } catch (e) {
      // Ignore
    }
  }

  const defaultCustomization = getDefaultCustomization();

  for (const doc of documents) {
    if (doc && doc._id && (!doc.projectSettings?.customization || !doc.projectSettings?.customization?.status_config)) {
      // Always set in memory first for immediate use (synchronously)
      doc.projectSettings = doc.projectSettings || {};
      doc.projectSettings.customization = defaultCustomization;

      // Ensure the field is included when document is serialized
      if (typeof (doc as any).markModified === 'function') {
        (doc as any).markModified('projectSettings.customization');
      }

      // Persist to DB: prefer Model.updateOne (works for both full and lean docs)
      if (Model && typeof Model.updateOne === 'function') {
        updates.push(
          Model.updateOne(
            { _id: doc._id },
            { $set: { 'projectSettings.customization': defaultCustomization } },
          ).catch((err: any) => {
            console.error('Error saving default customization to DB:', err);
          }),
        );
      } else if (typeof (doc as any).save === 'function') {
        // Fallback: Mongoose document save when Model not available
        updates.push(
          (doc as any).save().catch((err: any) => {
            console.error('Error saving default customization (save):', err);
          }),
        );
      }
    }
  }

  if (updates.length > 0) {
    Promise.all(updates).catch((err) => {
      console.error('Error in batch customization update:', err);
    });
  }
});

// Const sidebarCommonKeys = [
//   'project_id',
//   'type',
//   'id',
//   'icon_id'
// ]
// Export const sidebarCheckFields = {
//   'custom':['link',...sidebarCommonKeys],
//   'projectscene':['scene_id',...sidebarCommonKeys],
//   'masterscene':['scene_id',...sidebarCommonKeys],
//   'amenity':[...sidebarCommonKeys],
//   'gallery':[...sidebarCommonKeys],
//   'unitplan':[...sidebarCommonKeys],
//   'map':[...sidebarCommonKeys],
// }

import mongoose from 'mongoose';
import { SessionStatus, SessionSource, SessionType } from '../../types/session';
export const sessionSchema = new mongoose.Schema({
  _id: String,
  duration_minutes: Number,
  status: {
    type: String,
    enum: SessionStatus,
    default: SessionStatus.ACTIVE,
  },
  pixel_duration_minutes: Number,
  code: String,
  start: String,
  type: {
    type: String,
    enum: SessionType,
    default: SessionType.DEFAULT,
  },
  scheduled_end_time: String,
  invite_link: String,
  organization_id: String,
  user_id: String,
  project_id: String,
  source: {
    type: String,
    enum: SessionSource,
    default: SessionSource.DASHBOARD,
  },
  last_interaction_time: String,
  config: Object,
  is_scheduled: Boolean,
  schedule_time: String,
  end_time: String,
  instance_start_time: String,
  instance_end_time: String,
  description: String,
  participants: Array,
  thread_id: String,
  tag: String,
  is_pixelstreaming_active: Boolean,
  pixel_streaming_link: String,
  is_reserved: {
    type: Boolean,
    default: false,
  },
});

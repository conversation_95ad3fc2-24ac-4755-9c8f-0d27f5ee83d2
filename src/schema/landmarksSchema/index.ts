import mongoose from 'mongoose';
import { masterLandmarkCategory } from '../../types/masterLandmark';
export const landmarksSchema = new mongoose.Schema({
  _id: String,
  scene_id: String,
  name: String,
  thumbnail: String,
  distance: Number,
  category: {
    type: String,
    enum: masterLandmarkCategory,
    default: masterLandmarkCategory.OTHER,
  },
  walk_timing: Number,
  transit_timing: Number,
  car_timing: Number,
});

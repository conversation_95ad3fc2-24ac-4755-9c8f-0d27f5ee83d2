import mongoose from 'mongoose';

export const notificationSchema = new mongoose.Schema({
  _id: {
    type: String,
    immutable: false,
  },
  url: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    required: true,
  },
  user_id: {
    type: String,
    required: true,
  },
  timestamp: {
    type: String,
    required: true,
  },
  organization: {
    type: String,
    required: true,
  },
  viewed: {
    type: Boolean,
    required: true,
  },
});

import mongoose from 'mongoose';
import { Direction } from '../../types/direction';
import { priceCurrency, measurementType } from '../../types/units';

export const unitSchema = new mongoose.Schema({
  _id: String,
  unitplan_id: String,
  project_id: String,
  name: String,
  status: {
    type: String,
    default: 'available',
  },
  metadata: Object,
  building_id: String,
  floor: String,
  floor_id: String,
  price: String,
  max_price: String,
  community_id: String,
  currency: {
    type: String,
    enum: priceCurrency,
    default: priceCurrency.INR,
  },
  tour_id: String,
  measurement: Number,
  measurement_type: {
    type: String,
  },
  cta_link: {type: String},
  balcony_measurement: Number,
  balcony_measurement_type: {
    type: String,
  },
  suite_area: Number,
  suite_area_type: {
    type: String,
    enum: measurementType,
  },
  direction: {
    type: String,
    required: false,
    default: null,
    validate: {
      validator: function (value: string | null) {
        // Allow null, undefined, or valid enum values
        if (value === null || value === undefined || value === '') {
          return true;
        }
        return Object.values(Direction).includes(value as Direction);
      },
      message: 'Direction must be a valid enum value or null',
    },
  },
  created_at: {
    type: String,
    default: new Date().toISOString(),
  },
});

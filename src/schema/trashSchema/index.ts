import mongoose from 'mongoose';
const trashSchema = new mongoose.Schema({
  _id: String,
  type: String,
  root: Boolean,
  timeStamp: Number,
  createdAt: { type: Date, default: Date.now },
  data: Object,
  linked_trashes: Array<string>,
});

// TrashSchema.index({ createdAt: 1 }, { expireAfterSeconds: 15 * 24 * 60 * 60 });
// TrashSchema.index({ createdAt: 1 }, { expireAfterSeconds: 5 });

export { trashSchema };

import mongoose from 'mongoose';
export const customTourSchema = new mongoose.Schema({
  _id: {
    type: String,
    immutable: true,
  },
  name: {
    type: String,
    immutable: false,
  },
  rotation: {
    type: Object,
    immutable: false,
  },
  thumbnail: {
    type: String,
    immutable: false,
  },
  url: {
    type: String,
    immutable: false,
  },
  group_id: {
    type: String,
    immutable: false,
  },
  group_name: {
    type: String,
    immutable: false,
  },
  hotspots: [{
    _id: {
      type: String,
      immutable: true,
    },
    position: {
      type: Object,
      immutable: false,
    },
    destination: {
      type: String,
      immutable: false,
    },
  }],
});

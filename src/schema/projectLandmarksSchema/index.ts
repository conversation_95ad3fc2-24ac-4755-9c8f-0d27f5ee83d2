import mongoose from 'mongoose';
import { landmarkCategory } from '../../types/projectLandmark';
export const projectLandmarksSchema = new mongoose.Schema({
  _id: String,
  name: String,
  thumbnail: String,
  distance: Number,
  category: {
    type: String,
    enum: landmarkCategory,
    default: landmarkCategory.OTHER,
  },
  icon: String,
  walk_timing: Number,
  transit_timing: Number,
  car_timing: Number,
  lat: Number,
  long: Number,
  driving: String,
  transit: String,
  walking: String,
  description: String,
  metadata: Object,
});

import mongoose from 'mongoose';
import { LeadSource, LeadStatus, LeadInterest, LeadProductInterest, LeadIndustryType } from '../../types/leads';

export const  leadsSchema = new mongoose.Schema({
  _id: String,
  session_id: String,
  organization_id: String,
  name: String,
  phone_number: String,
  email: String,
  duration_minutes: Number,
  joining_time: String,
  interested_in: Array<LeadInterest>,
  lead_product_interest: {
    type: String,
    enum: Object.values(LeadProductInterest),
    required: false,
  },
  lead_industry_type: {
    type: String,
    enum: Object.values(LeadIndustryType),
    required: false,
  },
  lead_source: {
    type: String,
    enum: LeadSource,
    default: LeadSource.DASHBOARD,
  },
  start_time: String,
  user_id: String,
  next_interaction_time: String,
  last_interaction_time: String,
  end_time: String,
  lead_creation_time: String,
  lead_status: {
    type: String,
    enum: LeadStatus,
    default: LeadStatus.NEW,
  },
  thread_id: String,
});

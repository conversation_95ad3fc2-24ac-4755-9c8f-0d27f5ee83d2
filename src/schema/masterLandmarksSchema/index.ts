import mongoose from 'mongoose';
import { masterLandmarkCategory } from '../../types/masterLandmark';
export const masterLandmarksSchema = new mongoose.Schema({
  _id: String,
  name: String,
  thumbnail: String,
  distance: Number,
  category: {
    type: String,
    enum: masterLandmarkCategory,
    default: masterLandmarkCategory.OTHER,
  },
  walk_timing: Number,
  transit_timing: Number,
  car_timing: Number,
  description: String,
});

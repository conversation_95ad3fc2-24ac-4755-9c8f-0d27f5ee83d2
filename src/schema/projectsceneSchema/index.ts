import mongoose from 'mongoose';
import { deep_zoom_status, projectSceneType } from '../../types/projectScene';
import { Direction } from '../../types/direction';
export const projectscenesSchema = new mongoose.Schema({
  _id: String,
  organization_id: String,
  type: {
    type: String,
    enum: projectSceneType,
    default: projectSceneType.IMAGE,
  },
  name: String,
  background: {
    low_resolution: {
      type: String,
      immutable: false,
    },
    high_resolution: {
      type: String,
      immutable: false,
    },
    low_resolution_night: {
      type: String,
      immutable: false,
    },
    high_resolution_night: {
      type: String,
      immutable: false,
    },
    high_resolution_copy: {
      type: String,
      immutable: false,
    },
    high_resolution_night_copy: {
      type: String,
      immutable: false,
    },
  },
  active: Boolean,
  info_icon: String,
  parent: String,
  info_text: String,
  root: Boolean,
  building_id: String,
  floor_ids: Array,
  clouds: Boolean,
  video: String,
  gsplat_link: String,
  category: String,
  frames: Object,
  order: Number,
  deep_zoom_status: {
    type: String,
    enum: deep_zoom_status,
  },
  deep_zoom_failed_info: String,
  position: {
    x: Number,
    y: Number,
    z: Number,
  },
  polar_angle: {
    max: Number,
    min: Number,
  },
  distance: {
    max: Number,
    min: Number,
  },
  auto_rotate: Boolean,
  minZoomLevel: Number,
  maxZoomLevel: Number,
  viewbox: {
    width: Number,
    height: Number,
  },
  direction: {
    type: String,
    required: false,
    default: null,
    validate: {
      validator: function (value: string | null) {
        // Allow null, undefined, or valid enum values
        if (value === null || value === undefined || value === '') {
          return true;
        }
        return Object.values(Direction).includes(value as Direction);
      },
      message: 'Direction must be a valid enum value or null',
    },
  },
  north_direction: {
    position: {
      x: Number,
      y: Number,
      z: Number,
    },
    rotation: {
      x: Number,
      y: Number,
      z: Number,
      w: Number,
    },
  },
  preview: {
    type: String,
    immutable: false,
  },
});

import mongoose from 'mongoose';
import { masterSVGType } from '../../types/masterSVG';
export const mastersvgSchema= new mongoose.Schema({
  _id: String,
  scene_id: String,
  svg_url: String,
  layers: Object,
  type: {
    type: String,
    enum: masterSVGType,
    default: masterSVGType.LANDMARK,
  },
  minZoomLevel: {
    type: Number,
    min: 0,
    max: 100,
  },
  maxZoomLevel: {
    type: Number,
    min: 0,
    max: 100,
  },
  applyOnLayers: Boolean, // For deepzoom functionality
});
const commonKeys = [
  'g',
  'x',
  'y',
  'placement',
  'reSize',
  'zIndex',
  'maxZoomLevel',
  'minZoomLevel',
  'name',
  'rotation',
  'position',
  'scale.x',
  'scale.y',
  'scale',
  'svg_url',
  'width',
  'height',
  'video_tag',
  'type',
  'layer_id',
  '_id',
];

export const checkMasterFieldsSchema = {
  'scene': ['scene_id', ...commonKeys],
  'project': ['project_id', 'master_radius_id', ...commonKeys],
  'project_scene': ['project_id', 'scene_id', ...commonKeys],
  'pin': ['scene_id', ...commonKeys],
  'image': ['image_id', ...commonKeys],
  'route': [...commonKeys],
  'master_radius': [...commonKeys],
  'radius': [...commonKeys],
  'label': ['title', 'category', ...commonKeys],
  'unavailable': [...commonKeys],
  'static': [...commonKeys],
  'plane': [...commonKeys],
  'none': [...commonKeys],
  'zoom_target': [...commonKeys],
  'city': ['city', 'country', 'scene_id', ...commonKeys],
};

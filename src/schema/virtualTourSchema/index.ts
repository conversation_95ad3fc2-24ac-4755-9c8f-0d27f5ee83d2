import mongoose from 'mongoose';
import { tourCategory, tourType } from '../../types/virtualTour';
export const virtualTourSchema = new mongoose.Schema({
  _id: String,
  name: String,
  tour_name: String,
  description: String,
  organization: String,
  project_id: String,
  category: {
    type: String,
    enum: tourCategory,
    default: tourCategory.INTERIOR,
  },
  type: {
    type: String,
    enum: tourType,
  },
  images: Object,
  unitplan_id: String,
  groups: Object,
  space_id: String,
  link: String,
  created_at: Date,
  updated_at: Date,
  model: String,
  camera: String,
  labels: Object,
  initial_rotation: Object,
});

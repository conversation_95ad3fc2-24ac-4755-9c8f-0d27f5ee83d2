import mongoose from 'mongoose';
export const AssetsSchema = new mongoose.Schema({
  _id: {
    immutable: true,
    type: String,
  },
  file_name: {
    immutable: false,
    type: String,
  },
  file_url: {
    immutable: false,
    type: String,
  },
  file_type: {
    immutable: false,
    type: String,
  },
  media_type: {
    immutable: false,
    type: String,
  },
  created_at: {
    immutable: false,
    type: String,
  },
  updated_at: {
    immutable: false,
    type: String,
  },
});

import mongoose from 'mongoose';
import { MediaType } from '../../types/amenity';
export const amenitySchema = new mongoose.Schema({
  _id: {
    immutable: true,
    type: String,
  },
  project_id: {
    immutable: true,
    type: String,
  },
  name: {
    immutable: false,
    type: String,
  },
  category: {
    type: String,
  },
  community_id: {
    immutable: false,
    type: String,
  },
  thumbnail: {
    type: String,
    immutable: false,
  },

  media_type: {
    type: String,
    enum: MediaType,
    default: MediaType.IMAGE,
  },
  file: {
    type: String,
    immutable: false,
  },
  embed_link: {
    type: String,
    immutable: false,
  },
  tour_id: {
    type: String,
    immutable: false,
  },

  description: {
    type: String,
    immutable: false,
  },
  order: {
    immutable: false,
    type: Number,
  },
  links: { type: Object},
  rotation: { type: String, immutable: false },
  media: { type: Array, required: false },
  modified: { type: Date, required: true},
});

import mongoose from 'mongoose';
import { FontType, Theme } from '../../types/projects';
export const organizationSchema = new mongoose.Schema({
  _id: String,
  name: String,
  founding_date: Date,
  contact_email: String,
  phone_number: String,
  thumbnail: String,
  address: String,
  website: String,
  max_users: Number,
  roles: Array,
  slots: Array,
  timezone: String,
  domain: String,
  unique_org_id: String,
  organizationSettings: {
    pixelstreaming: {
      lark_groupid: {
        type: String,
        immutable: false,
      },
      max_concurrent_sessions: {
        type: Number,
        immutable: false,
      },
      duration: {
        type: Number,
        immutable: false,
      },
    },
    salestool: {
      slots: { type: [mongoose.Schema.Types.Mixed],
        default: undefined,
      },
      timezone: String,
    },
    currency: {
      baseCurrency: {
        type: String,
      },
      exchangeRatio: {
        type: Object,
      },
      currency_provider: {
        type: String,
        immutable: false,
      },
      currency_provider_name: {
        type: String,
        immutable: false,
      },
    },
    theme: {
      theme: {
        type: String,
        enum: Theme,
      },
      primary: {
        type: String,
        immutable: false,
      },
      primary_text: {
        type: String,
        immutable: false,
      },
      secondary: {
        type: String,
        immutable: false,
      },
      secondary_text: {
        type: String,
        immutable: false,
      },
      font_type: {
        type: String,
        enum: FontType,
      },
      font_url: {
        type: String,
        immutable: false,
      },
    },
    weblite: {
      mastersvg_visibility: {
        type: Boolean,
      },
      share_masterscenes: {
        whatsapp: {
          type: Boolean,
          default: false,
          immutable: false,
        },
        email: {
          type: Boolean,
          default: false,
          immutable: false,
        },
        twitter: {
          type: Boolean,
          default: false,
          immutable: false,
        },
      },
      is_broadcast_enabled: {
        type: Boolean,
      },
      org_logo_click_type: String,
      org_logo_click_link: String,
    },
  },
});

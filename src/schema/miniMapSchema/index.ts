import mongoose from 'mongoose';
import { TypeEnum } from '../../types/miniMap';

export const amenityMiniMapSchema = new mongoose.Schema({
  _id: {
    type: String,
    immutable: false,
  },
  name: {
    type: String,
    required: true,
  },
  low_res: {
    type: String,
    required: true,
  },
  high_res: {
    type: String,
    required: true,
  },
  hotspots: {
    type: Object,
  },
  type: {
    type: String,
    enum: TypeEnum,
  },
  referenceId: {
    type: String,
  },
});

import { Request, Response, NextFunction } from 'express';
import logger from '../config/logger';

const loggerMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Log the incoming request details with jsonPayload
  logger.info('Incoming Request', {
    method: req.method,
    url: req.url,
    params: Object.keys(req.params).length > 0 ? req.params : undefined,
    query: Object.keys(req.query).length > 0 ? req.query : undefined,
    body: Object.keys(req.body).length > 0 ? req.body : undefined,
  });

  // Capture the original send method
  const originalSend = res.send;

  // Replace the send method to handle response errors
  res.send = function (body: unknown) {
    return originalSend.call(this, body);
  };

  // Capture errors using a custom error-handling middleware
  res.on('finish', () => {
    if (res.statusCode >= 400) {
      logger.error('Error Response', {
        method: req.method,
        url: req.url,
        status: res.statusCode,
        response: res.statusMessage,
      });
    }
  });

  next();
};

export default loggerMiddleware;

import { NextFunction, RequestHandler, Response } from 'express';
import { OrganizationModule } from '../modules/organization';
import { ExtendedRequest } from '../types/extras';

export const organizationAccessMiddleware: RequestHandler = async (
  request: ExtendedRequest,
  response: Response,
  next: NextFunction,
) => {
  if (request.headers && request.headers.organization) {
    const organization_id: string | string[] = request.headers.organization;
    if (organization_id && typeof organization_id === 'string') {
      const organization = new OrganizationModule();
      const organizationExists = await organization.IsOrganizationIdExists(
        organization_id,
      );
      if (!organizationExists) {
        request.organization_id = null;
        response.status(400).send({status: 0, error: 'Organization is not present'});
        return false;
      }
      if (!request.IsAuthenticated) {
        request.organization_id = null;
        response.status(400).send({status: 0, error: 'User not found'});
        return false;
      }
      const UserRole = await organization.GetUserRole(
        request.IsAuthenticated?.uid,
        organization_id,
      );
      if (!UserRole) {
        request.organization_id = null;
        response.status(400).send({status: 0, error: 'User not found in this organization'});
        return false;
      }
      request.organization_id = organization_id;
      request.UserRole = UserRole;
      next();
      return true;
    }

    request.organization_id = null;
    response.status(400).send({status: 0, error: 'Invalid Organization id'});
    return false;
  }

  request.organization_id = null;
  response.status(400).send({status: 0, error: 'Missing Organization id'});
  return false;
};

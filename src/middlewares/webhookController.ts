import { NextFunction, RequestHandler, Response } from 'express';
import { ExtendedRequest } from '../types/extras';
import { CustomUserModule } from '../modules/webhooks';

export const CustomAuthControl: RequestHandler = function (
  request: ExtendedRequest,
  response: Response,
  next: NextFunction,
):void{
  if (request.headers.apikey && request.headers.project_id){
    const apikey: string  = request.headers.apikey as string;
    const project_id : string = request.headers.project_id as string;
    const customUser = new CustomUserModule(project_id);
    customUser.GetUserId(apikey).then((result) => {
      if (result){
        request.organization_id=result.organization;
        next();
      } else {
        response.send({status: 0, error: 'Invalid apikey'});
      }
    }).catch((err) => {
      response.send({status: 0, error: 'Internal Server error '+err});
    });
  } else {
    response.send({status: 0, error: 'Missing key or project_id please check request'});
  }
};

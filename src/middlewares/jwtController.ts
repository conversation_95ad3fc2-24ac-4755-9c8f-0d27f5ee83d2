import { NextFunction, RequestHandler, Response } from 'express';
import { ExtendedRequest } from '../types/extras';
import { CustomUserModule } from '../modules/webhooks';
import jwt from 'jsonwebtoken';
import { JWTModule } from '../modules/jwt';
import { ProjectModule } from '../modules/projects';

export const CustomAuthControl: RequestHandler = function (
  request: ExtendedRequest,
  response: Response,
  next: NextFunction,
):void{
  if (request.headers.apikey && request.headers.project_id){
    const apikey: string  = request.headers.apikey as string;
    const project_id : string = request.headers.project_id as string;
    const customUser = new CustomUserModule(project_id);
    customUser.GetUserId(apikey).then((result) => {
      if (result){
        request.organization_id=result.organization;
        next();
      } else {
        response.send({status: 0, error: 'Invalid apikey'});
      }
    }).catch((err) => {
      response.send({status: 0, error: 'Internal Server error '+err});
    });
  } else {
    response.send({status: 0, error: 'Missing key or project_id please check request'});
  }
};

export const JWTAuthControl : RequestHandler =  function (
  request: ExtendedRequest,
  response: Response,
  next: NextFunction,
): void{
  try {
    const authHeader = request.headers.authorization;
    const headerToken = authHeader && authHeader.split(' ')[1];
    if (headerToken) {
      const token = headerToken as string;
      jwt.verify(token, process.env.JWT_TOKEN as string, (err) => {
        if (err) {
          response.status(401).send({ status: 0, message: 'Unauthorized api key' });
          return;
        }
        const jwtModule = new JWTModule();
        jwtModule.GetJWTRecord(token).then((data) => {
          if (data) {
            console.log('DATA FOUND', data);

            const organizationId = data.organization;
            request.organization_id = organizationId;
            next();
          } else {
            response.status(401).send({ status: 0, message: 'Record not found' });
          }
        }).catch(() => {
          response.status(500).send({ status: 0, message: 'Please check organization role.' });
        });
      });
    } else {
      response.status(401).send({ status: 0, message: 'Missing apikey' });
    }
  } catch (err){
    console.log(err);
    response.status(500).send({status: 0, message: 'Error getting record '+err });
  }
};

export const JWTProjectChecker: RequestHandler = function (
  request: ExtendedRequest,
  response: Response,
  next: NextFunction,
): void {
  try {
    const authHeader = request.headers.authorization;
    const projectId = request.body.project_id as string;
    const headerToken = authHeader && authHeader.split(' ')[1];
    if (headerToken && projectId) {
      const token = headerToken as string;
      jwt.verify(token, process.env.JWT_TOKEN as string, (err) => {
        if (err) {
          response.status(401).send({ status: 0, message: 'Unauthorized api key ' + err });
          return;
        }
        // Move async logic outside the callback
        (async () => {
          let organizationId: string = '';
          const jwtModule = new JWTModule();
          const jwtRecord = await jwtModule.GetJWTRecord(token);

          if (!jwtRecord) {
            response.status(401).send({
              status: 0,
              message: 'JWT Record not found',
            });
            return;
          }
          organizationId = jwtRecord.organization;
          request.organization_id = organizationId;

          const projectModule = new ProjectModule(organizationId);
          const projectRecord = await projectModule.GetProjectInOrganization(projectId);

          if (!projectRecord) {
            response.status(404).send({
              status: 0,
              message: 'Project not found in organization',
            });
            return;
          }
          next();
        })();
      });
    } else {
      response.status(401).send({ status: 0, message: 'Missing apikey' });
    }
  } catch (err) {
    console.log(err);
    response.status(500).send({ status: 0, message: 'Error getting record ' + err });
  }
};

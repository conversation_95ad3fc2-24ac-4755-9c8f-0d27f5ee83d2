import { NextFunction, RequestHandler, Response } from 'express';
import { ExtendedRequest } from '../types/extras';

export function accessControlMiddleware (
  allowedRoles: string[],
): RequestHandler {
  const handler: RequestHandler = async function (
    request: ExtendedRequest,
    response: Response,
    next: NextFunction,
  ): Promise<void> {
    if (!request.IsAuthenticated) {
      request.organization_id = null;
      // Response.status(401).json({ status: 0, error: 'User not found' });
      return;
    }

    const UserRole = request.UserRole;
    if (UserRole) {
      if (allowedRoles.includes(UserRole.role)) {
        request.hasRoleAccess = true;
        next();
        return;
      }

      response.status(403).json({
        status: 0,
        error: 'Permission Denied: User lacks required role',
      });
      return;
    }

    // Response.status(401).json({ status: 0, error: 'Permission Denied: User unauthenticated' });
    return;
  };

  return handler;
}

import mongoose from 'mongoose';
import {
  ExperienceType,
  Project,
  ProjectSettingsType,
  bulkGalleryUpdateType,
  PropertyType,
  bulkUpdateType,
  createProjectInput,
  editProjectInput,
  projectVMDetails,
  unitCardType,
  updateProjectInput,
  ctaType,
  StatusDefaultColor,
  logoType,
} from '../../types/projects';
import { projectSchema, getDefaultCustomization } from '../../schema/projectSchema';
import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import { SessionType } from '../../types/session';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
import { projectLandmarksSchema } from '../../schema/projectLandmarksSchema';
import { communitySchema } from '../../schema/communitySchema';
import { unitSchema } from '../../schema/UnitSchema';
import { unitplanSchema } from '../../schema/unitplanSchema';
import { trashModule } from '../trash';
import { trashType } from '../../types/trash';
import { invalidateCacheByPattern } from '../cdn';
import { AmenityModule } from '../amenity';
import { UnitModule } from '../units';
import { buildingModule } from '../building';
import { communityModule } from '../community';
import { unitplanModule } from '../unitplan';
import { AssetsModule } from '../asset';
import { GalleryModule } from '../gallery';
import { ModelModule } from '../glbModel';
import { ProjectSceneModule } from '../projectScene';
import { ProjectLandmarkModule } from '../projectLandmark';
import { MiniMapModule } from '../miniMap';
import { VirtualTourModule } from '../virtualTour';
import { customTourModule } from '../customTour';
import { projectsvgSchema } from '../../schema/projectsvgSchema';
import { sidebarSchema } from '../../schema/sidebarSchema';
import { projectSceneType } from '../../types/projectScene';
import { StatusModule } from '../status';

export async function invalidateProjectAPIs (organization_id:string, project_id?:string ): Promise<any> {
  try {
    const apiPaths = [

      // Project apis
      `/publicapis/organization/${organization_id}/listProjectsFromOrganization`,
    ];

    // Add project-specific APIs if project_id is provided
    if (project_id) {
      apiPaths.push(
        `/publicapis/organization/${organization_id}/project/${project_id}/getProject`,
        `/publicapis/organization/${organization_id}/project/${project_id}/getCountforProject`,
      );
    }

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

export class ProjectModule {
  private model: mongoose.Model<Project>;
  public storagepath;
  constructor (organization_id: string) {
    this.model = mongoose.model<Project>(
      `${organization_id}${Models._PROJECTS}`,
      projectSchema,
    );
    this.storagepath='CreationtoolAssets/'+organization_id+'/projects/';
  }
  public async CreateNewProject (
    createProjectObj: createProjectInput,
  ): Promise<Project | void> {
    console.log('CreateNewProject Called');

    logger.info('CreateNewProject Called', {createProjectObj: createProjectObj});
    return new Promise((resolve, reject) => {
      if (
        !Object.values(ExperienceType).some((type) => createProjectObj.experience.includes(type))
      ) {
        logger.error('invalid type');
        reject('invalid type');
        return;
      }
      console.log('type of exp', typeof createProjectObj.experience);
      console.log('exp', createProjectObj.experience);

      const newProject: Project = {
        _id: createProjectObj.id,
        name: createProjectObj.name,
        experience: createProjectObj.experience,
        property_type: createProjectObj.property_type,
        description: createProjectObj.description,
        is_public: createProjectObj.is_public,
        organization_id: createProjectObj.organization_id,
        city: createProjectObj.city,
        country: createProjectObj.country,
        project_thumbnail: createProjectObj.project_thumbnail,
        unique_project_id: createProjectObj.unique_project_id,
        projectSettings: {
          ...((createProjectObj as any).statusCategory && { statusCategory: (createProjectObj as any).statusCategory }),
          salestool: {is_enabled: false, session_duration: 30, default_experience: SessionType.ALE},
          general: {
            is_enabled: true, slots: [
              '2024-02-29T20:00:00.000Z',
              '2024-02-29T10:00:00.000Z',
              '2024-02-29T10:30:00.000Z',
              '2024-02-29T19:00:00.000Z',
              '2024-03-01T11:30:00.000Z',
              '2024-02-29T19:30:00.000Z',
              '2024-02-29T20:15:00.000Z',
              '2024-02-29T23:00:00.000Z',
            ],
            timezone: 'Asia/Kolkata',
            hideStatus: false,
          },
          pixelstreaming: {is_enabled: false, max_concurrent_sessions: 100000},
          ale: {
            is_enabled: false,
            default_language: 'en',
            currency_support: false,
            svg_visibility: false,
            share_scenes: {
              whatsapp: false,
              email: false,
              twitter: false,
              // Instagram: false,
              // Facebook: false,
            },
            unit_card_customize_type: unitCardType.DEFAULT,
            unitcard_config: {
              type: true,
              measurement: true,
              bedrooms: true,
              bathrooms: true,
              status: true,
              price: true,
              view: true,
              maid: true,
              floor_id: true,
              building_id: true,
              style: true,
              units: true,
              favIcon: true,
            },
            is_cta_enabled: false,
            is_call_enabled: false,
            cta_type: ctaType.DEFAULT,
            cta_name: 'Book Unit',
            is_unitplan_cta_enabled: false,
            unitplan_cta_type: ctaType.DEFAULT,
            unitplan_cta_name: 'Book Unit',
            measurement_type: 'sqft',
            bedroom_format: 'BR',
            unit_label: true,
            is_logo_clickable: false,
            logo_click_type: logoType.DEFAULT,
          },
          embed: {is_enabled: false},
          theme: {
            theme: createProjectObj.theme,
            primary: createProjectObj.primary,
            secondary: createProjectObj.secondary,
            primary_text: createProjectObj.primary_text,
            secondary_text: createProjectObj.secondary_text,
            font_type: createProjectObj.font_type,
            font_url: createProjectObj.font_url,
          },
          customization: {
            status_config: {
              'available': {
                color: {
                  mode: 'transparent',
                  defaultColor: StatusDefaultColor.AVAILABLE_TRANSPARENT,
                },
                interaction: 'visible_clickable',
              },
              'onhold': {
                color: {
                  mode: 'default',
                  defaultColor: StatusDefaultColor.ONHOLD_TRANSPARENT,
                },
                interaction: 'visible_clickable',
              },
              'not available': {
                color: {
                  mode: 'default',
                  defaultColor: StatusDefaultColor.NOT_AVAILABLE_TRANSPARENT,
                },
                interaction: 'visible_clickable',
              },
            },
          },
        },
      };
      const project = new this.model(newProject);
      project
        .save()
        .then(() => {
          logger.info('CreateNewProject Successfull', {newProject: newProject});
          resolve(newProject);
        })
        .catch((err) => {
          logger.error('Error in CreateNewProject', {message: err});
          reject(err);
        });
    });
  }
  public async editProject (editProjectObj:editProjectInput):Promise<Project| null>{
    logger.info('editProject Called', {editProjectInput: editProjectObj});

    const updateProjectObj:any ={
      ['name']: editProjectObj.name,
      ['experience']: editProjectObj.experience,
      ['property_type']: editProjectObj.property_type,
      ['project_thumbnail']: editProjectObj.project_thumbnail,
      ['city']: editProjectObj.city,
      ['country']: editProjectObj.country,
    };
    if (editProjectObj.unique_project_id !== undefined) {
      updateProjectObj.unique_project_id = editProjectObj.unique_project_id;
    }

    const updatedProject = await this.model.findOneAndUpdate(
      { _id: editProjectObj.id },
      {
        $set: updateProjectObj,
      },
      { new: true },
    );
    if (!updatedProject) {
      logger.error('Unable to find existing project');
      throw new Error('Unable to find existing project');
      return null;
    }

    logger.info('updateProjectSettings Successfull', {updateProject: updatedProject});
    return updatedProject;
  }

  public async getProjectById (project_id: string): Promise<Project | null> {
    logger.info('getProjectById Called', {project_id: project_id});
    const query = {
      _id: project_id,
    };

    let project = await this.model.findOne(query);

    if (project) {
      // Always sync statusCategory from status collection to get latest substatuses
      const statusModule = new StatusModule(project_id);
      const statuses = await statusModule.getStatus();

      // Convert array to object format for projectSettings.statusCategory.data
      const statusesObj = (statuses || []).reduce(
        (acc: Record<string, any>, s: any) => {
          acc[String(s._id)] = s;
          return acc;
        },
        {},
      );

      const existingStatusType = project?.projectSettings?.statusCategory?.status_type || 'default';
      const statusCategoryStructure = {
        status_type: existingStatusType,
        data: statusesObj,
      };

      // Update the DB with latest status list from status collection
      await this.model.findOneAndUpdate(
        { _id: project_id },
        { $set: { 'projectSettings.statusCategory': statusCategoryStructure } },
      );
      // Re-fetch the project to get latest status in DB
      project = await this.model.findOne(query);
    }

    logger.info('getProjectById Successfull', {project: project});
    return project as Project | null;
  }

  public async updateProjectSettings (
    project_id: string,
    query: ProjectSettingsType,
    updateProject: updateProjectInput,
  ): Promise<Project | null> {
    logger.info('updateProjectSettings Called', {project_id: project_id, query: query, updateProject: updateProject});
    try {
      let updateProjectObj={};
      // Find the project by ID
      const existingProject = await this.model.findById(project_id);
      if (!existingProject) {
        logger.error('Unable to find existing project');
        throw new Error('Unable to find existing project');
      }
      for (const key of Object.keys(query)) {

        switch (key) {
          case 'general':
            updateProjectObj = {
              ...updateProjectObj,
              [`projectSettings.${key}.slots`]: query[key].slots,
              [`projectSettings.${key}.is_enabled`]: query[key].is_enabled,
              [`projectSettings.${key}.branding_logo`]: query[key].branding_logo,
              [`projectSettings.${key}.branding_logo_dark`]: query[key].branding_logo_dark,
              [`projectSettings.${key}.lat`]: query[key].lat,
              [`projectSettings.${key}.long`]: query[key].long,
              ['description']: updateProject.description,
              [`projectSettings.${key}.hideStatus`]: query[key].hideStatus,
              [`projectSettings.${key}.timezone`]: query[key].timezone,
              [`projectSettings.${key}.updated_at`]: query[key].updated_at,

            };
            break;
          case 'ale': {
            let shareScene_object;

            if (typeof existingProject?.projectSettings?.ale?.share_scenes === 'boolean') {
              shareScene_object = {
                whatsapp: existingProject?.projectSettings?.ale?.share_scenes,
                email: existingProject?.projectSettings?.ale?.share_scenes,
                twitter: existingProject?.projectSettings?.ale?.share_scenes,
                // Instagram: existingProject?.projectSettings?.ale?.share_scenes,
                // Facebook: existingProject?.projectSettings?.ale?.share_scenes,
              };
            }

            if (query[key]?.share_scenes && typeof query[key]?.share_scenes === 'object') {
              shareScene_object = {
                ...(shareScene_object ?? existingProject?.projectSettings?.ale?.share_scenes ?? {}),
                ...query[key]?.share_scenes,
              };
            }

            if (shareScene_object) {
              updateProjectObj = {
                ...updateProjectObj,
                [`projectSettings.${key}.share_scenes`]: shareScene_object,
              };
            }
            updateProjectObj = {
              ...updateProjectObj,
              [`projectSettings.${key}.is_enabled`]: query[key].is_enabled,
              [`projectSettings.${key}.initial_scene_type`]: query[key].initial_scene_type,
              [`projectSettings.${key}.initial_scene_id`]: query[key].initial_scene_id,
              [`projectSettings.${key}.is_logo_clickable`]: query[key].is_logo_clickable,
              [`projectSettings.${key}.logo_click_type`]: query[key].logo_click_type,
              [`projectSettings.${key}.logo_click_link`]: query[key].logo_click_type === (logoType.CUSTOM as string) ? query[key].logo_click_link:'',
              [`projectSettings.${key}.session_duration`]: updateProject.session_duration,
              [`projectSettings.${key}.welcome_video`]: query[key].welcome_video,
              [`projectSettings.${key}.welcome_thumbnail`]: query[key].welcome_thumbnail,
              [`projectSettings.${key}.shortened_link`]: query[key].shortened_link,
              [`projectSettings.${key}.default_language`]: query[key].default_language,
              [`projectSettings.${key}.supported_languages`]: query[key].supported_languages,
              [`projectSettings.${key}.cta_name`]: query[key].cta_name,
              [`projectSettings.${key}.cta_type`]: query[key].cta_type,
              [`projectSettings.${key}.currency_support`]: query[key].currency_support,
              [`projectSettings.${key}.unit_label`]: query[key].unit_label,
              [`projectSettings.${key}.svg_visibility`]: query[key].svg_visibility,
              [`projectSettings.${key}.is_cta_enabled`]: query[key].is_cta_enabled,
              [`projectSettings.${key}.is_call_enabled`]: query[key].is_call_enabled,
              [`projectSettings.${key}.is_unitplan_cta_enabled`]: query[key].is_unitplan_cta_enabled,
              [`projectSettings.${key}.unitplan_cta_type`]: query[key].unitplan_cta_type,
              [`projectSettings.${key}.unitplan_cta_name`]: query[key].unitplan_cta_name,
              [`projectSettings.${key}.unitplan_cta_link`]: query[key].unitplan_cta_link,
              [`projectSettings.${key}.bedroom_format`]: query[key].bedroom_format,
              [`projectSettings.${key}.measurement_type`]: query[key].measurement_type,
              [`projectSettings.${key}.unit_card_customize_type`]: query[key].unit_card_customize_type,
              [`projectSettings.${key}.unitcard_config.type`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.type: true,
              [`projectSettings.${key}.unitcard_config.measurement`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.measurement: true,
              [`projectSettings.${key}.unitcard_config.bedrooms`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.bedrooms: true,
              [`projectSettings.${key}.unitcard_config.bathrooms`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.bathrooms: true,
              [`projectSettings.${key}.unitcard_config.status`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.status: true,
              [`projectSettings.${key}.unitcard_config.price`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.price: true,
              [`projectSettings.${key}.unitcard_config.maid`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.maid: true,
              [`projectSettings.${key}.unitcard_config.view`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.view: true,
              [`projectSettings.${key}.unitcard_config.floor_id`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.floor_id: true,
              [`projectSettings.${key}.unitcard_config.building_id`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.building_id: true,
              [`projectSettings.${key}.unitcard_config.style`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.style: true,
              [`projectSettings.${key}.unitcard_config.units`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.units: true,
              [`projectSettings.${key}.unitcard_config.favIcon`]: query[key].unit_card_customize_type
              === unitCardType.CUSTOM ? query[key].unitcard_config?.favIcon: true,
            };

            break;
          }
          case 'pixelstreaming':
            updateProjectObj = {
              ...updateProjectObj,
              [`projectSettings.${key}.max_concurrent_sessions`]: query[key].max_concurrent_sessions,
              [`projectSettings.${key}.is_enabled`]: query[key].is_enabled,
              [`projectSettings.${key}.session_duration`]: query[key].session_duration,
              [`projectSettings.${key}.pixel_streaming_endpoint`]: query[key].pixel_streaming_endpoint,
              [`projectSettings.${key}.resource_group`]: query[key].resource_group,
              [`projectSettings.${key}.vm_scaleset_name`]: query[key].vm_scaleset_name,
              [`projectSettings.${key}.min_instances`]: query[key].min_instances,
              [`projectSettings.${key}.auto_scale`]: query[key].auto_scale,
              [`projectSettings.${key}.application_id`]: query[key].application_id,
            };

            if (query[key].generated_key) {
              const existingGeneratedKeys = existingProject?.projectSettings?.pixelstreaming?.generated_key || [];
              updateProjectObj = {
                ...updateProjectObj,
                [`projectSettings.${key}.generated_key`]: [
                  ...existingGeneratedKeys,
                  {
                    key: query[key].generated_key,
                    time: new Date().toISOString(),
                  },
                ],
              };
            }
            break;
          case 'embed':
            updateProjectObj = {
              ...updateProjectObj,
              [`projectSettings.${key}.is_enabled`]: query[key].is_enabled,
            };
            break;
          case 'salestool':
            updateProjectObj = {
              ...updateProjectObj,
              [`projectSettings.${key}.is_enabled`]: query[key].is_enabled,
              [`projectSettings.${key}.default_experience`]: query[key].default_experience,
              [`projectSettings.${key}.tags`]: query[key].tags,
            };
            break;
          case 'theme':
            updateProjectObj = {
              ...updateProjectObj,
              [`projectSettings.${key}.theme`]: query[key].theme,
              [`projectSettings.${key}.primary`]: query[key].primary,
              [`projectSettings.${key}.primary_text`]: query[key].primary_text,
              [`projectSettings.${key}.secondary`]: query[key].secondary,
              [`projectSettings.${key}.secondary_text`]: query[key].secondary_text,
              [`projectSettings.${key}.font_type`]: query[key].font_type,
              [`projectSettings.${key}.font_url`]: updateProject.font_url,
            };
            break;
          case 'hologram': {
            const hologramData = (query as any)[key];
            if (hologramData) {
              updateProjectObj = {
                ...updateProjectObj,
                [`projectSettings.${key}.project_logo`]: hologramData.hologram_project_logo,
                [`projectSettings.${key}.project_location`]: hologramData.project_location,
                [`projectSettings.${key}.project_type`]: hologramData.project_type,
                [`projectSettings.${key}.amount`]: hologramData.amount,
                [`projectSettings.${key}.bedrooms`]: hologramData.bedrooms,
                [`projectSettings.${key}.thumbnail`]: hologramData.thumbnail,
                [`projectSettings.${key}.file`]: hologramData.file,
                [`projectSettings.${key}.tags`]: hologramData.tags,
              };
            }
            break;
          }
          case 'metadata':
            updateProjectObj = {
              ...updateProjectObj,
              [`projectSettings.${key}`]: query[key],
            };
            break;
          case 'statusCategory':
            updateProjectObj = {
              ...updateProjectObj,
              [`projectSettings.${key}.status_type`]: (query as any)[key]?.status_type,
            };
            break;
          case 'customization': {
            const customizationData = (query as any)[key];
            if (customizationData?.status_config) {
              const payloadStatusConfig = customizationData.status_config;
              const defaultCustomization = getDefaultCustomization();
              const existingStatusConfig = (existingProject as any)?.projectSettings?.customization?.status_config
                || defaultCustomization.status_config;

              // Merge payload into existing so we only update sent keys and preserve others
              const mergeStatusEntry = (existing: any, payload: any, defaultEntry: any) => {
                if (!payload) {
                  return existing?.color?.mode?existing : defaultEntry;
                }
                return {
                  color: {
                    ...(existing?.color || defaultEntry?.color),
                    ...(payload.color && {
                      mode: payload.color.mode ?? existing?.color?.mode ?? defaultEntry?.color?.mode,
                      defaultColor: existing?.color?.defaultColor ?? defaultEntry?.color?.defaultColor,
                      selectedColor: payload.color.selectedColor ?? existing?.color?.selectedColor,
                    }),
                  },
                  interaction: payload.interaction ?? existing?.interaction ?? defaultEntry?.interaction,
                };
              };

              const mergedStatusConfig = {
                available: mergeStatusEntry(existingStatusConfig?.available, payloadStatusConfig?.available, defaultCustomization.status_config.available),
                onhold: mergeStatusEntry(existingStatusConfig?.onhold, payloadStatusConfig?.onhold, defaultCustomization.status_config.onhold),
                'not available': mergeStatusEntry(existingStatusConfig?.['not available'], payloadStatusConfig?.['not available'], defaultCustomization.status_config['not available']),
              };
              updateProjectObj = {
                ...updateProjectObj,
                [`projectSettings.${key}`]: {
                  ...(existingProject as any)?.projectSettings?.customization,
                  status_config: mergedStatusConfig,
                },
              };
            }
            break;
          }
          default:
            logger.error('Invalid project settings type');
            throw new Error('Invalid project settings type');
        }
      }
      // Save the updated project
      const updatedProject = await this.model.findOneAndUpdate(
        { _id: project_id },
        {
          $set: updateProjectObj,
        },
        { new: true },
      );
      logger.info('updateProjectSettings Successfull', {updateProject: updatedProject});
      return updatedProject;
    } catch (error) {
      logger.error('Error in updating project', {message: error});
      throw new Error(`Error in updating project: ${error}`);
    }
  }
  public async GetListOfAllProjects (): Promise<Record<string, Project>>{
    logger.info('GetListOfAllProjects Called');
    const projects:Array<UnknownObject>= await this.model.find();
    const projectObj = arrayToObject(projects) as Record<string, Project>;
    logger.info('GetListOfAllProjects Successfull', {projectObj: projectObj});
    return projectObj;
  }
  public async GetProjectInOrganization (
    project_id: string,
  ): Promise<Project | null> {
    logger.info('GetProjectInOrganization Called');
    try {
      const project = await this.model.findOne({ _id: project_id });
      if (project) {
        logger.info('GetListOfAllProjects Successfull', {project: project});
        return project as Project;
      }
      logger.error('Project data not found');
      throw new Error('Project data not found');
    } catch (error) {
      logger.error('Project data not found', {message: error});
      throw new Error('Error while getting project data: ' + error);
    }
  }

  public async ListProjectsFromOrganization (): Promise<object> {
    logger.info('ListProjectsFromOrganization Called');
    const projects: UnknownObject[] = await this.model.find({});
    const projectsObj = arrayToObject(projects) as Record<string, Project>;
    Object.keys(projectsObj).forEach((projectId) => {
      const project = projectsObj[projectId];
      if (project.projectSettings?.ale) {
        const ale = project.projectSettings.ale;
        if (!ale.measurement_type) {
          ale.measurement_type = 'sqft';
        }
        if (!ale.bedroom_format) {
          ale.bedroom_format = 'BR';
        }
      }
    });
    logger.info('ListProjectsFromOrganization Successfull', {projectsObj: projectsObj});
    return projectsObj;
  }
  public async FilterProjectsFromOrganization
  (filters: { experience?: string | string[]; country?: string | string[],
     property_type?:PropertyType  }, page:number)
   : Promise<object> {
    logger.info('ListProjectsFromOrganization Called', { filters });
    const limit = 10;
    const skip = (page - 1) * limit;

    const total = await this.model.countDocuments({
      ...(filters.experience && {
        experience: Array.isArray(filters.experience) ? { $in: filters.experience } : filters.experience,
      }),
      ...(filters.country && { country: filters.country }),
      ...(filters.property_type && { property_type: filters.property_type }),
    });
    const projects: UnknownObject[] = await this.model.find({
      ...(filters.experience && {
        experience: Array.isArray(filters.experience) ? { $in: filters.experience } : filters.experience,
      }),
      ...(filters.country && { country: filters.country }),
      ...(filters.property_type && { property_type: filters.property_type }),
    })
      .sort({ _id: -1 }).skip(skip).limit(limit).lean();
    const projectsObj = arrayToObject(projects) as Record<string, Project>;
    logger.info('ListProjectsFromOrganization Successful', { projectsObj });
    return {total, projectsObj };
  }

  public async updateGallerySettings (payload: bulkGalleryUpdateType): Promise<string> {
    logger.info('updateGallerySettings Called');
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<Project | null>[] = payload.query.map(async (item) => {
        return this.model.findOneAndUpdate(
          { _id: payload.project_id },
          {
            $set: {
              [`projectSettings.gallery.${item.id}`]: {
                id: item.id,
                order: item.order,
                name: item.name,
              },
            },
          },
          { new: true }, // Return the updated document
        )
          .then((res) => {
            logger.info('updateGallerySettings Successfull', {response: res});
            return res;
          })
          .catch(() => {

            return null;
          });
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('updateGallerySettings successfully', {results: results});
            resolve('Documents updated successfully');
          } else {
            logger.error('Error while updating gallery settings');
            reject('Error while updating gallery settings');
          }
        })
        .catch((error) => {
          logger.error('Error in updateGallerySettings', {message: error});
          reject(error);
        });
    });
  }

  public async updateAmenitySettings (payload: bulkUpdateType): Promise<string> {
    logger.info('updateAmenitySettings Called');
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<Project | null>[] = payload.query.map(async (item) => {
        return this.model.findOneAndUpdate(
          { _id: payload.project_id },
          {
            $set: {
              [`projectSettings.amenity.${item.id}`]: {
                id: item.id,
                order: item.order,
                name: item.name,
              },
            },
          },
          { new: true }, // Return the updated document
        )
          .then((res) => {
            logger.info('updateAmenitySettings Successfull', {response: res});
            return res;
          })
          .catch(() => {

            return null;
          });
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('updateAmenitySettings successfully', {results: results});
            resolve('Documents updated successfully');
          } else {
            logger.error('Error while updating Amenity settings');
            reject('Error while updating Amenity settings');
          }
        })
        .catch((error) => {
          logger.error('Error in updateAmenitySettings', {message: error});
          reject(error);
        });
    });
  }

  public async deleteSettings (project_id:string, ids: string[], type:string): Promise<string>{
    logger.info('updateAmenitySettings Called');
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<Project | null>[] = ids.map(async (item) => {
        return this.model.findOneAndUpdate(
          { _id: project_id },
          { $unset: { [`projectSettings.${type}.${item}`]: 1 } }, // Return the updated document
        )
          .then((res) => {
            logger.info('Delete Amenity Settings Successfull', {response: res});
            return res;
          })
          .catch(() => {

            return null;
          });
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('AmenitySettings successfully', {results: results});
            resolve('Documents Deleted successfully');
          } else {
            logger.error('Error while updating Amenity settings');
            reject('Error while updating Amenity settings');
          }
        })
        .catch((error) => {
          logger.error('Error in updateAmenitySettings', {message: error});
          reject(error);
        });
    });
  }
  public async getVMDetails (
    project_id: string,
  ): Promise<projectVMDetails | null> {
    logger.info('getVMDetails Called');
    try {
      const project = await this.model.findOne({ _id: project_id });
      if (project) {
        logger.info('Project found successfully', {project: project});
        if (project?.projectSettings?.pixelstreaming) {
          logger.info('Project found successfully', {project: project});
          const { resource_group, vm_scaleset_name, min_instances, auto_scale }
          = project.projectSettings.pixelstreaming;

          const vmdetails = {
            resourceGroupName: resource_group,
            vmScaleSetName: vm_scaleset_name,
            minInstanceCount: min_instances, // Adding the new field here,
            autoScale: auto_scale,
          };

          return vmdetails;
        }
      }
      logger.error('Project data not found');
      throw new Error('Project data not found');
    } catch (error) {
      logger.error('Project data not found', {message: error});
      throw new Error('Error while getting project data: ' + error);
    }
  }
  public async getProjectTranslationData (project_id: string): Promise<string[]> {
    // Define models dynamically based on the project ID
    const ProjectLandmark = mongoose.model(`${project_id}${Models._LANDMARKS}`, projectLandmarksSchema);
    const Community = mongoose.model(`${project_id}${Models._COMMUNITIES}`, communitySchema);
    const Unit = mongoose.model(`${project_id}${Models._UNITS}`, unitSchema);
    const UnitPlan = mongoose.model(`${project_id}${Models._UNITPLANS}`, unitplanSchema);

    // Fetch distinct values from each field
    const projectLandmarksName = await ProjectLandmark.distinct('name');
    const projectLandmarksDescription = await ProjectLandmark.distinct('description');
    const communitiesName = await Community.distinct('name');
    const communitiesCategory = await Community.distinct('category');
    const unitsStyle = await Unit.distinct('style');
    const unitplansName = await UnitPlan.distinct('name');
    const unitplansStyle = await UnitPlan.distinct('style');
    const uniqueBedrooms = await Unit.distinct('bedrooms');
    const uniqueStyleTypes = await Unit.distinct('style');

    // Append all distinct values into a single array
    const allValues: string[] = [
      ...projectLandmarksName,
      ...projectLandmarksDescription,
      ...communitiesName,
      ...communitiesCategory,
      ...unitsStyle,
      ...unitplansName,
      ...unitplansStyle,
      ...uniqueBedrooms.map(String), // Convert non-string values to strings
      ...uniqueStyleTypes,
    ];

    return allValues;
  }

  public async getUniqueProjectIdsFromOrganization ():Promise<string[]>{
    try {
      const ProjectId = await this.model.distinct('_id');
      return ProjectId;
    } catch (error){
      logger.error('Error in getUniqueProjectIdsFromOrganization', { error });
      throw error;
    }
  }
  public async moveToTrash (
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<Project | void> {
    logger.info('moveToTrash Successfull',
      {project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: project_id,
    });
    if (documents.length === 0) {
      logger.error('Project ID provided not found');
      throw 'Project ID provided not found';
    }
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    const ProjectDataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${organization_id.toLowerCase()}_project`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await trash.addtoTrash(ProjectDataToInsertObj)
      .then(async () => {
        await this.model.deleteOne(
          {
            _id: project_id,
          },
        )
          .then(() => {
            logger.info('moveToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }

  public async restoreProjects (
    organization_id : string,
    trash_id : string,
  ): Promise<void>{
    logger.info('restoreProject Called',
      {organization_id: organization_id, trash_Id: trash_id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_id);
    const restoredData = await trash.restoreData(trash_id);
    if (restoredData){
      const createProjectPromise = Object.values(restoredData.data).map(async (item) => {
        const project = new this.model(item);
        await project.save();
      });
      await Promise.all([
        createProjectPromise,
      ])
        .then(async () => {
          await trash.deleteTrash(trash_ids);
          logger.info('Project Restored Successfull');
          return;
        })
        .catch((err) => {
          logger.error('Error in restoreProjects', {message: err});
          throw new Error('Failed to restore project data from trash');
        });
    } else {
      logger.error('Invalid TrashId');
      throw new Error('Invalid TrashId');
    }
  }

  /**
   * Export all project-related data to JSON format
   * Exports all modules associated with a project
   */
  public async exportProjectData (project_id: string, organization_id: string): Promise<{
    export_metadata: {
      source_project_id: string;
      source_organization_id: string;
      exported_at: string;
      version: string;
    };
    project_data: UnknownObject;
    modules: {
      units?: UnknownObject[];
      amenities?: UnknownObject[];
      buildings?: UnknownObject[];
      communities?: UnknownObject[];
      unitplans?: UnknownObject[];
      assets?: UnknownObject[];
      gallery?: UnknownObject[];
      models?: UnknownObject[];
      scenes?: UnknownObject[];
      landmarks?: UnknownObject[];
      svgs?: UnknownObject[];
      minimap?: UnknownObject[];
      sidebar?: UnknownObject[];
      virtual_tour?: UnknownObject[];
      custom_tour?: UnknownObject[];
    };
  }> {
    logger.info('exportProjectData called', { project_id, organization_id });

    // Verify project exists
    const project = await this.getProjectById(project_id);
    if (!project) {
      throw new Error('Project not found');
    }

    // Helper function to get all documents from a collection
    const getAllDocuments = async (
      collectionName: string,
      schema: mongoose.Schema,
    ): Promise<UnknownObject[]> => {
      try {
        const model = mongoose.model(collectionName, schema);
        const documents = await model.find().lean();
        return documents as UnknownObject[];
      } catch (error) {
        logger.warn(`Collection ${collectionName} not found or empty`, { error });
        return [];
      }
    };

    // Initialize export data structure
    const exportData: {
      export_metadata: {
        source_project_id: string;
        source_organization_id: string;
        exported_at: string;
        version: string;
      };
      project_data: UnknownObject;
      modules: {
        units?: UnknownObject[];
        amenities?: UnknownObject[];
        buildings?: UnknownObject[];
        communities?: UnknownObject[];
        unitplans?: UnknownObject[];
        assets?: UnknownObject[];
        gallery?: UnknownObject[];
        models?: UnknownObject[];
        scenes?: UnknownObject[];
        landmarks?: UnknownObject[];
        svgs?: UnknownObject[];
        minimap?: UnknownObject[];
        sidebar?: UnknownObject[];
        virtual_tour?: UnknownObject[];
        custom_tour?: UnknownObject[];
      };
    } = {
      export_metadata: {
        source_project_id: project_id,
        source_organization_id: organization_id,
        exported_at: new Date().toISOString(),
        version: '1.0',
      },
      project_data: project as unknown as UnknownObject,
      modules: {},
    };

    // Export Units
    try {
      const unitModule = new UnitModule(project_id);
      const units = await unitModule.getAllUnits();
      if (units && typeof units === 'object') {
        exportData.modules.units = Object.values(units) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting units', { error });
      exportData.modules.units = [];
    }

    // Export Amenities
    try {
      const amenityModule = new AmenityModule(project_id, organization_id);
      const amenities = await amenityModule.GetAmenities('');
      if (amenities && typeof amenities === 'object') {
        exportData.modules.amenities = Object.values(amenities) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting amenities', { error });
      exportData.modules.amenities = [];
    }

    // Export Buildings
    try {
      const buildingModuleInstance = new buildingModule(project_id, organization_id);
      const buildings = await buildingModuleInstance.GetListOfBuildings();
      if (buildings && typeof buildings === 'object') {
        exportData.modules.buildings = Object.values(buildings) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting buildings', { error });
      exportData.modules.buildings = [];
    }

    // Export Communities
    try {
      const communityModuleInstance = new communityModule(project_id, organization_id);
      const communities = await communityModuleInstance.getCommunities();
      if (communities && typeof communities === 'object') {
        exportData.modules.communities = Object.values(communities) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting communities', { error });
      exportData.modules.communities = [];
    }

    // Export Unitplans
    try {
      const unitplanModuleInstance = new unitplanModule(project_id, organization_id);
      const unitplans = await unitplanModuleInstance.getListOfUnitplan();
      if (unitplans && typeof unitplans === 'object') {
        exportData.modules.unitplans = Object.values(unitplans) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting unitplans', { error });
      exportData.modules.unitplans = [];
    }

    // Export Assets
    try {
      const assetsModule = new AssetsModule(project_id, organization_id);
      const assetsResult = await assetsModule.getListofAssets(1, 10000);
      if (assetsResult && assetsResult.items && typeof assetsResult.items === 'object') {
        exportData.modules.assets = Object.values(assetsResult.items) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting assets', { error });
      exportData.modules.assets = [];
    }

    // Export Gallery
    try {
      const galleryModule = new GalleryModule(project_id, organization_id);
      const gallery = await galleryModule.GetGallery('');
      if (gallery && typeof gallery === 'object') {
        exportData.modules.gallery = Object.values(gallery) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting gallery', { error });
      exportData.modules.gallery = [];
    }

    // Export Models (GLB)
    try {
      const modelModule = new ModelModule(project_id, organization_id);
      const models = await modelModule.getModels();
      if (models && typeof models === 'object') {
        exportData.modules.models = Object.values(models) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting models', { error });
      exportData.modules.models = [];
    }

    // Export Project Scenes
    try {
      const sceneModule = new ProjectSceneModule(project_id, organization_id);
      const scenes = await sceneModule.getAllScenes();
      if (!scenes || typeof scenes !== 'object') {
        exportData.modules.scenes = [];
      } else {
        const scenesArray: UnknownObject[] = [];

        // Helper function to export frames for rotatable_image scenes
        const exportRotatableImageFrames = async (
          sceneData: UnknownObject,
          sceneModel: mongoose.Model<any>,
          sceneArray: UnknownObject[],
        ): Promise<void> => {
          if (sceneData.type !== projectSceneType.ROTATABLE_IMAGE) {
            return;
          }

          try {
            /* eslint-disable dot-notation */
            const sceneId = String(sceneData._id);
            const frames = await sceneModel.find({
              type: projectSceneType.ROTATABLE_IMAGE_FRAME,
              parent: sceneId,
            });

            if (!frames || frames.length === 0) {
              return;
            }

            for (const frame of frames) {
              const frameData = frame.toObject();
              sceneArray.push(frameData as unknown as UnknownObject);
            }

            logger.info('Exported rotatable_image frames', {
              scene_id: sceneId,
              frame_count: frames.length,
            });
          } catch (frameError) {
            logger.warn('Error exporting rotatable_image frames', {
              scene_id: sceneData._id ? String(sceneData._id) : 'unknown',
              error: frameError,
            });
          }
        };

        /* eslint-disable no-unused-vars */
        for (const [, sceneObj] of Object.entries(scenes)) {
          const sceneData = (sceneObj as UnknownObject).sceneData as UnknownObject;
          if (!sceneData || typeof sceneData !== 'object' || !('_id' in sceneData)) {
            continue;
          }

          scenesArray.push(sceneData as UnknownObject);
          /* eslint-disable dot-notation */
          await exportRotatableImageFrames(sceneData, sceneModule['model'], scenesArray);
        }

        exportData.modules.scenes = scenesArray;
      }
    } catch (error) {
      logger.warn('Error exporting scenes', { error });
      exportData.modules.scenes = [];
    }

    // Export Project Landmarks
    try {
      const landmarkModule = new ProjectLandmarkModule(project_id, organization_id);
      const landmarks = await landmarkModule.getListofLandmark();
      if (landmarks && typeof landmarks === 'object') {
        exportData.modules.landmarks = Object.values(landmarks) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting landmarks', { error });
      exportData.modules.landmarks = [];
    }

    // Export Project SVG
    try {
      const svgData = await getAllDocuments(
        `${project_id}${Models._SVGS}`,
        projectsvgSchema,
      );
      exportData.modules.svgs = svgData;
    } catch (error) {
      logger.warn('Error exporting SVGs', { error });
      exportData.modules.svgs = [];
    }

    // Export MiniMap
    try {
      const minimapModule = new MiniMapModule(project_id, organization_id);
      const minimap = await minimapModule.GetListOfMiniMap();
      if (minimap && typeof minimap === 'object') {
        exportData.modules.minimap = Object.values(minimap) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting minimap', { error });
      exportData.modules.minimap = [];
    }

    // Export Sidebar
    try {
      const sidebarData = await getAllDocuments(
        `${project_id}${Models._SIDEBAR}`,
        sidebarSchema,
      );
      exportData.modules.sidebar = sidebarData;
    } catch (error) {
      logger.warn('Error exporting sidebar', { error });
      exportData.modules.sidebar = [];
    }

    // Export Virtual Tour
    try {
      const virtualTourModule = new VirtualTourModule(organization_id, project_id);
      const virtualTours = await virtualTourModule.GetAllTours();
      if (virtualTours && typeof virtualTours === 'object') {
        exportData.modules.virtual_tour = Object.values(virtualTours) as UnknownObject[];
      }
    } catch (error) {
      logger.warn('Error exporting virtual tours', { error });
      exportData.modules.virtual_tour = [];
    }

    // Export Custom Tour
    try {
      const virtualTourModule = new VirtualTourModule(organization_id, project_id);
      const virtualTours = await virtualTourModule.GetAllTours();
      const customTourData: UnknownObject[] = [];

      if (virtualTours && typeof virtualTours === 'object') {
        for (const tour of Object.values(virtualTours)) {
          const tourId = (tour as UnknownObject)._id?.toString();
          if (tourId) {
            try {
              const customTourModuleInstance = new customTourModule(
                organization_id,
                tourId,
                project_id,
              );
              const customTourImages = await customTourModuleInstance.getImages();
              if (customTourImages && typeof customTourImages === 'object') {
                customTourData.push(...(Object.values(customTourImages) as UnknownObject[]));
              }
            } catch (error) {
              logger.warn(`Error exporting custom tour for tour ${tourId}`, { error });
            }
          }
        }
      }
      exportData.modules.custom_tour = customTourData;
    } catch (error) {
      logger.warn('Error exporting custom tours', { error });
      exportData.modules.custom_tour = [];
    }

    logger.info('exportProjectData completed successfully', {
      project_id,
      organization_id,
      modules_exported: Object.keys(exportData.modules).length,
    });

    return exportData;
  }
}

import logger from '../../config/logger';
import { generateFloors } from '../../helpers/building';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { buildingSchema } from '../../schema/buildingSchema';
import { Building, bulkFloorUpdateType, CreateBuildingType, Floor, updateBuildingType } from '../../types/building';
import mongoose from 'mongoose';
import { trashModule } from '../trash/index';
import { trashType } from '../../types/trash';
import { Models } from '../../types/extras';
import { invalidateCacheByPattern } from '../cdn';

export async function invalidateBuildingAPIs (organization_id:string, project_id:string ): Promise<any> {
  try {

    const apiPaths = [
      // Building apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getListOfBuildings`,
      `/publicapis/organization/${organization_id}/project/${project_id}/getCountforBuilding`,
      `/publicapis/organization/${organization_id}/project/${project_id}/getFloorDetails`,

      // Project apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getProject`,
      `/publicapis/organization/${organization_id}/project/${project_id}/getCountforProject`,

      // Community apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getCommunities`,
    ];

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

export class buildingModule {
  private model: mongoose.Model<Building>;

  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<Building>(
      `${project_id}${Models._BUILDINGS}`,
      buildingSchema,
    );
    this.storagepath='CreationtoolAssets/'+organization_id+'/projects/'+project_id+'/buildings/';
  }

  public async CreateNewBuilding (
    payload:CreateBuildingType,
  ): Promise<Building | void> {
    logger.info('CreateNewBuilding Called', {payload: payload});
    return new Promise((resolve, reject) => {

      const floors: Record<string, Floor> = generateFloors(payload.total_floors) as Record<string, Floor>;

      const newBuilding: Building = {
        _id: new mongoose.Types.ObjectId(),
        project_id: payload.project_id,
        name: payload.name,
        type: payload.type,
        total_floors: payload.total_floors,
        floors: floors,
        community_id: payload.community_id,
        thumbnail: payload.thumbnail as string,
        updated_at: new Date().toISOString(),
      };

      const building = new this.model(newBuilding);
      building.save().then(() => {
        logger.info('CreateNewBuilding Successfull', {building: building});
        resolve(newBuilding);
      }).catch((err: string) => {
        logger.error('Error in CreateNewBuilding', {message: err});
        reject(err);
      });
    });
  }

  public async GetListOfBuildings (): Promise<object | null> {
    logger.info('GetListOfBuildings Called');
    const buildingLists: Array<UnknownObject> = await this.model.find();
    const buildingListObj = arrayToObject(buildingLists) as Record<string, Building>;
    logger.info('GetListOfBuildings Successfull', {buildingListObj: buildingListObj});
    return buildingListObj;
  }
  public async GetBuilding (building_id: string): Promise<Building | null> {
    logger.info('GetBuilding Called', {building_id: building_id});
    const query = { _id: building_id };
    const building = await this.model.findOne(query);
    logger.info('GetBuilding Successfull', {building: building});
    return building as Building | null;
  }

  public async DeleteBuilding
  (building_id: string,
  ): Promise<Building | null> {
    logger.info('DeleteBuilding Called', {building_id: building_id});
    return new Promise((resolve, reject) => {
      this.model.findOneAndDelete({ _id: building_id }).then((deletedBuilding:unknown) => {
        logger.info('DeleteBuilding Successfull', {deletedBuilding: deletedBuilding});
        resolve(deletedBuilding as Building);

      }).catch((err) => {
        logger.error('Error in DeleteBuilding', {message: err});
        reject(err);
      });
    });
  }

  public async updateBuilding (id: string, updateData: updateBuildingType): Promise<Building | null> {
    logger.info('UpdateBuilding Called', {building_id: id, payload: updateData});

    try {
      const { floors, ...basicUpdateData } = updateData;

      const finalPayload: any = {
        ...basicUpdateData,
        ...(floors && updateData.total_floors !== undefined ? {
          total_floors: Object.keys(floors).length,
        } : {}),

        // Add the floor objects using reduce
        ...(floors && updateData.total_floors !== undefined ?
          Object.keys(floors).reduce((acc: {[key: string]: any}, item) => {
            acc[`floors.${item}`] = {
              floor_id: item,
              floor_name: 'Floor ' + item.toUpperCase(),
              units: [],
              order: (updateData.total_floors || 0) + Object.keys(floors).indexOf(item) + 1,
            };
            return acc;
          }, {})
          : {}),
      };

      const updatedBuilding = await this.model.findOneAndUpdate(
        { _id: id },
        { $set: finalPayload },
        { new: true },
      );

      if (!updatedBuilding) {
        logger.error('UpdateBuilding Failed: Building not found', {building_id: id});
        throw new Error('Building not found or not authorized');
      }

      logger.info('UpdateBuilding Successful', {updatedBuilding: updatedBuilding});

      return updatedBuilding;

    } catch (error) {
      logger.error('UpdateBuilding Failed', {message: error});
      throw new Error(`Error updating building: ${error}`);
    }
  }

  public async bulkUpdateFloors (payload: bulkFloorUpdateType, building_id:string): Promise<string>{
    logger.info('bulkUpdateFloors Called', {payload: payload});
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<Building | null>[] = payload.query.map(async (item) => {
        return this.model.findOneAndUpdate(
          {
            _id: building_id,
            [`floors.${item.floor_id}`]: { $exists: true },
          },
          {
            $set: {
              [`floors.${item.floor_id}.order`]: item.order,
            },
          },
          {
            new: true, // Return the updated document
          },
        );
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('bulkUpdateFloors successfully', {results: results});
            resolve('Documents updated successfully');
          } else {
            logger.error('Error while updating sidebar option');
            reject('bulkUpdateFloors not updated');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
  public async UpdateFloor
  (
    payload: object,
    building_id: string,
    floor_id:string,
  ): Promise<Building | null> {
    logger.info('UpdateFloor Called', {building_id: building_id, payload: payload, floor_id: floor_id});
    return new Promise((resolve, reject) => {
      const update: { $set: Record<string, Record<string, string>> } = { $set: {} };
      for (const [key, value] of Object.entries(payload)) {
        update.$set[`floors.${floor_id}.${key}`] = value as Record<string, string>;
      }
      const query = {_id: building_id};
      this.model.findOneAndUpdate(query, update, {
        new: true,
      }).then((updatedBuilding) => {
        logger.info('UpdateFloor Successfull', {updatedBuilding: updatedBuilding});
        resolve(updatedBuilding);
      }).catch((err) => {
        logger.info('Error in UpdateFloor', {message: err});
        reject(err);
      });
    });
  }

  public async DeleteFloor (building_id:string, floor_id:string):Promise<string> {
    logger.info('deleteFloor Called', {building_id: building_id, floor_id: floor_id});

    try {
      const building = await this.model.findById(building_id);
      if (!building) {
        throw new Error('Building not found');
      }

      const floors = building.floors;
      const deletedFloor = floors[floor_id];
      if (!deletedFloor) {
        throw new Error('Floor not found');
      }

      const deletedOrder = deletedFloor.order;

      // Create a new floors object without the deleted floor
      const newFloors: { [key: string]: any } = {};

      for (const key in floors) {
        if (key !== floor_id) {
          const floor = { ...floors[key] };
          if (floor.order > deletedOrder) {
            floor.order -= 1;
          }
          newFloors[key] = floor;
        }
      }

      // Replace the entire floors object
      building.floors = newFloors;

      // Decrement total floors
      building.total_floors -= 1;
      await building.save();
      return 'Floor deleted and orders updated successfully';
    } catch (error){
      throw new Error(`Error in DeleteFloor:${error}`);
      logger.error('Error in DeleteFloor', {message: error});
    }
  }

  public async RenameFloor ( building_id: string, floor_id: string, name: string): Promise<string> {
    logger.info('renameFloor called', { building_id, floor_id, name });
    try {
      let updatedDocument ={} as Floor;
      const building = await this.model.findById(building_id);
      if (!building) {
        throw new Error('Building not found');
      }
      if (!building.floors || !building.floors[floor_id]) {
        throw new Error('Floor not found');
      }

      if (building.floors[name]){
        throw new Error('A floor with the same name already exists');
      }

      updatedDocument = building.floors[floor_id];
      updatedDocument.floor_id = name;
      updatedDocument.floor_name = `floor ${name}`;
      updatedDocument.updated_at = new Date().toISOString();

      await this.model.findOneAndUpdate(
        {
          _id: building_id,
          [`floors.${floor_id}`]: { $exists: true },
        },
        [
          {
            $set: {
              [`floors.${name}`]: updatedDocument,
            },
          },
          {
            $unset: `floors.${floor_id}`,
          },
        ],
        { new: true }, // Returns updated document
      );
      logger.info('Floor renamed successfully', { building_id, old_id: floor_id, new_id: name });
      return 'Floor Renamed Successfully';
    } catch (error) {
      logger.error('Error in RenameFloor', { message: error });
      throw new Error(`Error in RenameFloor: ${error instanceof Error ? error.message:''}`);
    }
  }

  public async AddNewFloor (
    building_id: string,
    floor_name: string,

  ): Promise<Building | null> {
    try {
      logger.info('AddNewFloor Called', { building_id, floor_name });

      // Find the building
      const building = await this.model.findById(building_id);

      if (!building) {
        logger.info('Building not found', { building_id });
        throw new Error('Building not found');
      }

      if (building.floors[floor_name]){
        throw new Error('A floor with the same name already exists');
      }

      // Find the highest order value in existing floors
      let highestOrder = 0;
      if (building.floors) {
        Object.values(building.floors).forEach((floor) => {
          if (floor.order > highestOrder) {
            highestOrder = floor.order;
          }
        });
      }

      // Create the new floor object
      const newFloor = {
        floor_id: floor_name,
        floor_name: `floor ${floor_name}`,
        units: [],
        order: highestOrder + 1,
        updated_at: new Date().toISOString(),
      };

      // Update the building with the new floor
      const updatedBuilding = await this.model.findByIdAndUpdate(
        building_id,
        {
          $set: {
            [`floors.${floor_name}`]: newFloor, //
            total_floors: building.total_floors + 1,
          },
        },
        { new: true },
      );
      logger.info('AddFloor Successful', { updatedBuilding });
      return updatedBuilding;
    } catch (error) {
      logger.error('Error in AddNewFloor', { message: error });
      throw new Error(`Error in AddNewFloor: ${error instanceof Error ? error.message:''}`);
    }
  }

  public async getBuildingsCount (community_id:string):Promise <number | null>{
    logger.info('getBuildingsCount Called', {community_id: community_id});
    try {
      const buildingsCount = await this.model.countDocuments({ community_id: community_id });
      logger.info('getBuildingsCount Successfull', {buildingsCount: buildingsCount});
      return buildingsCount;
    } catch (error) {
      logger.info('Error in getBuildingsCount', {message: error});
      return null;
    }
  }
  public async moveToTrash (
    buildingIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<Building | void> {
    logger.info('moveToTrash Successfull',
      {buildingIds: buildingIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: buildingIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('Buildings corresponding to Building IDs provided not found');
      throw 'Buildings corresponding to Building IDs provided not found';
    }
    const BUILDINGdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._BUILDINGS}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(BUILDINGdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: buildingIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moceToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreBuilding (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreBUILDING Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);
    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        const building = new this.model(item);
        await building.save();
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreBUILDING is Successfull');
        return 'BUILDING got restored';
      });
    } else {
      logger.error('Error in restoreBUILDING');
      throw new Error('Failed to restore building data from trash');
    }
  }
}

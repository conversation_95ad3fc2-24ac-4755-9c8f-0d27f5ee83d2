import logger from '../../config/logger';
import {masterLandmarksSchema} from '../../schema/masterLandmarksSchema';
import { Models } from '../../types/extras';
import {masterLandmark, updateMasterLandmark} from '../../types/masterLandmark';
import mongoose from 'mongoose';
import { invalidateCacheByPattern } from '../cdn';
import { trashModule } from '../trash/index';
import { trashType } from '../../types/trash';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';

export async function invalidateMasterLandmarkAPIs (organization_id: string): Promise<any> {
  try {
    const apiPaths = [
      `/publicapis/organization/${organization_id}/getListofLandmarks`,
    ];

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

// Import {storageUpload} from '../../helpers/storageUpload';
export class masterLandmarkModule{
  private model: mongoose.Model<masterLandmark>;
  public storagepath;
  constructor (organization_id:string) {
    this.model=mongoose.model<masterLandmark>(`${organization_id}${Models._LANDMARKS}`, masterLandmarksSchema);
    this.storagepath='CreationtoolAssets/'+organization_id+'/master_landmarks/';
  }
  // Public async UploadFiles (file:Express.Multer.File):Promise<string> {
  //   Return new Promise((resolve, reject) => {
  //     Const uploadOptions = {
  //       Destination:
  //               This.storagepath
  //               +file.originalname,
  //     };
  //     StorageUpload(uploadOptions, file.path).then((thumbnailUrl) => {
  //       Resolve(thumbnailUrl);
  //     })
  //       .catch((err) => {
  //         Reject(err);
  //       });
  //   });
  // }
  public async createLandmark (payload:object):Promise<masterLandmark|string>{
    logger.info('createLandmark Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const landmark = new this.model(payload);
      landmark.save().then((res) => {
        logger.info('createLandmark Successfully', {response: res});
        resolve(res);
      }).catch((err:string) => {
        logger.error('Error in createLandmark', {message: err});
        reject(err);
      });
    });
  }

  public async getListofLandmark (): Promise<{[key: string]: masterLandmark}|null> {
    logger.info('getListofLandmark Called');
    const masterLandmarks = await this.model.find();
    const transformedResult: {[key: string]: masterLandmark} = {};
    for (const landmark of masterLandmarks) {
      transformedResult[landmark._id] = landmark;
    }
    logger.info('getListofLandmark Successfully', {transformedResult: transformedResult});
    return transformedResult;
  }

  public async updateLandmark (payload:updateMasterLandmark):Promise<masterLandmark>{
    logger.info('updateLandmark Called', {payload: payload});
    return new Promise<masterLandmark>((resolve, reject) => {
      const updateFields: updateMasterLandmark = {};
      if (payload.name !== undefined) {
        updateFields.name = payload.name;
      }
      if (payload.distance !== undefined) {
        updateFields.distance = payload.distance;
      }
      if (payload.category !== undefined) {
        updateFields.category = payload.category;
      }
      if (payload.walk_timing !== undefined) {
        updateFields.walk_timing = payload.walk_timing;
      }
      if (payload.transit_timing !== undefined) {
        updateFields.transit_timing = payload.transit_timing;
      }
      if (payload.car_timing !== undefined) {
        updateFields.car_timing = payload.car_timing;
      }
      if (payload.description !== undefined) {
        updateFields.description = payload.description;
      }
      if (payload.thumbnail !== undefined) {
        updateFields.thumbnail = payload.thumbnail;
      }

      this.model.findOneAndUpdate(
        { _id: payload.landmark_id },
        {
          $set: updateFields,
        },
        { new: true },
      ).then((res) => {
        logger.info('updateLandmark Successful', {Response: res});
        resolve(res as masterLandmark);
      }).catch((error) => {
        logger.error('Error in updateLandmark ', {message: error});
        reject(error);
      });

    });
  }

  public async moveToTrash (
    landmarkIds: Array<string>,
    organization_id: string,
    timeStamp: number,
  ): Promise<masterLandmark | void> {
    logger.info('moveToTrash Called',
      {landmarkIds: landmarkIds, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: landmarkIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('landmarkIds corresponding to Landmark IDs provided not found');
      throw 'landmarkIds corresponding to Landmark IDs provided not found';
    }
    const landmarkdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${organization_id.toLowerCase()}_master_landmarks`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(landmarkdataToInsertObj),
    ])
      .then(async (res) => {
        console.log('**********Moved to Trash*********', res);
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: landmarkIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successful');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }

  public async restoreLandmark (
    organization_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreLandmark Called',
      {organization_id: organization_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createLandmarkPromises = Object.values(restoredData.data).map(async (item) => {
        await this.createLandmark(item);
      });

      await Promise.all([
        createLandmarkPromises,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreLandmark is Successful');
        return 'Landmark got restored';
      });
    } else {
      logger.error('Error in restoreLandmark');
      throw new Error('Failed to restore landmark data from trash');
    }
  }
}

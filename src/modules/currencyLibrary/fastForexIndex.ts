import axios from 'axios';
import logger from '../../config/logger';
import { currencyProvidersEnum } from '../../types/currencyLibrary';
import { FormattedRate } from '../../types/organization';

type exchangeObject = {
    baseCurrency: string,
    exchangeRatio: FormattedRate[],
    updated: Date,
    provider: string
}

export class fastforexModule {

  private readonly fastforexURL: string;
  private readonly fastforexkey: string;
  constructor () {
    this.fastforexURL = process.env.FASTFOREXURL || '';
    this.fastforexkey = process.env.FASTFOREXKEY || '';

  }

  public async fetchExchangeRates (baseCurrency: string): Promise<exchangeObject> {
    try {

      const response = await axios.get(this.fastforexURL, {
        params: {
          'from': baseCurrency.toUpperCase(),
          'api_key': this.fastforexkey,
        }, headers: { accept: 'application/json' },
      });

      const currencyRatesArray: FormattedRate[] = Object.entries(response?.data?.results).map(([currency, rate]) => ({
        currency,
        rate: typeof rate === 'number' ? rate : Number(rate) || null,
      }));

      const responseObject: exchangeObject = {
        baseCurrency: response.data.base,
        exchangeRatio: currencyRatesArray,
        updated: response.data.updated,
        provider: currencyProvidersEnum.FASTFOREX,
      };

      return responseObject;
    } catch (error) {
      logger.error('Error getting initial token', { error });
      throw new Error('Failed to get initial token');
    }
  }
}

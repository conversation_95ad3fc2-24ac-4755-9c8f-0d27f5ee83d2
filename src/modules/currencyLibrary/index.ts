import mongoose from 'mongoose';
import { currencyLibrary, currencyUpdate } from '../../types/currencyLibrary';
import { Models } from '../../types/extras';
import { currencyLibrarySchema } from '../../schema/currencyExchangeSchema';
import logger from '../../config/logger';

export class currencyLibraryModule {
  private model: mongoose.Model<currencyLibrary>;
  constructor (providername: string) {
    this.model = mongoose.model<currencyLibrary>(
      `${providername}${Models._CURRENCYLIBRARY}`,
      currencyLibrarySchema,
    );
  }

  public async CreateCurrency (payload: currencyLibrary): Promise<currencyLibrary | void> {
    logger.info('CreateCurrency called', { payload: payload });
    return new Promise((resolve, reject) => {
      console.log(payload, 'inside Create Currency');
      const currencyLib = new this.model(payload);
      currencyLib
        .save()
        .then((res) => {
          logger.info('CreateCurrency Successfull', { response: res });
          resolve(res);
        })
        .catch((err) => {
          logger.error('Error in CreateCurrency', { message: err });
          reject(err);
        });
    });
  }

  public async UpdateCurrency (payload: currencyUpdate, baseCurrency: string, provider_name: string): Promise<currencyLibrary | void> {
    logger.info('Update Currency Called', { provider_name: provider_name, payload: payload, baseCurrency: baseCurrency });

    try {
      const existingCurrency = await this.model.findOne({ baseCurrency: baseCurrency.toUpperCase() });

      if (existingCurrency) {
        const updateCurrency = await this.model.findOneAndUpdate(
          { baseCurrency: baseCurrency.toUpperCase() },
          {
            $set: {
              exchangeRatio: payload.exchangeRatio,
              updated: payload.timestamp,
            },
          },
          { new: true },
        );

        if (updateCurrency) {
          logger.info('Update Currency Successfull', { updateCurrency: updateCurrency });
          return updateCurrency;
        }

        logger.error('Error in updating Currency');
        throw new Error('Error in updating Currency');

      } else {
        logger.error('Unable to find existing Currency Record');
        throw new Error('Unable to find existing Currency Record');
      }

    } catch (error) {
      logger.error('Internal Server Error while Update Currency', { message: error });
      throw new Error('Internal Server Error while update Currency' + error);
    }
  }

  public async GetCurrencies (provider_name: string): Promise<currencyLibrary[] | void> {
    logger.info('Get currencies Called');
    const listOfCurrencies = await this.model.find({ source: provider_name });
    if (listOfCurrencies && listOfCurrencies.length > 0) {
      logger.info('GetCurrencies Successfull');
      return listOfCurrencies as currencyLibrary[];
    }
    logger.error(`No currencies found for provider ${provider_name}`);
    throw new Error(`No currencies found for provider ${provider_name}`);
  }

  public async GetCurrency (provider_name: string, baseCurrency: string): Promise<currencyLibrary | void> {
    logger.info('Get currency Called');
    const currency = await this.model.findOne({
      $and: [
        { source: provider_name },
        { baseCurrency: baseCurrency.toUpperCase() },
      ],
    });
    if (currency) {
      logger.info('Get Currency Successfull');
      return currency as currencyLibrary;
    }
    logger.error(`No currencies found for provider ${provider_name} & ${baseCurrency}`);
    throw new Error(`No currencies found for provider ${provider_name} & ${baseCurrency}`);
  }
}

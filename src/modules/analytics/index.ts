import { BetaAnalyticsDataClient } from '@google-analytics/data';
import { ReportParams, ReportRow, EventDetailsParams, EventDetailsRow } from '../../types/analyticsModel';

// Singleton instance of BetaAnalyticsDataClient to avoid creating multiple clients
// Which can cause rate limiting and connection issues with concurrent requests
let analyticsDataClientInstance: BetaAnalyticsDataClient | null = null;

function getAnalyticsDataClient (): BetaAnalyticsDataClient {
  if (!analyticsDataClientInstance) {
    analyticsDataClientInstance = new BetaAnalyticsDataClient({
      credentials: {
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        private_key: (process.env.FIREBASE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
      },
    });
  }
  return analyticsDataClientInstance;
}

export class AnalyticsModule {
  private analyticsDataClient: BetaAnalyticsDataClient;
  private propertyId: string;

  constructor () {
    // Use singleton client instance instead of creating a new one each time
    this.analyticsDataClient = getAnalyticsDataClient();
    this.propertyId = process.env.FIREBASE_PROPERTY_ID || '';
  }

  public async runReport ({ startDate, endDate, orgId, projectId }: ReportParams): Promise<ReportRow[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'country' },
          { name: 'region' },
          { name: 'city' },
          { name: 'browser' },
          { name: 'operatingSystem' },
          { name: 'deviceCategory' },
          { name: 'sessionSource' },
          { name: 'eventName' },
        ],
        metrics: [
          { name: 'activeUsers' },
          { name: 'sessions' },
          { name: 'totalUsers' },
          { name: 'newUsers' },
          { name: 'engagementRate' },
          { name: 'userEngagementDuration' },
          { name: 'screenPageViews' },
          { name: 'eventCount' },
        ],
        dimensionFilter: {
          filter: {
            fieldName: 'fullPageUrl',
            stringFilter: {
              matchType: 'CONTAINS',
              value: `/${orgId}/projectscene/${projectId}`,
            },
          },
        },
      });

      if (!response.rows) {
        return [];
      }

      return response.rows.map((row): ReportRow => {
        const country = row.dimensionValues?.[0]?.value || 'Unknown';
        const region = row.dimensionValues?.[1]?.value || 'Unknown';
        const city = row.dimensionValues?.[2]?.value || 'Unknown';
        const browser = row.dimensionValues?.[3]?.value || 'Unknown';
        const operatingSystem = row.dimensionValues?.[4]?.value || 'Unknown';
        const deviceCategory = row.dimensionValues?.[5]?.value || 'Unknown';
        const sessionSource = row.dimensionValues?.[6]?.value || 'Unknown';
        const eventName = row.dimensionValues?.[7]?.value || 'Unknown';

        const activeUsers = Number(row.metricValues?.[0]?.value || '0');
        const sessions = Number(row.metricValues?.[1]?.value || '0');
        const totalUsers = Number(row.metricValues?.[2]?.value || '0');
        const newUsers = Number(row.metricValues?.[3]?.value || '0');
        const engagementRate = Number(row.metricValues?.[4]?.value || '0');
        const userEngagementDuration = Number(row.metricValues?.[5]?.value || '0');
        const screenPageViews = Number(row.metricValues?.[6]?.value || '0');
        const eventCount = Number(row.metricValues?.[7]?.value || '0');

        const newUserPercentage = ((newUsers / totalUsers) * 100).toFixed(2);
        const pageviewsPerUser = (screenPageViews / totalUsers).toFixed(2);

        return {
          country,
          region,
          city,
          browser,
          operatingSystem,
          deviceCategory,
          sessionSource,
          eventName,
          activeUsers,
          sessions,
          totalUsers,
          newUsers,
          engagementRate,
          userEngagementDuration,
          screenPageViews,
          eventCount,
          newUserPercentage,
          pageviewsPerUser,
        };
      });
    } catch (error) {
      console.error('Error running report:', error);
      throw error;
    }
  }

  public async fetchEventDetails ({
    startDate,
    endDate,
    orgId,
    projectId,
    eventName,
    customFields,
  }: EventDetailsParams): Promise<EventDetailsRow[]> {
    // First, try with all custom fields
    try {
      const dimensions = [
        { name: 'eventName' },
        ...customFields.map((field) => ({ name: `customEvent:${field}` })),
      ];

      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: dimensions,
        metrics: [{ name: 'eventCount' }],
        dimensionFilter: {
          andGroup: {
            expressions: [
              {
                filter: {
                  fieldName: 'eventName',
                  stringFilter: {
                    matchType: 'EXACT',
                    value: eventName,
                  },
                },
              },
              {
                filter: {
                  fieldName: 'fullPageUrl',
                  stringFilter: {
                    matchType: 'CONTAINS',
                    value: `/${orgId}/projectscene/${projectId}`,
                  },
                },
              },
            ],
          },
        },
      });

      if (!response.rows) {
        return [];
      }

      return response.rows.map((row): EventDetailsRow => {
        const result: EventDetailsRow = {
          eventName: row.dimensionValues?.[0]?.value || 'Unknown',
          eventCount: row.metricValues?.[0]?.value || '0',
        };

        customFields.forEach((field, index) => {
          result[field] = row.dimensionValues?.[index + 1]?.value || '';
        });

        return result;
      });
    } catch (error: any) {
      // If INVALID_ARGUMENT error (code 3), fallback to query without custom fields
      // This allows us to at least return event counts even if custom fields don't exist
      if (error?.code === 3 || error?.message?.includes('INVALID_ARGUMENT')) {
        console.warn(`Custom fields not available for event "${eventName}". Falling back to basic event data.`, {
          eventName,
          customFields,
          error: error?.message || error,
        });

        try {
          // Fallback: Query without custom fields to get at least event counts
          const [fallbackResponse] = await this.analyticsDataClient.runReport({
            property: `properties/${this.propertyId}`,
            dateRanges: [{ startDate, endDate }],
            dimensions: [{ name: 'eventName' }],
            metrics: [{ name: 'eventCount' }],
            dimensionFilter: {
              andGroup: {
                expressions: [
                  {
                    filter: {
                      fieldName: 'eventName',
                      stringFilter: {
                        matchType: 'EXACT',
                        value: eventName,
                      },
                    },
                  },
                  {
                    filter: {
                      fieldName: 'fullPageUrl',
                      stringFilter: {
                        matchType: 'CONTAINS',
                        value: `/${orgId}/projectscene/${projectId}`,
                      },
                    },
                  },
                ],
              },
            },
          });

          if (!fallbackResponse.rows) {
            return [];
          }

          // Return data with empty custom fields
          return fallbackResponse.rows.map((row): EventDetailsRow => {
            const result: EventDetailsRow = {
              eventName: row.dimensionValues?.[0]?.value || 'Unknown',
              eventCount: row.metricValues?.[0]?.value || '0',
            };

            // Set all custom fields to empty strings since they're not available
            customFields.forEach((field) => {
              result[field] = '';
            });

            return result;
          });
        } catch (fallbackError) {
          // If fallback also fails, throw the original error
          console.error('Error fetching event details (fallback also failed):', fallbackError);
          throw error;
        }
      }

      // For other errors, throw as normal
      console.error('Error fetching event details:', error);
      throw error;
    }
  }
}

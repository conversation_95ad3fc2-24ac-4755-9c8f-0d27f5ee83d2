import {projectLandmarksSchema} from '../../schema/projectLandmarksSchema';
import { categoryMap, getAllRoutesInput, getRoutesInput, gmap_types, landmarkCategory, placeType, projectLandmark,
  routeCategory,
  updateProjectLandmark, updateProjectLandmarkFiles } from '../../types/projectLandmark';
import mongoose, { Types } from 'mongoose';
import { Models } from '../../types/extras';
import { client } from '../../config/googleMaps';
import { DirectionsRequest, LatLngLiteral, PlaceEditorialSummary,
  Status, TravelMode } from '@googlemaps/google-maps-services-js';
import { arrayToObject, convertDurationToMinutes, UnknownObject,
  convertDistance } from '../../helpers/dataFormatHelper';
import { admin, bucketName } from '../../config/firebase';
import { fetchAndUploadImage } from '../../helpers/imageDownloadHelper';
import logger from '../../config/logger';
import { trashModule } from '../trash/index';
import { trashType } from '../../types/trash';
import { invalidateCacheByPattern } from '../cdn';

export async function invalidateProjectLandmarkAPIs (organization_id:string, project_id:string ): Promise<any> {
  try {
    const apiPaths = [
      `/publicapis/organization/${organization_id}/project/${project_id}/getListofProjectLandmark`,
    ];

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

// Import {storageUpload} from '../../helpers/storageUpload';
export class ProjectLandmarkModule{
  private model: mongoose.Model<projectLandmark>;
  public storagepath;
  constructor (project_id:string, organization_id:string) {
    this.model=mongoose.model<projectLandmark>(`${project_id}${Models._LANDMARKS}`, projectLandmarksSchema);
    this.storagepath='CreationtoolAssets/'+organization_id+'/projects/'+project_id+'/projectlandmarks/';
  }
  public async createLandmark (payload:object):Promise<projectLandmark | void>{
    logger.info('createLandmark Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const landmark = new this.model(payload);
      landmark.save().then((res) => {
        logger.info('createLandmark Successfull', {Response: res});
        resolve(res);
      }).catch((err:string) => {
        logger.error('Error in createLandmark ', {message: err});
        reject(err);
      });
    });
  }
  public async getListofLandmark (): Promise<object|null> {
    logger.info('getListofLandmark Called');
    const projectLandmarks: Array<UnknownObject> = await this.model.find();
    const transformedResult = arrayToObject(projectLandmarks) as Record<string, projectLandmark>;
    logger.info('getListofLandmark Successfull', {transformedResult: transformedResult});
    return transformedResult;
  }
  public async updateLandmark (payload:updateProjectLandmark):Promise<projectLandmark>{
    logger.info('updateLandmark Called', {payload: payload});
    return new Promise<projectLandmark>((resolve, reject) => {
      this.model.findOneAndUpdate(
        { _id: payload.landmark_id },
        {
          $set: {
            'name': payload.name,
            'distance': payload.distance,
            'category': payload.category,
            'walk_timing': payload.walk_timing,
            'transit_timing': payload.transit_timing,
            'car_timing': payload.car_timing,
            'lat': payload.lat,
            'long': payload.long,
            'description': payload.description,
            'driving': payload.driving,
            'transit': payload.transit,
            'walking': payload.walking,
          },
        },
        { new: true },
      ).then((res) => {
        logger.info('updateLandmark Successfull', {Response: res});
        resolve(res as projectLandmark);
      }).catch((error) => {
        logger.error('Error in updateLandmark ', {message: error});
        reject(error);
      });

    });
  }
  public async updateLandmarkFiles (
    payload:updateProjectLandmarkFiles,
    thumbnail:string,
    icon:string):Promise<projectLandmark>{
    logger.info('updateLandmarkFiles Called', {payload: payload, thumbnail: thumbnail});
    return new Promise<projectLandmark>((resolve, reject) => {
      this.model.findOneAndUpdate(
        { _id: payload.landmark_id },
        {
          $set: {
            'thumbnail': thumbnail,
            'icon': icon,
          },
        },
        { new: true },
      ).then((res) => {
        logger.info('updateLandmarkFiles Successfull', {Response: res});
        resolve(res as projectLandmark);
      }).catch((error) => {
        logger.error('Error in updateLandmarkFiles ', {message: error});
        reject(error);
      });

    });
  }
  public async getAllRoutes (payload: getAllRoutesInput): Promise<string> {
    logger.info('getAllRoutes Called', {payload: payload});
    return new Promise<string>((resolve, reject) => {
      const route_urls:{ [key:string]:string | null}={};
      this.getListofLandmark().then((landmarks) => {
        if (landmarks) {
          const results = Object.values(landmarks).map(async (item) => {
            const origin: LatLngLiteral = { lat: payload.projLat, lng: payload.projLong }; // San Francisco, CA
            const destination: LatLngLiteral = { lat: item.lat, lng: item.long };
            const modePromises = Object.values(TravelMode).map(async (mode_name) => {
              if (mode_name !== 'bicycling') {
                const request = {
                  params: {
                    origin: origin,
                    destination: destination,
                    key: process.env.GOOGLE_MAPS_API_KEY,
                    mode: mode_name,
                  },
                  timeout: 1000,
                } as DirectionsRequest;
                try {
                  const response = await client.directions(request);
                  if (response.data.status === Status.OK) {
                    const jsonString = JSON.stringify(response.data);
                    const filePath = this.storagepath + item._id + '/' + mode_name + '.json';
                    await admin.storage()
                      .bucket(bucketName)
                      .file(filePath)
                      .save(jsonString, {
                        contentType: 'application/json',
                      });
                    const thumbnailUrl =
                      'https://firebasestorage.googleapis.com/v0/b/' +
                      bucketName +
                      '/o/' +
                      encodeURIComponent(filePath) +
                      '?alt=media';
                    route_urls[mode_name] = thumbnailUrl;
                  } else if (response.data.status === Status.ZERO_RESULTS) {
                    route_urls[mode_name] = null;
                  } else {
                    logger.error('response.data.status');
                    reject(response.data.status);
                  }
                } catch (err) {
                  logger.error({message: err});
                  reject(err);
                }
              }
            });
            return Promise.all(modePromises).then(() => {
              return this.model.findOneAndUpdate(
                { _id: item._id },
                {
                  $set: {
                    'driving': route_urls.driving?route_urls.driving:route_urls.walking,
                    'walking': route_urls.walking,
                    'transit': route_urls.transit?route_urls.transit:route_urls.walking,
                  },
                },
                { new: true },
              );
            });
          });
          Promise.all(results).then(() => {
            logger.info('getAllRoutes Successfull');
            resolve('routes found');
          });
        }
      });
    });
  }
  public async getLandmarkById (landmarkId: string): Promise<projectLandmark> {
    logger.info('getLandmarkById Called', {landmarkId: landmarkId});
    const landmark = await this.model.findById(landmarkId);
    if (landmark) {
      logger.info('getLandmarkById Successfull', {landmark: landmark});
      return landmark as projectLandmark;
    }
    logger.error(`Landmark with ID ${landmarkId} not found`);
    throw new Error(`Landmark with ID ${landmarkId} not found`);

  }
  public async getRoutes (payload: getRoutesInput): Promise<string> {
    logger.info('getRoutes Called', {payload: payload});
    const route_urls: { [key: string]: string | null } = {};
    const { projLat, projLong, landmarkId } = payload;

    try {
      const landmark = await this.getLandmarkById(landmarkId);
      if (!landmark) {
        logger.error(`Landmark with ID ${landmarkId} not found`);
        throw new Error(`Landmark with ID ${landmarkId} not found`);
      }

      const origin: LatLngLiteral = { lat: projLat, lng: projLong };
      const destination: LatLngLiteral = { lat: landmark.lat, lng: landmark.long };

      const modePromises = Object.values(TravelMode).map(async (mode_name) => {
        if (mode_name !== 'bicycling') {
          const request = {
            params: {
              origin: origin,
              destination: destination,
              key: process.env.GOOGLE_MAPS_API_KEY,
              mode: mode_name,
            },
            timeout: 1000,
          } as DirectionsRequest;

          try {
            const response = await client.directions(request);
            if (response.data.status === Status.OK) {
              const jsonString = JSON.stringify(response.data);
              const filePath = `${this.storagepath}${landmark._id}/${mode_name}.json`;
              await admin.storage()
                .bucket(bucketName)
                .file(filePath)
                .save(jsonString, {
                  contentType: 'application/json',
                });
              const thumbnailUrl =
                            'https://firebasestorage.googleapis.com/v0/b/' +
                            bucketName +
                            '/o/' +
                            encodeURIComponent(filePath) +
                            '?alt=media';
              route_urls[mode_name] = thumbnailUrl;
            } else if (response.data.status === Status.ZERO_RESULTS) {
              route_urls[mode_name] = null;
            } else {
              logger.error(`Google Maps API error: ${response.data.status}`);
              throw new Error(`Google Maps API error: ${response.data.status}`);
            }
          } catch (err) {
            logger.error('Failed to fetch directions:', {message: err});
            throw new Error(`Failed to fetch directions: ${err}`);
          }
        }
      });

      await Promise.all(modePromises);

      await this.model.findOneAndUpdate(
        { _id: landmark._id },
        {
          $set: {
            'driving': route_urls.driving ? route_urls.driving : route_urls.walking,
            'walking': route_urls.walking,
            'transit': route_urls.transit ? route_urls.transit : route_urls.walking,
          },
        },
        { new: true },
      );
      logger.info('getRoutes Successfull', {payload: payload});
      return 'routes found';

    } catch (error) {
      logger.error('Error fetching routes', {message: error});
      throw new Error('Error fetching routes');
    }
  }

  public async fetchProjectLandmarks (payload:getAllRoutesInput):Promise<boolean | void>{
    logger.info('fetchProjectLandmarks Called', {payload: payload});
    const radius = 500;
    const maxResultsPerType = 3;
    const API_KEY = process.env.GOOGLE_MAPS_API_KEY;

    try {
      const places : Record<string, placeType[]> = {};
      const addedPlaceIds = new Set();
      for (const type of gmap_types) {
        const response = await client.placesNearby({
          params: {
            location: `${payload.projLat},${payload.projLong}`,
            radius: radius,
            type: type,
            key: API_KEY || '',
          },
        });
        const filteredPlaces = response.data.results
          .filter((place) => !addedPlaceIds.has(place.place_id))
          .slice(0, maxResultsPerType)
          .map((place) => ({
            name: place.name || '',
            place_id: place.place_id || '',
            photos: place.photos,
            geometry: place.geometry,
            description: place.editorial_summary,
          }));

        filteredPlaces.forEach((place) => addedPlaceIds.add(place.place_id));
        places[type] = filteredPlaces;

      }
      await this.fetchLandmarkImages(places);
      await this.getDistanceMatrixForPlaces(payload, places);
      for (const type of Object.keys(places)) {
        for (const place of places[type]) {
          const response = await client.placeDetails({
            params: {
              place_id: place.place_id,
              key: API_KEY || '',
              fields: ['editorial_summary'],
            },
          });
          place.description = response.data.result.editorial_summary?.overview as PlaceEditorialSummary;
          const walkTiming = convertDurationToMinutes(place.distanceMatrix?.walking?.duration?.text || '');
          const transitTiming = convertDurationToMinutes(place.distanceMatrix?.transit?.duration?.text || '');
          const carTiming = convertDurationToMinutes(place.distanceMatrix?.driving?.duration?.text || '');
          const id = new Types.ObjectId();
          const category = categoryMap[type] || landmarkCategory.OTHER;
          const landmarkPayload = {
            _id: id,
            name: place.name,
            thumbnail: place.imageURL ? place.imageURL : '',
            distance: place.distanceMatrix?.driving?.distance.text?
              convertDistance(place.distanceMatrix?.driving?.distance.text) : 0,
            category: category,
            walk_timing: walkTiming,
            transit_timing: transitTiming,
            car_timing: carTiming,
            lat: place.geometry?.location.lat,
            long: place.geometry?.location.lng,
            description: '',
          };
          await this.createLandmark(landmarkPayload);
        }
      }
      logger.info('fetchProjectLandmarks Successfull');
      return true;
    } catch (error) {
      logger.error('Error fetching landmarks:', {message: error});
      console.error('Error fetching landmarks:', error);
      return false;
    }
  }

  public async getDistanceMatrixForPlaces
  (payload: getAllRoutesInput, places: Record<string, placeType[]>):Promise<void> {
    logger.info('getDistanceMatrixForPlaces Called', {payload: payload, places: places});
    const origins: LatLngLiteral[] = [{ lat: payload.projLat, lng: payload.projLong }];
    const destinations: LatLngLiteral[] = [];

    Object.keys(places).forEach((type) => {
      for (const place of places[type]) {
        destinations.push(place.geometry?.location as LatLngLiteral);
      }
    });
    const travelModes = [TravelMode.driving, TravelMode.walking, TravelMode.transit];

    for (const mode of travelModes) {
      try {
        const distanceMatrixResponse = await client.distancematrix({
          params: {
            origins: origins,
            destinations: destinations,
            mode: mode,
            key: process.env.GOOGLE_MAPS_API_KEY || '',
          },
        });

        const rows = distanceMatrixResponse.data.rows;

        let destinationIndex = 0;
        Object.keys(places).forEach((type) => {
          for (const place of places[type]) {
            if (!place.distanceMatrix) {
              place.distanceMatrix = {};
            }
            place.distanceMatrix[mode] = rows[0].elements[destinationIndex];
            destinationIndex++;
          }
        });
        logger.info('getDistanceMatrixForPlaces Successfull');
      } catch (error) {
        logger.error(`Error fetching distance matrix for mode ${mode}:`, {message: error});

      }
    }
  }

  public async fetchLandmarkImages (places: Record<string, placeType[]>):Promise<void> {
    logger.info('fetchLandmarkImages Called', {places: places});
    const uploadPromises: Promise<void>[] = [];
    for (const type in places) {
      for (const place of places[type]) {
        if (place.photos) {
          const filePath = `${this.storagepath}maps/`;
          const urlPath = 'https://maps.googleapis.com/maps/api/place/photo?maxwidth=720&photoreference=';
          const promise =
          fetchAndUploadImage(`${urlPath}${place.photos[0].photo_reference}&key=${process.env.GOOGLE_MAPS_API_KEY}`,
            place.name, filePath)
            .then((url: string) => {
              place.imageURL = url;
            })
            .catch((error) => {
              logger.error('Error in fetchLandmarkImages', {message: error});
              console.error('Error:', error);
            });
          uploadPromises.push(promise);
        }
      }
    }
    await Promise.all(uploadPromises);
    logger.info('fetchLandmarkImages Successfull');
  }

  public async saveRoutes (landmarkId: string, json: string, mode: routeCategory): Promise<string> {
    logger.info('fetchLandmarkImages Called', {landmarkId: landmarkId, json: json, mode: mode});
    try {
      const landmark = await this.getLandmarkById(landmarkId);
      if (!landmark) {
        logger.error(`Landmark with ID ${landmarkId} not found`);
        throw new Error(`Landmark with ID ${landmarkId} not found`);
      }
      const jsonString = JSON.stringify(json);
      const filePath = `${this.storagepath}${landmark._id}/${mode}.json`;
      await admin.storage()
        .bucket(bucketName)
        .file(filePath)
        .save(jsonString, {
          contentType: 'application/json',
        });
      const thumbnailUrl =
              'https://firebasestorage.googleapis.com/v0/b/' +
              bucketName +
              '/o/' +
              encodeURIComponent(filePath) +
              '?alt=media';
      await this.model.findOneAndUpdate(
        { _id: landmark._id },
        {
          $set: {
            [mode]: thumbnailUrl,
          },
        },
        { new: true },
      );
      logger.info('saveRoutes Successfull');
      return 'routes found';

    } catch (error) {
      logger.error('Error fetching routes', {message: error});
      throw new Error('Error fetching routes');
    }
  }

  public async moveToTrash (
    landmarkIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<projectLandmark | void> {
    logger.info('moveToTrash Successfull',
      {landmarkIds: landmarkIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: landmarkIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('landmarkIds corresponding to Landmark IDs provided not found');
      throw 'landmarkIds corresponding to Landmark IDs provided not found';
    }
    const landmarkdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._LANDMARKS}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(landmarkdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: landmarkIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreLandmark (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreLandmark Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        await this.createLandmark(item);
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreLandmark is Successfull');
        return 'Landmark got restored';
      });
    } else {
      logger.error('Error in restoreLandmark');
      throw new Error('Failed to restore landmark data from trash');
    }
  }
}

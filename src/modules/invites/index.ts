import mongoose from 'mongoose';
import { InviteSchema } from '../../schema/invitationsSchema';
import { User} from '../../types/organization';
import { Invitation, UserInvitation, UserInvite, CreateInvitationInput } from '../../types/invitations';
import { OrganizationModule } from '../organization';
import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import logger from '../../config/logger';
import { trashModule } from '../trash';
import { trashType } from '../../types/trash';
import { sendEmail } from '../../helpers/session';
import { loadAcceptInvitationTemplate, loadNotifyUserTemplate } from '../../helpers/templateLoader';
const InvitationModel = mongoose.model('Invitations', InviteSchema);

export class InvitesModule {
  private model: mongoose.Model<Invitation>;

  constructor () {
    this.model = mongoose.model<Invitation>(
      'invitations',
      InviteSchema,
    );
  }
  public async CreateInvitation (new_invite: CreateInvitationInput): Promise<Invitation> {
    logger.info('CreateInvitation Called', {new_invite: new_invite});
    try {
      // Check if an invitation with the same email and organization already exists
      const existingInvite = await this.model.findOne({
        email: new_invite.email,
        organization_id: new_invite.organization_id,
      });

      if (existingInvite) {
        logger.error('Invitation with the same email and organization already exists');
        throw new Error('Invitation with the same email and organization already exists');
      }

      // Ensure that _id is generated before saving the document
      const invite = new InvitationModel({ ...new_invite, _id: new mongoose.Types.ObjectId() });
      const savedInvite = await invite.save();
      logger.info('CreateInvitation Successfully', {savedInvite: savedInvite});
      return savedInvite as Invitation;
    } catch (error) {
      logger.error('Error in CreateInvitation', {message: error});
      throw error; // Rethrow the error to reject the promise
    }
  }
  public SendInvite (invite_data: UserInvite): Promise<boolean> {
    logger.info('SendInvite Called',  invite_data.email );
    return new Promise<boolean>((resolve, reject) => {
      (async () => {
        try {
          // Send email with template
          await sendEmail({
            from: '<EMAIL>',
            to: invite_data.email,
            subject: 'You have been invited to join ' + invite_data.organization_name,
            html: loadAcceptInvitationTemplate(invite_data.referer_url, invite_data.organization_name),
          });
          logger.info('Invite email has been sent successfully', invite_data.email);
          resolve(true);

        } catch (error) {
          logger.error('Error sending invite to user', invite_data.email, error);
          reject(new Error('Failed to send invite mail:' + error));
        }
      })();
    });
  }
  public notifyUser (invite_data: UserInvite): Promise<boolean> {
    logger.info('NotifyUser Called',  invite_data.email );
    return new Promise<boolean>((resolve, reject) => {
      (async () => {
        try {
          // Send email with template
          await sendEmail({
            from: '<EMAIL>',
            to: invite_data.email,
            subject: 'You have been invited to join ' + invite_data.organization_name,
            html: loadNotifyUserTemplate(invite_data.referer_url,
              invite_data.organization_name, invite_data.user_name),
          });
          logger.info('notify user email has been sent successfully', invite_data.email);
          resolve(true);

        } catch (error) {
          logger.error('Error sending notify user email', invite_data.email, error);
          reject(new Error('Failed to send notify user email:' + error));
        }
      })();
    });
  }
  public async AcceptInvite (user: User, userInvite: UserInvitation): Promise<UserInvitation> {
    try {
      const organization = new OrganizationModule();
      await organization.AddUserToOrganization(userInvite.organization_id, user);
      const userRoleObj = {
        user_id: user.user_id,
        organizationId: userInvite.organization_id,
        roleId: userInvite.role,
        email: user.email,
      };
      await organization.AssignRoleToUser(userRoleObj);
      await this.DeleteInvitation(userInvite._id);
      return userInvite;
    } catch (error) {
      throw new Error('Error accepting invite: '+userInvite._id + error);
    }
  }
  public async findUserInvite (email: string): Promise<UserInvitation[] | null> {
    const InviteModel = mongoose.model('invitations', InviteSchema);
    const userInvite = await InviteModel.find({email: email, status: 'pending'});
    if (userInvite) {
      return userInvite as UserInvitation[];
    }
    return null;
  }
  public async findUserInviteById (invite_id: string): Promise<UserInvitation | null> {
    const InviteModel = mongoose.model('invitations', InviteSchema);
    const userInvite = await InviteModel.findOne({_id: invite_id});
    if (userInvite) {
      return userInvite as UserInvitation;
    }
    return null;
  }
  public async GetInvitations (
    organizationId: string,
  ): Promise<object | null> {
    try {
      const invitations: Array<UnknownObject> = await InvitationModel.find({
        organization_id: organizationId,
      });
      const invitationsObj = arrayToObject(invitations) as Record<string, Invitation>;
      return invitationsObj;
    } catch {
      return null;
    }
  }
  public async DeleteInvitation (invite_id: string): Promise<Invitation> {
    try {
      const deletedInvite: unknown = await this.model.findOneAndDelete({_id: invite_id});
      return deletedInvite as Invitation;
    } catch (error) {
      console.log(error);
      throw error; // Rethrow the error to reject the promise
    }
  }
  public async moveToTrash (
    invitationId: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<void> {
    logger.info('moveToTrash Successfull',
      {invitationId: invitationId, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const invitations = await this.model.findOne({_id: invitationId});
    // Const invitationObj = arrayToObject(invitations) as Record<string, Invitation>;
    if (!invitations) {
      logger.error('InvitationId  provided not found');
      throw 'InvitationId  provided not found';
    }
    const InvitationDataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${organization_id.toLowerCase()}_invitation`,
      timeStamp: timeStamp,
      data: invitations,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(InvitationDataToInsertObj),
    ])
      .then(async () => {
        this.model.findOneAndDelete(
          {_id: invitationId},
        ).then(() => {
          logger.info('moveToTrash Successfull');
          return;
        })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
}

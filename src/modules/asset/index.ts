import mongoose from 'mongoose';
import { Models } from '../../types/extras';
import { arrayToObject } from '../../helpers/dataFormatHelper';
import { CopyFirebaseItem } from '../../helpers/moveStorageUpload';
import { assets, updateAssets } from '../../types/asset';
import { AssetsSchema } from '../../schema/assetsSchema';
import logger from '../../config/logger';

export class AssetsModule{
  private model: mongoose.Model<assets>;
  public storagepath;
  constructor (project_id:string, organization_id:string) {
    this.model=mongoose.model<assets>(`${project_id}${Models._ASSETS}`, AssetsSchema);
    this.storagepath='CreationtoolAssets/'+organization_id+'/projects/'+project_id+'/assets/';
  }
  public async createAsset (payload:object):Promise<assets | void>{
    logger.info('createAsset Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const asset = new this.model(payload);
      asset.save().then((res) => {
        logger.info('CreateAsset Succesfull', {response: res});
        resolve(res);
      }).catch((err:string) => {
        logger.error('Error in CreateAsset', {message: err});
        reject(err);
      });
    });
  }
  public async DeleteAsset
  (asset_id: string,
  ): Promise<assets | null> {
    logger.info('DeleteAsset Called', {asset_id: asset_id});
    return new Promise((resolve, reject) => {
      this.model.findOneAndDelete({ _id: asset_id }).then((deletedAsset: unknown) => {
        logger.info('DeleteAsset Successfull', {deletedAsset: deletedAsset});
        resolve(deletedAsset as assets);

      }).catch((err) => {
        logger.error('Error in DeleteAsset ', {message: err});
        reject(err);
      });
    });
  }
  public async getListofAssets (
    page?: number,
    limit?: number,
  ): Promise<{ items: Record<string, assets>; total: number }> {
    logger.info('getListofAssets Called', { page, limit });

    const skip = page && limit ? (page - 1) * limit : 0;
    const limitValue = limit || 0;

    const assetsList: Array<assets> = await this.model
      .find()
      .skip(skip)
      .limit(limitValue)
      .lean();

    const items = arrayToObject(assetsList) as Record<string, assets>;

    const total = await this.model.countDocuments();

    logger.info('getListofAssets Successful', { items });

    return { items, total };
  }

  public async UpdateAsset (payload:updateAssets):Promise<assets>{
    logger.info('UpdateAsset Called', {payload: payload});
    return new Promise<assets>((resolve, reject) => {
      this.model.findOneAndUpdate(
        { _id: payload.asset_id },
        {
          $set: {
            'file_type': payload.file_type,
            'file_url': payload.file_url,
            'file_name': payload.file_name,
            'media_type': payload.media_type,
            'updated_at': new Date().toISOString(),
          },
        },
        { new: true },
      ).then((res) => {
        logger.info('UpdateAsset Successfull', {response: res});
        resolve(res as assets);
      }).catch((error) => {
        logger.error('UpdateAsset Successfull', {message: error});
        reject(error);
      });

    });
  }

  public async CopyAsset (sourceUrl: string, destinationPath: string): Promise<string>{
    try {
      const downloadURL = await CopyFirebaseItem(sourceUrl, destinationPath);

      return downloadURL;
    } catch (error) {
      throw new Error('Failed to Copy the Asset '+ error);
    }
  }
  public async FindAsset (searchQuery: string): Promise<Array<assets> | null | string>{
    logger.info('Find Asset Called', {payload: searchQuery});
    try {
      const query = {
        $or: [
          { file_name: { $regex: searchQuery, $options: 'i' } },
          { file_type: { $regex: searchQuery, $options: 'i' } },
          { media_type: { $regex: searchQuery, $options: 'i' } },
        ],
      };

      const searchedResult = await this.model.find(query);
      logger.error('Find Asset successfull', {message: searchedResult});

      if (searchedResult.length >0){
        return searchedResult;
      }
      return 'Asset not Found !';
    } catch (error) {
      logger.error('Find Asset Unsuccessfull', {message: error});
      throw new Error('Asset not Found '+ error);
    }
  }

}

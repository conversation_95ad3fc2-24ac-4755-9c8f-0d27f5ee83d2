import logger from '../../config/logger';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { sidebarSchema } from '../../schema/sidebarSchema';
import { sidebarType, updateSidebarType, updateSingleOption } from '../../types/sidebar';
import mongoose from 'mongoose';
import { trashModule } from '../trash/index';
import { trashType } from '../../types/trash';
import { Models } from '../../types/extras';
import { invalidateCacheByPattern } from '../cdn';

export async function invalidateSidebarAPIs (organization_id: string, project_id: string): Promise<any> {
  try {

    const apiPaths = [
      `/publicapis/organization/${organization_id}/project/${project_id}/getOptions`,
    ];

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

export class SidebarModule {
  private model: mongoose.Model<sidebarType>;
  constructor (project_id:string) {
    this.model=mongoose.model<sidebarType>(`${project_id}${Models._SIDEBAR}`, sidebarSchema);
  }
  public async createOptions ( payload: sidebarType,
  ): Promise<sidebarType | void> {
    logger.info('createOptions Called', {payload: payload});
    return new Promise<sidebarType | void>((resolve, reject) => {
      const sidebarMod = new this.model(payload);
      sidebarMod.save().then((res) => {
        logger.info('createOptions Successfull', {response: res});
        resolve(res);
      })
        .catch((error) => {
          logger.error('Error in createOptions', {message: error});
          reject(error);
        });
    });
  }
  public async updateOptions ( payload: updateSingleOption,
  ): Promise<sidebarType | null> {
    return new Promise<sidebarType | null>((resolve, reject) => {
      logger.info('updateOptions Called', {payload: payload});
      if (payload.type === 'custom') {
        this.model.findOneAndUpdate(
          {_id: payload.id},
          {
            $set: {
              name: payload.name,
              type: payload.type,
              icon_id: payload.icon_id,
              order: payload.order,
              link: payload.link,
            },
            $unset: {
              scene_id: '',
            },
          },
          { new: true }, // Return the updated document
        )
          .then((res) => {
            logger.info('updateOptions Successfull', {response: res});
            resolve(res);
          })
          .catch((error) => {
            logger.error('Error in updateOptions', {message: error});
            reject(error);
          });
      } else {
        this.model.findOneAndUpdate(
          {_id: payload.id},
          {
            $set: {
              name: payload.name,
              type: payload.type,
              scene_id: payload.scene_id,
              icon_id: payload.icon_id,
              order: payload.order,
            },
            $unset: {
              link: '',
            },
          },
          { new: true }, // Return the updated document
        )
          .then((res) => {
            logger.info('updateOptions Successfull', {response: res});
            resolve(res);
          })
          .catch((error) => {
            logger.error('Error in updateOptions', {message: error});
            reject(error);
          });
      }
    });
  }
  public async updateBulkOptions (payload: updateSidebarType): Promise<string> {
    logger.info('updateBulkOptions Called', {payload: payload});
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<sidebarType | null>[] = payload.query.map(async (item) => {
        return this.model.findOneAndUpdate(
          { _id: item.id },
          {
            $set: {
              name: item.name,
              type: item.type,
              scene_id: item.scene_id,
              icon_id: item.icon_id,
              order: item.order,
            },
          },
          { new: true }, // Return the updated document
        )
          .then((res) => {

            return res;
          })
          .catch(() => {
            return null;
          });
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('updateBulkOptions Successfull', {results: results});
            resolve('Documents updated successfully');
          } else {
            logger.error('Error while updating sidebar option');
            reject('Error while updating sidebar option');
          }
        })
        .catch((error) => {
          logger.error('Error in updateBulkOptions', {message: error});
          reject(error);
        });
    });
  }

  // Public async getOptions (
  // ): Promise<object | void> {
  //   Const optionsList: Array<sidebarType> = await this.model.aggregate([
  //     { $match: { order: { $exists: true } } }, // Match documents that have the 'order' key
  //     { $sort: { ['order']: 1 } }, // Sort the matched documents by the specified field
  //   ]);
  //   Const unmatchedOptions: Array<sidebarType> = await this.model.find({ order: { $exists: false } });
  //   OptionsList.push(...unmatchedOptions);
  //   Const optionsObject = arrayToObject(optionsList) as Record<string, sidebarType>;
  //   Return optionsObject;
  // }
  public async getOptions (): Promise<object | void> {
    logger.info('getOptions Called');
    const optionsList: Array<sidebarType> = await this.model.aggregate([
      {
        $addFields: {
          hasOrder: { $cond: { if: { $eq: [{ $type: '$order' }, 'missing'] }, then: 0, else: 1 } },
        },
      },
      { $sort: { hasOrder: -1, ['order']: 1 } },
      { $unset: 'hasOrder' },
    ]);

    const optionsObject = arrayToObject(optionsList) as Record<string, sidebarType>;
    logger.info('getOptions Successful', {optionsObject: optionsObject});
    return optionsObject;
  }

  public async deleteOption
  (option_id:string,
  ): Promise<string> {
    logger.info('deleteOption Called', {option_id: option_id});
    return new Promise((resolve, reject) => {
      this.model.findOneAndDelete({_id: option_id}).then(() => {
        logger.info('deleteOption Successful');
        resolve('Option deleted sucessfully');
      }).catch((err) => {
        logger.error('Error in deleteOption');
        reject(err);
      });
    });
  }

  public async moveToTrash (
    sidebarIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<sidebarType | void> {
    logger.info('moveToTrash Successfull',
      {sidebarIds: sidebarIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: sidebarIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('sidebarIds corresponding to sidebar IDs provided not found');
      throw 'sidebarIds corresponding to sidebar IDs  provided not found';
    }
    const unitdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._SIDEBAR}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(unitdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: sidebarIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreSidebar (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoresidebar Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        await this.createOptions(item);
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoresidebar is Successfull');
        return 'sidebar got restored';
      });
    } else {
      logger.error('Error in restoresidebar');
      throw new Error('Failed to restore sidebar data from trash');
    }
  }
}

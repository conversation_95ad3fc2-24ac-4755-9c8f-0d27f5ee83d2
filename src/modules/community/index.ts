import mongoose from 'mongoose';
import { communitySchema } from '../../schema/communitySchema';
import { community, transformedCommunity } from '../../types/community';
import { AmenityModule } from '../amenity';
import { buildingModule } from '../building';
import { UnitModule } from '../units';
import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import logger from '../../config/logger';
import { trashModule } from '../trash/index';
import { trashType } from '../../types/trash';
import { Models } from '../../types/extras';
import { invalidateCacheByPattern } from '../cdn';

export async function invalidateCommunityAPIs (organization_id:string, project_id:string ): Promise<any> {
  try {
    const apiPaths = [
      // Community apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getCommunities`,
    ];

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

export class communityModule {
  private model: mongoose.Model<community>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<community>(
      `${project_id}${Models._COMMUNITIES}`,
      communitySchema,
    );
    this.storagepath='CreationtoolAssets/'+organization_id+'/projects/'+project_id+'/communities/';
  }
  public async CreateCommunity (
    payload:object,
  ): Promise<community | void> {
    logger.info('CreateCommunity Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const communityMod = new this.model(payload);
      communityMod.save().then((res) => {
        logger.info('CreateCommunity Suucessfull', {response: res});
        resolve(res);
      }).catch((err: string) => {
        logger.error('Error in CreateCommunity', {message: err});
        reject(err);
      });
    });
  }
  public async getCommunities ():Promise<transformedCommunity | null>{
    logger.info('getCommunities Called');
    const communities = await this.model.find();
    logger.info('getCommunities Suucessfull', {communities: communities});
    return communities.reduce((acc, communityData) => {
      acc[communityData._id] = communityData;
      return acc;
    }, {} as transformedCommunity);
  }
  public async getSyncUpdata
  (
    community_id: string,
    project_id:string,
    organization_id:string,
  ): Promise<object> {
    logger.info('getSyncUpdata Called',
      {community_id: community_id, project_id: project_id, organization_id: organization_id});
    const amenityMod = new AmenityModule(project_id, organization_id);
    const buildingMod = new buildingModule(project_id,  organization_id);
    const unitMod = new UnitModule(project_id);
    const amenitiesCountPromise = amenityMod.getAmenitiesCount(community_id);
    const buildingsCountPromise = buildingMod.getBuildingsCount(community_id);
    const unitsCountPromise = unitMod.getUnitsCount(community_id);

    return Promise.all([
      amenitiesCountPromise,
      buildingsCountPromise,
      unitsCountPromise,
    ]).then(([amenitiesCount, buildingsCount, unitsCount]) => {
      logger.info('getSyncUpdata Suucessfull',
        {amenitiesCount: amenitiesCount, buildingsCount: buildingsCount, unitsCount: unitsCount});
      return { amenitiesCount, buildingsCount, unitsCount };
    }).catch((err) => {
      logger.error('Error in CreateCommunity', {message: err});
      return err;
    });
  }
  public async getCommunityId (community_id: string): Promise<community> {
    logger.info('getCommunityById Called', {community_id: community_id});
    const community_data = await this.model.findById(community_id);
    if (community_data) {
      logger.info('getCommunityById Successfull', {community: community_data});
      return community_data as community;
    }
    logger.error(`Community with ID ${community_id} not found`);
    throw new Error(`Community with ID ${community_id} not found`);

  }
  public async UpdateCommunity (id: string, updateData: Partial<community>): Promise<community | null> {
    try {
      const updatedCommunity = await this.model.findOneAndUpdate(
        { _id: id },
        { $set: updateData },
        { new: true },
      );

      if (!updatedCommunity) {
        throw new Error('Community not found or not authorized');
      }

      return updatedCommunity;
    } catch (error) {
      throw new Error(`Error updating community: ${error}`);
    }
  }
  public async moveToTrash (
    communityIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<community | void> {
    logger.info('moveToTrash Successfull',
      {communityIds: communityIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: communityIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('COMMUNITYs corresponding to COMMUNITY IDs provided not found');
      throw 'COMMUNITYs corresponding to COMMUNITY IDs provided not found';
    }
    const COMMUNITYdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._COMMUNITIES}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(COMMUNITYdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: communityIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moceToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreCommunity (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreCommunity Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);
    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        await this.CreateCommunity(item);
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restore is Successfull');
        return 'COMMUNITY got restored';
      });
    } else {
      logger.error('Error in restoreCOMMUNITY');
      throw new Error('Failed to restore community data from trash');
    }
  }
}

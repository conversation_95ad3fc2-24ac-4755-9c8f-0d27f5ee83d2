import fs from 'fs';
import { checkFieldsSchema, projectsvgSchema } from '../../schema/projectsvgSchema';
import mongoose from 'mongoose';
import {
  coordinatesObjectReference,
  Layers,
  projectSVG,
  SchemaKeys,
  transformedSVG,
  updatePayload,
} from '../../types/projectSVG';
import { storageUpload } from '../../helpers/storageUpload';
import logger from '../../config/logger';
import { GetCoordinatesFromSvgUrl } from '../../helpers/projectSvg';
import { trashModule } from '../trash/index';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { trashType } from '../../types/trash';
import { Models } from '../../types/extras';
import { ProjectSceneModule } from '../projectScene';
interface layersObject {
  g?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  layer_id?: string;
  placement?: string;
  reSize?: boolean;
  zIndex?: number;
  maxZoomLevel?: number;
  minZoomLevel?: number;
  type?: string;
}

type bulkSVGUpdatePayload = {
    id:string,
    order?:number,
}

export type bulkUpdateSVG = {
    query:bulkSVGUpdatePayload[],
    project_id:string
}

export class ProjectSVGModule {
  public model: mongoose.Model<projectSVG>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<projectSVG>(
      `${project_id}${Models._SVGS}`,
      projectsvgSchema,
    );
    this.storagepath =
      'CreationtoolAssets/' +
      organization_id +
      '/projects/' +
      project_id +
      '/projectsvgs/';
  }
  public async UploadFiles (
    filename: string,
    modifiedSvg: string | object,
    scene_id: string,
    localfile?:string,
  ): Promise<string> {
    logger.info('UploadFiles Called', {filename: filename, modifiedSvg: modifiedSvg, scene_id: scene_id});
    return new Promise((resolve, reject) => {

      fs.writeFile(localfile?localfile:'../output.svg', modifiedSvg.toString(), async (err) => {
        if (err) {
          logger.error('Error:', {message: err});
          reject(err);
        }
        const uploadOptions = {
          destination: this.storagepath + scene_id + '/' + filename,
        };
        storageUpload(uploadOptions, localfile?localfile:'../output.svg').then(async (thumbnailUrl) => {
          logger.info('UploadFiles Successfull', {thumbnailUrl: thumbnailUrl});
          resolve(thumbnailUrl);
        })
          .catch((error) => {
            logger.error('Error in storageUpload', {error: error});
            reject(error);
          });
      });
    });
  }
  public async uploadVideoFiles (
    filename: string,
    videoPath: string,
    scene_id: string,
  ):Promise<string>{
    return new Promise((resolve, reject) => {
      const uploadOptions = {
        destination: this.storagepath + scene_id + '/' + filename,
      };
      storageUpload(uploadOptions, videoPath).then((thumbnailUrl) => {
        logger.info('UploadFiles Successfull', {thumbnailUrl: thumbnailUrl});
        resolve(thumbnailUrl);
      })
        .catch((error) => {
          logger.error('Error in storageUpload', {error: error});
          reject(error as string);
        });
    });
  }
  public async validateLayers (payload: object): Promise<object> {
    const arrayOfLayers = Object.values(payload);
    arrayOfLayers.map((item, index) => {
    // Setting Default Values
      if (!Object.prototype.hasOwnProperty.call(item, 'minZoomLevel')) {
        Object.assign(item, { minZoomLevel: 0 });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'maxZoomLevel')) {
        Object.assign(item, { maxZoomLevel: 100 });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'reSize')) {
        console.log('reSize---------------------------------------');
        Object.assign(item, { reSize: true }, { placement: null });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'showLabel')) {
        Object.assign(item, { showLabel: false });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'name')) {
        console.log('item', item);
        console.log('arrayofLayers', arrayOfLayers.length);
        Object.assign(item, { name: 'layer ' + (index + 1) });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'zIndex')) {
        Object.assign(item, { zIndex: 1 });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'scale')) {
        Object.assign(item, { scale: {x: 1, y: 1} });
      }
      return item;
    });

    const layersObject = arrayOfLayers.reduce((obj: object, item: Layers) => {
      const randomIdForLayers = item.layer_id;
      return Object.assign(obj, {
        [randomIdForLayers]: item,
      });
    }, {});

    return layersObject;
  }

  public async createSVG (payload: object): Promise<projectSVG | void> {
    logger.info('createSVG Called', {payload: payload});
    return new Promise((resolve, reject) => {
      console.log('1', payload);

      const svg = new this.model(payload);
      svg
        .save()
        .then((res) => {
          logger.info('createSVG Successfull', {response: res});
          resolve(res);
        })
        .catch((err: string) => {
          logger.error('Error in createSVG', {message: err});
          reject(err);
        });
    });
  }
  public async uploadGtagToFirebase (
    data: updatePayload,
    layer_id: string,
  ): Promise<string> {
    logger.info('uploadGtagToFirebase Called', {data: data, layer_id: layer_id});
    const storagePath = `layers/${layer_id}`;
    try {
      if (typeof data.g !== 'string') {
        throw new Error('Invalid data: g must be a string');
      }
      const svgContent = data.g.toString();
      const thumbnailUrl = await this.UploadFiles(
        'gTag.svg',
        svgContent,
        storagePath,
      );
      console.log('linkkkkkkkk', thumbnailUrl);

      if (thumbnailUrl){
        logger.info('uploadGtagToFirebase Successfull', {thumbnailUrl: thumbnailUrl});
      }
      return thumbnailUrl as string ;
    } catch (error) {
      console.error('Error uploading to Firebase Storage:', error);
      throw error;
    }
  }

  public async uploadGtagToFirebaseSvg (
    layersData: updatePayload,
  ): Promise<updatePayload | layersObject> {
    try {
      logger.info('uploadGtagToFirebaseSvg Called', {layersData: layersData});

      const data: { [key: string]: layersObject } = {};

      for (const key in layersData) {
        if (Object.prototype.hasOwnProperty.call(layersData, key)) {
          const layersObject: layersObject = layersData[key] as layersObject;

          // Const storagePath = `/layers/${layersObject.layer_id}`;

          if (layersObject.g){
            const svgContent = layersObject.g.toString();
            const thumbnailUrl = await this.UploadFiles(
              'gTag.svg',
              svgContent,
                layersObject.layer_id as string,
            );
            layersObject.g = thumbnailUrl;
            console.log('linkkkkkkkkkkkkk', thumbnailUrl);
            data[key] = layersObject;
          }
        }
      }

      return data;
    } catch (error) {
      console.error('Error uploading to Firebase Storage:', error);
      throw error;
    }
  }
  public async updateLayersVideo (
    payload: { [key: string]: string | updatePayload },
    videoTag : string | null,
  ): Promise<object | null | string>{
    logger.info('updateLayersVideo Called', { payload: payload });
    try {
      const updatedDocument = await this.model.findOneAndUpdate(
        {
          _id: payload.svg_id,
        },
        {
          $set: {
            [`layers.${payload.layer_id}.video_tag`]: videoTag,
          },
        },
        {
          new: true,
        },
      );

      logger.info('updateLayersVideo Successful', { updatedDocument: updatedDocument });
      return updatedDocument;
    } catch (err) {
      logger.error('Error in updateLayersVideo', { response: err });
      return err as string;
    }
  }
  public async updateLayers (
    payload: { [key: string]: string | updatePayload },
    organization_id?: string,
  ): Promise<object | null | string> {
    logger.info('updateLayers Called', { payload: payload });

    const query = payload.query as updatePayload;
    const g = query.g
      ? await this.uploadGtagToFirebase(query as updatePayload, payload.layer_id as string)
      : undefined;

    const data =
    query.type === 'landmark'
      ? {
        [`layers.${payload.layer_id}.landmark`]: {
          route_id: query.route_id,
          landmark_id: query.landmark_id,
        },
      }
      : query.type === 'project'
        ? {
          [`layers.${payload.layer_id}.project_id`]: query.project_id,
        }
        : query.type === 'image'
          ? {
            [`layers.${payload.layer_id}.image_id`]: query.image_id,
          }
          : query.type === 'project_scene'
            ? {
              [`layers.${payload.layer_id}.project_id`]: query.project_id,
              [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
            }
            : query.type === 'scene' || query.type === 'pin'
              ? {
                [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
              }
              : query.type === 'amenity'
                ? {
                  [`layers.${payload.layer_id}.amenity_id`]: query.amenity_id,
                }
                : query.type === 'community'
                  ? {
                    [`layers.${payload.layer_id}.community_id`]: query.community_id,
                    [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
                    [`layers.${payload.layer_id}.showLabel`]: query.showLabel,
                  }
                  : query.type === 'tower' || query.type === 'building'
                    ? {
                      [`layers.${payload.layer_id}.building_id`]: query.building_id,
                      [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
                      [`layers.${payload.layer_id}.showLabel`]: query.showLabel,
                    }
                    : query.type === 'toweroverlay'
                      ? {
                        [`layers.${payload.layer_id}.floor_id`]: query.floor_id,
                        [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
                        [`layers.${payload.layer_id}.building_id`]: query.building_id,
                      }
                      : query.type === 'floor'
                        ? {
                          [`layers.${payload.layer_id}.floor_id`]: query.floor_id,
                          [`layers.${payload.layer_id}.building_id`]: query.building_id,
                          [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
                        }
                        : query.type === 'units'
                          ? {
                            [`layers.${payload.layer_id}.units`]: query.units,
                          }
                          : query.type === 'label'
                            ? {
                              [`layers.${payload.layer_id}.title`]: query.title,
                              [`layers.${payload.layer_id}.category`]: query.category,
                            }
                            : query.type === 'amenitycategory'
                              ? {
                                [`layers.${payload.layer_id}.amenity_category`]: query.amenity_category,
                              }
                              : query.type === 'grouped_units'?
                                {
                                  [`layers.${payload.layer_id}.group_name`]: query.group_name,
                                  [`layers.${payload.layer_id}.showLabel`]: query.showLabel,
                                  [`layers.${payload.layer_id}.bedrooms`]: query.bedrooms,
                                }:{};

    let scaleX, scaleY;
    if (typeof query.scale === 'object' && query.scale !== null){
      scaleX = query.scale.x;
      scaleY = query.scale.y;
    }
    try {

      const document = await this.model.findById(payload.svg_id);
      const scene_id = document?.scene_id;
      const project_id = payload.project_id;
      if (scene_id && project_id && organization_id) {
        const projectScene = new ProjectSceneModule(String(project_id), organization_id);
        await projectScene.clearPreview(scene_id);
      }

      if (document && query.type) {
        const keysToUnset = Object.values(document.layers).flatMap((obj) => {
          if ( payload.layer_id === (obj as Layers).layer_id){
            return Object.keys(checkFieldsSchema).flatMap((key) => {
              if (query.type === key && key as SchemaKeys){
                return Object.keys(obj).filter((fields) => {
                  return !checkFieldsSchema[key as SchemaKeys].includes(fields);
                });
              }
              return [];
            });
          }
          return [];
        });
        const unsetFields = keysToUnset?.reduce((acc, key) => {
          acc[`layers.${payload.layer_id}.${key}`] = '';
          return acc;
        }, {} as Record<string, string>);

        console.log('unsetFields', unsetFields);

        // Remove Non-relevant fields to specific svg type
        await this.model.updateOne(
          { _id: payload.svg_id },
          {
            $unset: unsetFields,
          },
        );
      }
      const updatedDocument = await this.model.findOneAndUpdate(
        {
          _id: payload.svg_id,
        },
        {
          $set: {
            ['viewbox']: payload.viewbox,
            [`layers.${payload.layer_id}.type`]: query.type,
            [`layers.${payload.layer_id}.rotation`]: payload.rotation,
            [`layers.${payload.layer_id}.position`]: payload.position,
            [`layers.${payload.layer_id}.scale.x`]: scaleX,
            [`layers.${payload.layer_id}.scale.y`]: scaleY,
            [`layers.${payload.layer_id}.g`]: g,
            [`layers.${payload.layer_id}.x`]: query.x,
            [`layers.${payload.layer_id}.y`]: query.y,
            [`layers.${payload.layer_id}.placement`]: query.placement,
            [`layers.${payload.layer_id}.reSize`]: query.reSize,
            [`layers.${payload.layer_id}.zIndex`]: query.zIndex,
            [`layers.${payload.layer_id}.maxZoomLevel`]: query.maxZoomLevel,
            [`layers.${payload.layer_id}.minZoomLevel`]: query.minZoomLevel,
            [`layers.${payload.layer_id}.name`]: query.name,
            [`layers.${payload.layer_id}.height`]: query.height,
            [`layers.${payload.layer_id}.width`]: query.width,
            [`layers.${payload.layer_id}.showLabel`]: query.showLabel,
            [`layers.${payload.layer_id}.group_name`]: query.group_name,
            [`layers.${payload.layer_id}.bedrooms`]: query.bedrooms,
            ...data,
          },
        },
        {
          new: true,
        },
      );

      logger.info('updateLayers Successful', { updatedDocument: updatedDocument });
      return updatedDocument;
    } catch (err) {
      logger.error('Error in updateLayers', { response: err });
      return err as string;
    }
  }

  public async getSvgById (scene_id: string): Promise<transformedSVG | null> {
    logger.info('getSvgById Called', {scene_id: scene_id});
    const query = {
      scene_id: scene_id,
    };
    const project = await this.model.find(query);
    return project.reduce((acc, svg) => {
      acc[svg._id] = svg;
      logger.info('getSvgById Successfull', {returnValue: acc});
      return acc;
    }, {} as transformedSVG);
  }
  public async createLayers (
    payload: { [key: string]: string | updatePayload },
    randomId: string,
    svgurl?: string,
  ): Promise<object | null> {
    return new Promise((resolve, reject) => {
      console.log('Eroooooooo', randomId);
      const query = payload.query
        ? JSON.parse(payload.query as string)
        : undefined;
      const rotation = payload.rotation
        ? JSON.parse(payload.rotation as string)
        : undefined;
      const position = payload.position
        ? JSON.parse(payload.position as string)
        : undefined;
      const scale = payload.scale
        ? JSON.parse(payload.scale as string)
        : undefined;
      const type = query ? query.type : undefined;
      let data;
      if (query) {
        data =
          query.type === 'landmark'
            ? {
              [`layers.${randomId}.landmark`]: {
                route_id: query.route_id,
                landmark_id: query.landmark_id,
              },
            }
            : query.type === 'project'
              ? {
                [`layers.${randomId}.project_id`]: query.project_id,
              }
              : query.type === 'image'
                ? {
                  [`layers.${randomId}.image_id`]: query.image_id,
                }
                : query.type === 'project_scene'
                  ? {
                    [`layers.${randomId}.project_id`]: query.project_id,
                    [`layers.${randomId}.scene_id`]: query.scene_id,
                  }
                  : query.type === 'scene' || query.type === 'pin'
                    ? {
                      [`layers.${randomId}.scene_id`]: query.scene_id,
                    }
                    : query.type === 'amenity'
                      ? {
                        [`layers.${randomId}.amenity_id`]: query.amenity_id,
                      }
                      : query.type === 'community'
                        ? {
                          [`layers.${randomId}.community_id`]: query.community_id,
                          [`layers.${randomId}.scene_id`]: query.scene_id,
                        }
                        : query.type === 'tower' || query.type === 'building'
                          ? {
                            [`layers.${randomId}.building_id`]: query.building_id,
                            [`layers.${randomId}.scene_id`]: query.scene_id,
                          }
                          : query.type === 'toweroverlay'
                            ? {
                              [`layers.${randomId}.floor_id`]: query.floor_id,
                              [`layers.${randomId}.scene_id`]: query.scene_id,
                              [`layers.${randomId}.building_id`]: query.building_id,
                            }
                            : query.type === 'floor'
                              ? {
                                [`layers.${randomId}.floor_id`]: query.floor_id,
                                [`layers.${randomId}.building_id`]: query.building_id,
                                [`layers.${randomId}.scene_id`]: query.scene_id,
                              }
                              : query.type === 'units'
                                ? {
                                  [`layers.${randomId}.units`]: query.units,
                                }
                                : query.type === 'label'
                                  ? {
                                    [`layers.${randomId}.title`]: query.title,
                                    [`layers.${randomId}.category`]: query.category,
                                  }
                                  : query.type === 'amenitycategory'
                                    ? {
                                      [`layers.${payload.layer_id}.amenity_category`]:
                  query.amenity_category,
                                    }
                                    : query.type === 'grouped_units'
                                      ? {
                                        [`layers.${payload.scene_id}.group_name`]: query.group_name,
                                      }
                                      :{};
      }
      this.model
        .findOneAndUpdate(
          {
            _id: payload.svg_id,
          },
          {
            $set: {
              [`layers.${randomId}.type`]: type,
              [`layers.${randomId}.name`]: payload.name,
              [`layers.${randomId}.rotation`]: rotation,
              [`layers.${randomId}.position`]: position,
              [`layers.${randomId}.scale`]: scale,
              [`layers.${randomId}.layer_id`]: randomId,
              [`layers.${randomId}.svg_url`]: svgurl,
              ...data,
            },
          },
          {
            new: true,
          },
        ).then((updatedDocument) => {
          logger.info('createLayers successfull', {updatedDocument: updatedDocument});
          resolve(updatedDocument);
        })
        .catch((err) => {
          logger.error('Error in createLayers', {message: err});
          reject(err);
        });
    });
  }

  public async deleteLayers (
    svg_id:string,
    layer_id: string,
    organization_id: string,
  ): Promise<object | void | null> {
    logger.info('deleteLayers Called',
      {svg_idz: svg_id, layer_id: layer_id, organization_id: organization_id});
    try {
      const svg = await this.model.findOne({_id: svg_id});
      if (!svg){
        throw new Error('Invalid SVG ID');
      }
      const updatedDocument = await this.model.findOneAndUpdate(
        {
          _id: svg_id,
          [`layers.${layer_id}`]: { $exists: true },
        },
        {
          $unset: {
            [`layers.${layer_id}`]: '',
          },
        },
        { new: true },
      );
      if (!updatedDocument){
        throw new Error('Invalid Layer ID');
      }
      console.log('uuuu', updatedDocument);
      logger.info('deleteLayers Successful', { updatedDocument: updatedDocument });
      return updatedDocument;
    } catch (err){
      logger.error('Error in deleteLayers', {message: err});
      throw new Error(`${err}`);
    }

  }
  public async moveToTrash (
    svgIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<projectSVG | void> {
    logger.info('moveToTrash Successfull',
      {svgIds: svgIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: svgIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('SVGs corresponding to SVG IDs provided not found');
      throw 'SVGs corresponding to SVG IDs provided not found';
    }
    const SVGdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._SVGS}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(SVGdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: svgIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreSVG (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreSVG Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        await this.createSVG(item);
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreSVG is Successfull');
        return 'SVG got restored';
      });
    } else {
      logger.error('Error in restoreSVG');
      throw new Error('Failed to restore svg data from trash');
    }
  }

  public async bulkUpdateSVGs (payload: bulkUpdateSVG): Promise<string | null> {
    logger.info('updateBulkAmenities Called', {payload: payload});
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<layersObject | null>[] = payload.query.map(async (item) => {
        return this.model.findOneAndUpdate(
          { _id: item.id },
          {
            $set: {
              order: item.order,
            },
          },
          { new: true }, // Return the updated document
        )
          .then((res) => {
            return res;
          })
          .catch(() => {
            return null;
          });
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('updateBulkSVG successfully', {results: results});
            resolve('Documents updated successfully');
          } else {
            logger.error('Error while updating sidebar option');
            reject('Error while updating sidebar option');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
  public async convertType (scene_id: string): Promise<{[key: string]: { layers: coordinatesObjectReference }} | null> {
    logger.info('convert project svg type Called', { scene_id });

    try {
      const svgdata = await this.model.find({ scene_id });
      const svgObj: { [key: string]: { layers: coordinatesObjectReference }} = {};

      // Process each item and use Promise.all to wait for all asynchronous tasks to finish
      await Promise.all(
        svgdata.map(async (item) => {
          const res = await GetCoordinatesFromSvgUrl(item.svg_url);

          if (res) {
            svgObj[item._id] = {
              layers: res,
            };

            // Process each key in the result and upload files asynchronously
            await Promise.all(
              Object.keys(res).map(async (key) => {
                const svgContent = res[key].g.toString();
                const uniqueFilename = `${key}_gTag.svg`;

                // Upload the content and update the svgObj
                svgObj[item._id].layers[key].g = await this.UploadFiles(
                  uniqueFilename,
                  svgContent,
                  key as string,
                  `../${uniqueFilename}`,
                );
              }),
            );
          }
        }),
      );

      // Return the processed object for the first item or null if nothing was processed
      return svgObj;
    } catch (error) {
      logger.error('Error in convertType:', { error });
      throw error;
    }
  }

  public async layersConversion (payload: { [key: string]: { layers: coordinatesObjectReference }}): Promise<string> {
    // Collect all update promises
    const updatePromises = Object.keys(payload).map(async (key) => {
      const documentId = key;

      // Update the layers in the document
      const updates = Object.keys(payload[key].layers).map((layerId) => ({
        [`layers.${layerId}.g`]: payload[key].layers[layerId].g,
        [`layers.${layerId}.x`]: payload[key].layers[layerId].x,
        [`layers.${layerId}.y`]: payload[key].layers[layerId].y,
        [`layers.${layerId}.width`]: payload[key].layers[layerId].width,
        [`layers.${layerId}.height`]: payload[key].layers[layerId].height,
      }));

      // Build a single update query
      const updateQuery = updates.reduce((acc, update) => Object.assign(acc, update), {});

      // Return the promise for each updateOne operation
      return this.model.updateOne({ _id: documentId }, { $set: updateQuery });
    });

    // Wait for all promises to resolve
    await Promise.all(updatePromises);

    // Return success message once all updates are complete
    return 'All layers updated successfully';
  }

  public async editSVGZoomLevels (
    svg_id: string,
    minZoomLevel?: number, // For deepzoom functionality
    maxZoomLevel?: number, // For deepzoom functionality
    applyOnLayers?: boolean, // For deepzoom functionality
  ): Promise<projectSVG | null | string> {
    logger.info('editSVGZoomLevels Called', {
      svg_id: svg_id,
      minZoomLevel: minZoomLevel,
      maxZoomLevel: maxZoomLevel,
      applyOnLayers: applyOnLayers,
    });

    try {
      // First, get the SVG to check if it exists
      const svgData = await this.model.findById(svg_id);

      if (!svgData) {
        throw new Error('SVG not found');
      }

      // Build update data for SVG document level
      const updateData: { [key: string]: any } = {};

      if (minZoomLevel !== undefined) {
        updateData.minZoomLevel = minZoomLevel;
      }

      if (maxZoomLevel !== undefined) {
        updateData.maxZoomLevel = maxZoomLevel;
      }

      if (applyOnLayers !== undefined) {
        updateData.applyOnLayers = applyOnLayers; // For deepzoom functionality
      }

      // Validate that minZoomLevel is less than maxZoomLevel if both are provided
      if (minZoomLevel !== undefined && maxZoomLevel !== undefined) {
        if (minZoomLevel >= maxZoomLevel) {
          throw new Error('minZoomLevel must be less than maxZoomLevel');
        }
      }

      // If maxZoomLevel or minZoomLevel is provided and applyOnLayers is true, update all layers
      if (applyOnLayers === true && (minZoomLevel !== undefined || maxZoomLevel !== undefined) && svgData.layers) {
        const layerIds = Object.keys(svgData.layers);

        // Build update data for all layers using the same pattern as updateLayers
        layerIds.forEach((layerId) => {
          if (minZoomLevel !== undefined) {
            updateData[`layers.${layerId}.minZoomLevel`] = minZoomLevel;
          }
          if (maxZoomLevel !== undefined) {
            updateData[`layers.${layerId}.maxZoomLevel`] = maxZoomLevel;
          }
        });

        logger.info('Updating zoom levels for all layers', {
          layerCount: layerIds.length,
          minZoomLevel: minZoomLevel,
          maxZoomLevel: maxZoomLevel,
        });
      }

      // Update the document
      const updatedDocument = await this.model.findOneAndUpdate(
        { _id: svg_id },
        { $set: updateData },
        { new: true },
      );

      if (!updatedDocument) {
        throw new Error('Failed to update SVG');
      }

      logger.info('editSVGZoomLevels Successful', { updatedDocument: updatedDocument });
      return updatedDocument;

    } catch (err) {
      logger.error('Error in editSVGZoomLevels', { error: err });
      return err as string;
    }
  }

}

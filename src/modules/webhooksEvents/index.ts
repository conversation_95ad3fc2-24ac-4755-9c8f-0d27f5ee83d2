import mongoose from 'mongoose';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
import { WebhookEventsData } from '../../types/webhooks';
import { webhooksEventsSchema } from '../../schema/webhooksEvents';
import { TaskModule } from '../tasks';

export class WebhookEventsModule {
  private model:mongoose.Model<WebhookEventsData>;
  public storagepath;
  constructor (organization_id:string){
    this.model = mongoose.model<WebhookEventsData>(
      `${organization_id}${Models._WEBHOOKEVENTS}`,
      webhooksEventsSchema,
    );
    this.storagepath = 'CreationtoolAssets/'+organization_id+'/webhooksevents';
  }

  public async createEvents (payload : WebhookEventsData):Promise<object | void>{
    try {
      const WebhookEvents = new this.model(payload);

      const result = await WebhookEvents.save();

      logger.info('WebhookEvent created successfully', { result: result });

      const task = new TaskModule();
      const queueName = 'Webhooks';
      const taskId = `${result._id}_${result.eventType}`;

      await task.createTask(
        queueName,
        `${process.env.BASE_URL}webhookSubcription/executeEvent`,
        result,
        new Date().toISOString(),
        taskId,
        {},
      );

      logger.info('Cloud Task created for executeEvents', {
        taskId,
        eventId: result._id,
      });

      return result;

    } catch (error){
      logger.info('Error in creating WebhookEvent', { payload: payload });
      throw error;
    }

  }

  public async  executeEvents (payload:WebhookEventsData):Promise<object|void>{
    try {
      if (!payload._id || !payload.webhook_id) {
        logger.error('Missing _id or webhook_id in event', { payload });
        return { success: false, message: 'Missing required fields _id or webhook_id' };
      }

      const eventData = await this.model.findOne({ _id: payload._id });

      if (!eventData) {
        logger.info('No event data found', { _id: payload._id });
        return { success: false, message: 'Event data not found' };
      }

      const currentTime = new Date().toISOString(); // Get current time once
      try {

        const response = await fetch(eventData.targetUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            event: eventData.eventType,
            data: eventData,
          }),
        });

        const lastExecutionStatus = response.status === 200 ? 'success' : 'failure';

        // Prepare log entry
        const logEntry = {
          response: {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
          },
          message: response.ok
            ? 'Webhook delivered successfully'
            : `Webhook delivery failed with status ${response.status}`,
          time: currentTime,
        };

        await this.model.findOneAndUpdate(
          { _id: payload._id },
          {
            $push: { log: logEntry },
            $set: {
              lastExecutionStatus: lastExecutionStatus,
              lastExecutionTime: currentTime,
            },
          },
          { new: true },
        );

        if (!response.ok) {
          logger.error('Webhook delivery failed with non-2xx status', {
            targetUrl: eventData.targetUrl,
            status: response.status,
            statusText: response.statusText,
          });
          return {
            success: false,
            message: `Webhook delivery failed with status ${response.status}`,
          };
        }

        logger.info('Webhook sent successfully', {
          targetUrl: eventData.targetUrl,
          status: response.status,
        });

        return { success: true, message: 'Webhook delivered successfully' };
      } catch (error) {
        const errorLogEntry = {
          response: {},
          message: `Webhook delivery failed: ${error instanceof Error ? error.message : String(error)})`,
          time: currentTime,
        };

        await this.model.findOneAndUpdate(
          { _id: payload._id },
          {
            $push: { log: errorLogEntry },
            $set: {
              lastExecutionStatus: 'failure',
              lastExecutionTime: currentTime,
            },
          },
          { new: true },
        );

        logger.error('Webhook delivery failed', {
          targetUrl: eventData.targetUrl,
          error: error instanceof Error ? error.message : String(error),
        });

        return {
          success: false,
          message: `Webhook delivery failed: ${error instanceof Error ? error.message : String(error)}`,
        };
      }
    } catch (error) {
      logger.error('Error in webhook processing', {
        error: error instanceof Error ? error.message : String(error),
        payload,
      });

      return {
        success: false,
        message: `Processing error: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  public async getEvents (organization_id : string, targetUrl :string):Promise<object|void>{
    const result = await this.model.find({
      targetUrl: targetUrl,
    });

    if (!result || result.length === 0){
      logger.info('No events found');
      throw new Error(`No events found for this url ${targetUrl} in ${organization_id} organization`);
    }

    logger.info('getEvents Successful', {count: result.length});
    return result;
  }
}

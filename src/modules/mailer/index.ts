import { Session } from '../../types/session';
import { Leads } from '../../types/leads';
import { Organization } from '../../types/organization';
import { mailUserData } from '../../types/mailer';
import { createEvent } from 'ics';
import { convertToLocalDateTime, sendEmail } from '../../helpers/session';
import logger from '../../config/logger';

export class MailerModule{
  public async SendHostInviteMail (sessionData: Session,
    organizationData: Organization, userData: mailUserData): Promise<string | void> {
    try {
      logger.info('SendHostInviteMail Called', {sessionData: sessionData, organizationData: organizationData, userData: userData});
      const timezone = organizationData.organizationSettings?.salestool?.timezone  || 'Asia/Kolkata';
      const { localDate, localTime } =
    convertToLocalDateTime(sessionData.start, timezone);
      const organizationName = organizationData?.name;
      const subject = `Virtual Property Showcase - ${organizationName}`;
      const fullDomain = new URL(sessionData.invite_link).origin;
      const session_invite_link = fullDomain+'/salestool/preview/'+sessionData._id;
      const message = `<p>Dear ${userData.username},<br><br>
        We are pleased to inform you that your virtual property showcase session has been successfully scheduled.
        This presents an excellent opportunity for you to showcase your distinguished property to your clients.<br><br>
        Meeting Details:<br>
        - Organization: ${organizationName}<br>
        - Date: ${localDate}<br>
        - Time: ${localTime} 
        ${timezone}
        Please use the following link to join the meeting:<br>
        <a href="${session_invite_link}">Join the Virtual Property Showcase</a><br>
        Alternatively, you can join the meeting using the following code:<br>
        Meeting Code: ${sessionData.code}<br><br>
        Participants can join from any device, but for the best experience, 
        we recommend using a desktop or laptop with the latest Chrome browser.
        No additional software or account is required.<br><br>
        We look forward to your successful showcase.<br><br>`;
      console.log(message);

      const inviteData = {
        toAddress: userData.useremail,
        fromAddress: '<EMAIL>',
        message: message,
        subject: subject,
      };
      const event = {
        start: [
          new Date(sessionData.start).getFullYear(),
          new Date(sessionData.start).getMonth() + 1,
          new Date(sessionData.start).getDate(),
          new Date(sessionData.start).getHours(),
          new Date(sessionData.start).getMinutes(),
        ] as [number, number, number, number, number],
        duration: { hours: 1, minutes: 0 },
        title: `Virtual Property Showcase - ${organizationName}`,
        description: sessionData.description? sessionData.description: 'Join our virtual showcase meeting',
        location: session_invite_link,
        url: session_invite_link,
        organizer: { name: userData.username, email: userData.useremail },
      };
      const icsContent = await new Promise<string>((resolve, reject) => {
        createEvent(event, (error, value) => {
          if (error) {
            console.error('Error creating ICS file:', error);
            reject(new Error('Error creating ICS file: ' + error.message));
          } else {
            resolve(value);
          }
        });
      });

      const msg = {
        to: inviteData.toAddress,
        from: inviteData.fromAddress,
        subject: inviteData.subject,
        html: inviteData.message,
        attachments: [
          {
            filename: 'invite.ics',
            content: icsContent,
            contentType: 'text/calendar',
          },
        ],
        headers: {},
      };

      if (sessionData.thread_id) {
        msg.headers = {
          'In-Reply-To': sessionData.thread_id,
          'References': sessionData.thread_id,
        };
      }

      const mailResponse = await sendEmail(msg);
      const messageId = mailResponse.messageId;
      console.log('Mail sent successfully, messageId:', messageId);
      return messageId;

    } catch (err) {
      console.error('Error sending mail:', err);
      throw new Error('Error sending mail: ' + err);
    }
  }

  public async SendGuestInviteMail (sessionData: Session,
    leadData: Leads, organizationData: Organization, userData: mailUserData): Promise<string | void> {

    const timezone = organizationData.organizationSettings?.salestool?.timezone || 'Asia/Kolkata';

    const { localDate, localTime } =
    convertToLocalDateTime(sessionData.start, timezone);

    const organizationName = organizationData?.name;
    const subject = `Virtual Property Showcase - ${organizationName}`;

    const message = `<p>Dear ${leadData.name},<br><br>
          We would like to invite you to our virtual showcase meeting for the property that I am representing.
          The meeting will take place on 
          ${localDate} ${localTime} 
          ${timezone}. 
          To attend the meeting,
          you can click on the link provided below at the scheduled time.
          You can join the meeting from the comfort of your home or office, 
          and there's no need to download any software or create an account.
          You are free to use any device, but we strongly suggest you use a desktop or laptop with
          the latest Chrome browser for the best experience.<br><br>
          Link: <a href="${sessionData.invite_link}?lead_id=${leadData._id}">Click here to join</a><br><br>
          Alternatively, you can join the meeting using the following code:<br>
          Meeting Code: ${sessionData.code}<br><br>
          Look forward to showcasing the property to you.<br>
          Best regards,<br>
          ${userData.username || userData.useremail}</p>`;

    const inviteData = {
      toAddress: leadData.email,
      fromAddress: '<EMAIL>',
      message: message,
      subject: subject,
    };

    // Create ICS file content using the ics package
    const event = {
      start: [
        new Date(sessionData.start).getFullYear(),
        new Date(sessionData.start).getMonth() + 1,
        new Date(sessionData.start).getDate(),
        new Date(sessionData.start).getHours(),
        new Date(sessionData.start).getMinutes(),
      ] as [number, number, number, number, number],
      duration: { hours: 1, minutes: 0 },
      title: `Virtual Property Showcase - ${organizationName}`,
      description: sessionData.description? sessionData.description: 'Join our virtual showcase meeting',
      location: sessionData.invite_link,
      url: sessionData.invite_link,
      organizer: { name: userData.username, email: userData.useremail },
    };

    // Assuming createEvent is a function to create and send calendar events
    try {
      const icsContent = await new Promise<string>((resolve, reject) => {
        createEvent(event, (error, value) => {
          if (error) {
            logger.error('Error creating ICS file:', {message: error.message});
            reject(new Error('Error creating ICS file: ' + error.message));
          } else {
            resolve(value);
          }
        });
      });

      const msg = {
        to: inviteData.toAddress,
        from: inviteData.fromAddress,
        subject: inviteData.subject,
        html: inviteData.message,
        attachments: [
          {
            filename: 'invite.ics',
            content: icsContent,
            contentType: 'text/calendar',
          },
        ],
        headers: {},
      };

      if (sessionData.thread_id) {
        msg.headers = {
          'In-Reply-To': sessionData.thread_id,
          'References': sessionData.thread_id,
        };
      }

      const mailResponse = await sendEmail(msg);
      const messageId = mailResponse.messageId;
      return messageId;
    } catch (err) {
      throw new Error('Error sending mail: ' + err);
    }
  }
  public async SendGuestReminderMail
  (sessionData:Session, leadsData: Leads[], organizationData: Organization, userData: mailUserData):Promise<void>{
    logger.info('SendGuestReminderMail Called',
      {sessionData: sessionData, leadsData: leadsData, organizationData: organizationData, userData: userData});
    const organizationName = organizationData?.name;
    const timezone = organizationData.organizationSettings?.salestool?.timezone  || 'Asia/Kolkata';
    const { localDate, localTime } =
    convertToLocalDateTime(sessionData.start, timezone);
    const subject = `Virtual Property Showcase - ${organizationName}`;
    const mailPromises = leadsData.map(async (leadData) => {
      const message = `<p>Dear ${leadData.name},<br><br>
          This is a friendly reminder about our upcoming virtual
          showcase meeting for the property that I am representing.
          The meeting will take place on ${localDate} ${localTime} 
          ${timezone}. 
          To attend the meeting,
          you can click on the link provided below at the scheduled time. You can join the meeting
          from the comfort of your home or office, and there's no need to download any software or create an account.
          You are free to use any device, but we strongly suggest you use a desktop or laptop with
          the latest Chrome browser for the best experience.<br><br>
          Link: <a href="${sessionData.invite_link}?lead_id=${leadData._id}">Click here to join</a><br><br>
          We look forward to showcasing the property to you.<br>
          Best regards,<br>
          ${userData.username || userData.useremail}</p>`;

      const reminderData = {
        toAddress: leadData.email,
        fromAddress: '<EMAIL>',
        message: message,
        subject: subject,
      };

      const msg = {
        to: reminderData.toAddress,
        from: reminderData.fromAddress,
        subject: reminderData.subject,
        html: reminderData.message,
        headers: {},
      };
      if (sessionData.thread_id) {
        msg.headers = {
          'In-Reply-To': sessionData.thread_id,
          'References': sessionData.thread_id,
        };
      }
      try {
        await sendEmail(msg);
        logger.info('SendReminderMail Successfull', {messageId: msg});
      } catch (err) {
        logger.error('Error sending mail:', {message: err});
        throw new Error('Error sending mail: ' + err);
      }
    });

    await Promise.all(mailPromises);
  }
  public async sendHostReminderMail (sessionData:Session,
    organizationData: Organization, userData: mailUserData):Promise<string|null> {
    const organizationName = organizationData?.name;
    const timezone = organizationData.organizationSettings?.salestool?.timezone  || 'Asia/Kolkata';
    const fullDomain = new URL(sessionData.invite_link).origin;
    const session_invite_link = fullDomain+'/salestool/preview/'+sessionData._id;
    const { localDate, localTime } =
    convertToLocalDateTime(sessionData.start, timezone);
    // Send reminder to the host
    const hostSubject = `Virtual Property Showcase - ${organizationName}`;
    const hostMessage = `<p>Dear ${userData.username},<br><br>
      This is a friendly reminder about your upcoming virtual 
      showcase meeting for the property that you are representing.
      The meeting will take place on ${localDate} ${localTime} 
      ${timezone}. 
      Link: <a href="${session_invite_link}">Click here to join</a><br><br>`;

    const hostReminderData = {
      toAddress: userData.useremail,
      fromAddress: '<EMAIL>',
      message: hostMessage,
      subject: hostSubject,
    };

    const hostMsg = {
      to: hostReminderData.toAddress,
      from: hostReminderData.fromAddress,
      subject: hostReminderData.subject,
      html: hostReminderData.message,
      headers: {},
    };
    console.log('sendHostReminderMail', sessionData.thread_id);
    if (sessionData.thread_id) {
      hostMsg.headers = {
        'In-Reply-To': sessionData.thread_id,
        'References': sessionData.thread_id,
      };
    }
    try {
      const hostMailResponse = await sendEmail(hostMsg);
      const hostMessageId = hostMailResponse.messageId;
      return hostMessageId;
    } catch (err) {
      throw new Error('Error sending host reminder mail: ' + err);
    }

    return null;
  }
  public async notifyCancellationHost (sessionData: Session,
    userData: mailUserData, organizationData: Organization): Promise<string | null> {
    const organizationName = organizationData?.name;
    const timezone = organizationData.organizationSettings?.salestool?.timezone  || 'Asia/Kolkata';
    const { localDate, localTime } =
    convertToLocalDateTime(sessionData.start, timezone);
    const subject = `Virtual Property Showcase - ${organizationName}`;
    const message = `<p>Dear ${userData.username},<br><br>
      The upcoming virtual showcase meeting, scheduled for 
      ${localDate} ${localTime} 
      ${timezone} has been cancelled.
      Please review any necessary follow-up actions. <br><br>`;

    const notificationData = {
      toAddress: userData.useremail,
      fromAddress: '<EMAIL>',
      message: message,
      subject: subject,
    };

    const msg = {
      to: notificationData.toAddress,
      from: notificationData.fromAddress,
      subject: notificationData.subject,
      html: notificationData.message,
      headers: {},
    };
    if (sessionData.thread_id) {
      msg.headers = {
        'In-Reply-To': sessionData.thread_id,
        'References': sessionData.thread_id,
      };
    }

    try {
      const mailResponse = await sendEmail(msg);
      const messageId = mailResponse.messageId;
      return messageId;
    } catch (err) {
      throw new Error('Error sending mail to host: ' + err);
    }
  }
  public async notifyCancellationGuest
  (sessionData:Session, leadsData: Leads[], organizationData: Organization, userData: mailUserData):Promise<string|null>{
    logger.info('NotifyCancellation Called',
      {sessionData: sessionData, leadsData: leadsData, organizationData: organizationData, userData: userData});
    const organizationName = organizationData?.name;
    const timezone = organizationData.organizationSettings?.salestool?.timezone  || 'Asia/Kolkata';
    const { localDate, localTime } =
    convertToLocalDateTime(sessionData.start, timezone);
    for (const leadData of leadsData) {
      const subject = `Virtual Property Showcase - ${organizationName}`;
      const message = `<p>Dear ${leadData.name},<br><br>
          We regret to inform you that our virtual showcase meeting for 
          the property that I am representing, which was scheduled to 
          take place on ${localDate} ${localTime} 
          ${timezone}, 
          has been cancelled due to unforeseen circumstances.
          <br><br>
          We apologize for any inconvenience this may cause and will notify
          you of the new schedule as soon as possible. Thank you for your understanding.
          <br><br>
          Best regards,<br>
          ${userData.username || userData.useremail}</p>`;

      const notificationData = {
        toAddress: leadData.email,
        fromAddress: '<EMAIL>',
        message: message,
        subject: subject,
      };

      const msg = {
        to: notificationData.toAddress,
        from: notificationData.fromAddress,
        subject: notificationData.subject,
        html: notificationData.message,
        headers: {},
      };
      if (sessionData.thread_id) {
        msg.headers = {
          'In-Reply-To': sessionData.thread_id,
          'References': sessionData.thread_id,
        };
      }

      try {
        const mailResponse = await sendEmail(msg);
        const messageId = mailResponse.messageId;
        logger.info('NotifyCancellation Successfull', {messageId: messageId});
        return messageId;
      } catch (err) {
        logger.error('Error sending mail:', {message: err});
        throw new Error('Error sending mail: ' + err);
      }
    }
    return null;
  }
}

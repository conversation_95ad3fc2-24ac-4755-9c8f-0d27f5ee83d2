import mongoose from 'mongoose';
import {trashSchema} from '../../schema/trashSchema';
import { trashType } from '../../types/trash';
import logger from '../../config/logger';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { admin, bucketName } from '../../config/firebase';
import { Models } from '../../types/extras';

export async function deleteFileFromStorage (path: string): Promise<void> {
  if (!path) {
    return;
  }

  // Convert CDN URL back to Firebase Storage Path
  const firebaseDomain = 'firebasestorage.googleapis.com/v0/b/';
  const cdnUrl = process.env.VITE_APP_BUCKET_CDN || 'storagecdn.propvr.ai';

  let storagePath = path;
  if (path.includes(cdnUrl)) {
    storagePath = path.replace(cdnUrl, `${firebaseDomain}${bucketName}/o`);
  }

  // Extract the correct storage path
  const matches = storagePath.match(/\/o\/(.*?)\?/);
  if (!matches || matches.length < 2) {
    console.error('Invalid Firebase Storage URL:', path);
    return;
  }

  const filePath = decodeURIComponent(matches[1]); // Decode URL encoding

  try {
    await admin.storage().bucket(bucketName).file(filePath).delete();
  } catch (error) {
    console.error(`❌ Error deleting file: ${filePath}`, error);
  }
}

export class trashModule{
  public model: mongoose.Model<trashType>;
  constructor (organization_id:string) {
    this.model=mongoose.model<trashType>(`${organization_id}${Models._TRASH}`, trashSchema);
  }
  public async addtoTrash (payload: object): Promise<trashType |string> {
    logger.info('addtoTrash Called', {payload: payload});
    return new Promise<trashType |string>((resolve, reject) => {
      const trashData = new this.model(payload);
      trashData
        .save()
        .then((res) => {
          logger.info('addtoTrash Successful', {response: res});
          resolve(res);
        })
        .catch((err) => {
          logger.error('Error in addtoTrash', {message: err});
          reject(err);
        });
    });
  }
  public async restoreData (trash_id:string):Promise<trashType>{
    logger.info('restoreData Called', {trash_id: trash_id});
    return new Promise<trashType>((resolve, reject) => {
      const query = {
        _id: trash_id,
      };
      this.model.findOne(query).then((res) => {
        logger.info('restoreData Successful', {response: res});
        resolve( res as trashType);
      })
        .catch((err) => {
          logger.error('Error in restoreData', {message: err});
          reject(err);
        });
    });

  }
  public async deleteTrash (trash_ids:Array<string>):Promise<void>{
    logger.info('deleteTrash Called', {trash_ids: trash_ids});
    const FoundTrash = await this.model.findOne({
      _id: { $in: trash_ids.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    return new Promise<void>((resolve, reject) => {
      if (FoundTrash){
        this.model.deleteMany({
          _id: { $in: trash_ids.map((id) => new mongoose.Types.ObjectId(id)) },
        }).then((res) => {
          logger.info('deleteTrash Successful', {response: res});
          resolve();
        })
          .catch((err) => {
            logger.error('Error in deleteTrash', {message: err});
            throw new Error(`${err}`);
          });
      } else {
        logger.error('Trash Not Found');
        reject(new Error('Trash Not Found'));
      }

    });

  }
  public async getAllTrash (type?: string, project_id?: string, organization_id?:string, page?: number, limit?: number)
    : Promise<{ items: Record<string, trashType>; total: number }> {
    logger.info('Fetching trash items');
    const isPaginated = !!(type && (project_id || organization_id));
    const combinedType = project_id && type
      ? `${project_id.toLowerCase()}_${type}`
      : organization_id && type
        ? `${organization_id.toLowerCase()}_${type}`
        : undefined;
    console.log('combinedType', combinedType);
    const filter = combinedType ? { type: combinedType } : {};
    console.log('filter', filter);
    const skip =  page && limit ? (page - 1) * limit : 0;
    const limitValue =  limit ? limit : 0;
    console.log('skip', skip);
    console.log('limitValue', limitValue);

    if (isPaginated) {
      console.log('11111');
      const trashItems: Array<UnknownObject> = await this.model
        .find(filter)
        .skip(skip)
        .limit(limitValue)
        .lean();
      const items = arrayToObject(trashItems) as Record<string, trashType>;
      const total = await this.model.countDocuments(filter);
      logger.info('get all trash items Successfully with Pagination', { items: items });
      return { items, total };
    }
    console.log('222222');
    const trashItems: Array<UnknownObject> = await this.model.find(combinedType ? { type: combinedType } : {});
    const items = arrayToObject(trashItems) as Record<string, trashType>;
    const total = await this.model.countDocuments(filter);

    logger.info('get all trash items Successfull', {items: items});
    return {items, total};

  }
  public async getAllExpiredTrash (): Promise<Record<string, trashType>> {
    const now = new Date();
    const documents: Array<UnknownObject> = await this.model.find();

    const expiredDocuments = documents.filter((doc) => {
      if (!doc.createdAt) {
        return false;
      }
      const createdAt = new Date(doc.createdAt instanceof Date ? doc.createdAt : String(doc.createdAt));
      const expirationTime = new Date(createdAt.getTime() + (90 * 24 * 60 * 60 * 1000));
      return expirationTime < now;
    });

    console.log('Expired Documents:', expiredDocuments);

    const firebaseUrlPattern = /^https:\/\/firebasestorage\.googleapis\.com\/v0\/b\/[^/]+\/o\/.+/;

    for (const doc of expiredDocuments) {
      for (const key of Object.values(doc.data)) {
        for (const [field, value] of Object.entries(key)) {
          if (typeof value === 'string' && firebaseUrlPattern.test(value)) {
            console.log(`✅ Found Firebase URL in "${field}":`, value);
            await deleteFileFromStorage(value);
          }
        }
      }
    }

    try {
      await this.model.deleteMany({ _id: { $in: expiredDocuments.map((doc) => doc._id) } });
    } catch (error) {
      console.error('Error deleting expired records from the database:', error);
    }

    const items = arrayToObject(expiredDocuments) as Record<string, trashType>;

    return items;
  }

  public async getTrashedDataWithSearch (
    type: string,
    project_id: string,
    limit: number | null,
    pageSize: number | null,
    searchText: string,
  ): Promise<object | null> {
    logger.info('getTrashedDataWithSearch Called', {
      type, project_id, limit, pageSize, searchText,
    });

    // Calculate skip count for pagination (only if pagination is enabled)
    const skipCount = (pageSize && pageSize > 0 && limit) ? (pageSize * limit) - limit : 0;

    // Build aggregation pipeline to match type by regex and filter by name
    const aggregationPipeline: any[] = [
      {
        $match: {
          type: { $regex: `${type}$` }, // Ends with the provided type
        },
      },
      {
        $addFields: {
          dataArray: { $objectToArray: '$data' },
        },
      },
      {
        $unwind: '$dataArray',
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$dataArray.v',
              { trash_id: '$_id', timeStamp: '$timeStamp', trash_type: '$type' },
            ],
          },
        },
      },
    ];

    // Add search filter if searchText is provided - search by name field
    if (searchText && searchText.trim() !== '') {
      aggregationPipeline.push({
        $match: {
          name: { $regex: searchText, $options: 'i' },
        },
      });
    }

    // Add pagination only if pageSize > 0 (pagination is enabled)
    if (pageSize && pageSize > 0) {
      aggregationPipeline.push({
        $skip: skipCount,
      });
      aggregationPipeline.push({
        $limit: limit,
      });
    }

    // Get total count for this type
    const totalCount = await this.model.countDocuments({ type: { $regex: `${type}$` } });

    // Get search count if searchText is provided
    let searchCount = totalCount;
    if (searchText && searchText.trim() !== '') {
      const searchPipeline = [
        {
          $match: {
            type: { $regex: `${type}$` },
          },
        },
        {
          $addFields: {
            dataArray: { $objectToArray: '$data' },
          },
        },
        {
          $unwind: '$dataArray',
        },
        {
          $replaceRoot: {
            newRoot: '$dataArray.v',
          },
        },
        {
          $match: {
            name: { $regex: searchText, $options: 'i' },
          },
        },
      ];
      const matchingDocuments = await this.model.aggregate(searchPipeline);
      searchCount = matchingDocuments.length;
    }

    const result = await this.model.aggregate(aggregationPipeline);

    // Convert result array to object of objects keyed by _id
    const dataObject: Record<string, any> = {};
    for (const item of result) {
      if (item._id) {
        dataObject[item._id] = item;
      }
    }

    const resultData = {
      'totalCount': totalCount,
      'searchCount': searchCount,
      'data': dataObject,
    };
    logger.info('getTrashedDataWithSearch Successful', {resultData: resultData});
    return resultData;
  }
}

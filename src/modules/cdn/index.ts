import logger from '../../config/logger';
import redisConfig from '../../config/redis';

function buildRedisPattern (pattern: string): string {
  // Handle different wildcard patterns
  if (pattern.includes('/*')) {
    // Replace /* with * for Redis pattern matching
    pattern = pattern.replace('/*', '*');
  }

  if (pattern.includes('*')) {
    // If pattern already has wildcards, use it as is
    return `${pattern}*`;
  }
  // If no wildcards, add them to match all variations
  return `${pattern}*`;

}

export const invalidateCacheByPattern = async (patterns: string[]): Promise<{
    success: boolean;
    deletedKeys: number;
    patterns: string[];
  }> => {
  if (!redisConfig.isRedisConnected()) {
    return { success: false, deletedKeys: 0, patterns };
  }

  try {
    const client = redisConfig.getClient();
    if (!client) {
      return { success: false, deletedKeys: 0, patterns };
    }

    let totalDeletedKeys = 0;
    const processedPatterns: string[] = [];

    for (const pattern of patterns) {
      console.log('pattern', pattern);
      const redisPattern = buildRedisPattern(encodeURIComponent(pattern));
      processedPatterns.push(redisPattern);

      console.log(`Manual invalidation - searching for pattern: ${redisPattern}`);
      console.log('redisPattern', redisPattern);
      const keys = await client.keys(redisPattern);
      if (keys.length > 0) {
        await client.del(keys);
        totalDeletedKeys += keys.length;
        logger.info(`Manually invalidated ${keys.length} cache entries for pattern: ${pattern}`);
        console.log('Manually deleted keys:', keys);
      } else {
        logger.info(`No cache entries found for manual invalidation pattern: ${pattern}`);
      }
    }

    return {
      success: true,
      deletedKeys: totalDeletedKeys,
      patterns: processedPatterns,
    };
  } catch (error) {
    logger.error('Manual cache invalidation error:', error);
    return { success: false, deletedKeys: 0, patterns };
  }
};

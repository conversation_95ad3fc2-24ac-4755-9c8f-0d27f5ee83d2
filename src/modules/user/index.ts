import {
  getAuth,
  signInWithEmailAndPassword,
  UserCredential,
} from '@firebase/auth';
import { admin } from '../../config/firebase';
import { getUser } from '../../helpers/authUser';
import { Users, CreateUserInput } from '../../types/user';
import { User } from '../../types/organization';
import mongoose, { PipelineStage } from 'mongoose';
import { userSchema } from '../../schema/userSchema';
import { organizationSchema } from '../../schema/organizationsSchema';
import { OrganizationModule } from '../../modules/organization';
import { InvitesModule } from '../invites';
import { UserRecord } from 'firebase-admin/lib/auth/user-record';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
import { sendEmail } from '../../helpers/session';
import { loadEmailVerificationTemplate, loadPasswordResetTemplate } from '../../helpers/templateLoader';
const OrganizationModel = mongoose.model('Organization', organizationSchema);
const UserModel = mongoose.model('User', userSchema);
export class UserModule {
  private model: mongoose.Model<User>;

  constructor () {
    this.model = mongoose.model<User>(`${Models.USERS}`, userSchema);
  }
  public async CreateUser (
    userData: CreateUserInput,
  ): Promise<{ uid: string; email: string }> {
    logger.info('CreateUser Called', { userData: userData });
    try {
      const invitesCollection = new InvitesModule();
      const userInvites = await invitesCollection.findUserInvite(userData.email);
      new OrganizationModule();

      const user = {
        _id: userData.uid,
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        organization_id: [], // Initialize as needed
        role: userData.role,
      };

      const userCollection = new UserModel(user);
      await userCollection.save();

      if (userInvites && userInvites.length > 0) {
        await Promise.all(
          userInvites.map(async (userInvite) => {
            const user_data = {
              user_id: userData.uid,
              email: userData.email,
              first_name: userData.first_name || null,
              last_name: userData.last_name || null,
              organization_id: [],
            };
            await invitesCollection.AcceptInvite(user_data, userInvite);
          }),
        );
      }

      logger.info('CreateUser Successful', { uid: userData.uid, email: userData.email });
      return { uid: userData.uid, email: userData.email };

      logger.error('Error creating User');
      throw new Error('Error creating User.');
    } catch (error) {
      logger.error('Error in CreateUser', { message: error });
      throw new Error('Error Creating User: ' + error);
    }
  }

  public async UpdateUserDetails (userId: string, updateData: Partial<User>): Promise<User | null> {
    logger.info('UpdateUserDetails Called', { userId, updateData });

    try {
      const updatedUser = await this.model.findByIdAndUpdate(userId, updateData, { new: true });

      if (!updatedUser) {
        logger.error('User not found', { userId });
        return null;
      }

      logger.info('UpdateUserDetails Successful', { updatedUser });
      return updatedUser;
    } catch (error) {
      logger.error('Error updating user details', { message: error });
      throw new Error(`Error updating user details: ${error}`);
    }
  }

  public async AuthenticateUser (
    email: string,
    password: string,
  ): Promise<UserCredential> {
    logger.info('AuthenticateUser Called', {
      email: email,
      password: password,
    });
    return new Promise<UserCredential>((resolve, reject) => {
      const auth = getAuth();
      signInWithEmailAndPassword(auth, email, password)
        .then((userCredential) => {
          logger.info('AuthenticateUser Successfull', {
            userCredential: userCredential,
          });
          resolve(userCredential);
        })
        .catch((error) => {
          logger.error('Error in AuthenticateUser', { message: error.code });
          reject(error.code);
        });
    });
  }

  public ResetPassword (email: string, refererUrl: string): Promise<boolean> {
    logger.info('ResetPassword Called', { email });
    return new Promise<boolean>((resolve, reject) => {
      (async () => {
        try {
          // Check if user exists and get user details
          const firebaseUser = await admin.auth().getUserByEmail(email);

          // Get display name from Firestore users collection
          let displayName = 'User';
          try {
            const userDoc = await admin.firestore().collection('users').doc(firebaseUser.uid).get();
            if (userDoc.exists) {
              const userData = userDoc.data();
              displayName = userData?.displayName || userData?.first_name || 'User';
            }
          } catch (firestoreError) {
            logger.warn('Could not fetch user data from Firestore', { email, error: firestoreError });
          }

          // Generate password reset link
          const actionCodeSettings = {
            url: refererUrl,
            handleCodeInApp: true,
          };

          const resetLink = await admin
            .auth()
            .generatePasswordResetLink(email, actionCodeSettings);

          // Send email with template
          await sendEmail({
            from: '<EMAIL>',
            to: email,
            subject: 'Reset Your Password - PropVR Dashboard',
            html: loadPasswordResetTemplate(resetLink, displayName),
          });

          logger.info('Password reset email sent successfully', { email });
          resolve(true);

        } catch (error: unknown) {
          logger.error('Error sending password reset email', { email, error });
          console.log('error', error);

          // Handle specific Firebase errors with user-friendly messages
          const firebaseError = error as { code?: string; message?: string };
          let userMessage = 'Failed to send password reset email. Please try again later.';

          if (firebaseError?.code) {
            switch (firebaseError.code) {
              case 'auth/user-not-found':
                userMessage = 'No account found with this email address.';
                break;
              case 'auth/invalid-email':
                userMessage = 'Invalid email address provided.';
                break;
              case 'auth/too-many-requests':
                userMessage = 'Too many password reset attempts. Please try again later.';
                break;
              case 'auth/internal-error':
                if (firebaseError.message?.includes('TOO_MANY_ATTEMPTS_TRY_LATER')) {
                  userMessage = 'Too many attempts. Please try again later.';
                } else {
                  userMessage = 'Service temporarily unavailable. Please try again later.';
                }
                break;
              case 'auth/network-request-failed':
                userMessage = 'Network error. Please check your connection and try again.';
                break;
              case 'auth/operation-not-allowed':
                userMessage = 'Password reset is currently disabled. Please contact support.';
                break;
              default:
                userMessage = 'Unable to send password reset email. Please try again later.';
                break;
            }
          }
          reject(new Error(userMessage));
        }
      })();
    });
  }
  public SendEmailVerification (email: string, redirectionUrl: string): Promise<boolean> {
    logger.info('SendEmailVerification Called', { email });
    return new Promise<boolean>((resolve, reject) => {
      (async () => {
        try {
          // Check if user exists and get verification status
          const firebaseUser = await admin.auth().getUserByEmail(email);
          if (firebaseUser.emailVerified) {
            logger.info('Email already verified', { email });
            reject(new Error('Email already verified'));
            return;
          }

          // Get display name from Firestore users collection
          let displayName = 'User';
          try {
            const userDoc = await admin.firestore().collection('users').doc(firebaseUser.uid).get();
            if (userDoc.exists) {
              const userData = userDoc.data();
              displayName = userData?.displayName || userData?.first_name;
            }
          } catch (firestoreError) {
            logger.warn('Could not fetch user data from Firestore', { email, error: firestoreError });
          }

          // Generate verification link
          const actionCodeSettings = {
            url: redirectionUrl,
            handleCodeInApp: true,
          };
          const verificationLink = await admin.auth().generateEmailVerificationLink(email, actionCodeSettings);
          // Send email with template
          await sendEmail({
            from: '<EMAIL>',
            to: email,
            subject: 'Verify Your Email Address - PropVR Dashboard',
            html: loadEmailVerificationTemplate(verificationLink, displayName),
          });
          logger.info('Email verification sent successfully', { email });
          resolve(true);

        } catch (error) {
          logger.error('Error sending email verification', { email, error });
          reject(new Error('Failed to send email verification:' + error));
        }
      })();
    });
  }

  public async GetUserDetails (user_id: string): Promise<object | null> {
    logger.info('GetUserDetails Called', { user_id });
    try {
      let user = await UserModel.findOne({ _id: user_id });
      // If user is not found in MongoDB, check Firebase and migrate if needed
      if (!user) {
        const authUser = await getUser(user_id); // Fetch from Firebase
        if (authUser) {
          await this.migrateFromFirebase(authUser); // Migrate user if found in Firebase
          user = await UserModel.findOne({ _id: user_id }); // Fetch again after migration
        }
      }
      // If user still not found, return null
      if (!user) {
        logger.error('User not found in database or Firebase', { user_id });
        return null;
      }

      // Fetch organizations related to the user
      const orgList = user.organization_id || [];
      const organizations = await OrganizationModel.find(
        { _id: { $in: orgList } },
        { _id: 1, name: 1, thumbnail: 1 },
      );
      // Construct the response object with all required fields
      const response = {
        _id: user._id.toString(),
        email: user.email || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        phone: user.phone_number || '', // Ensure phone field is included
        role: user.role || '', // Default to 'User' if missing
        access: user.access || '', // Default to 'View' if missing
        profilePicture: user.profilePicture || '', // Default to empty string if missing
        organization_id: organizations, // List of organizations
        fcmToken: user?.fcmToken,
      };
      logger.info('GetUserDetails Successful', { response });
      return response;
    } catch (error) {
      logger.error('Unable to fetch user details', { user_id, message: error });
      throw new Error('Unable to fetch user details: ' + error);
    }
  }
  public SetDefaultOrg = (
    user_id: string,
    organization_id: string,
  ): Promise<{ last_org: string }> => {
    logger.info('SetDefaultOrg Called', {
      user_id: user_id,
      organization_id: organization_id,
    });
    return new Promise((resolve, reject) => {
      admin
        .firestore()
        .collection('users')
        .doc(user_id)
        .get()
        .then((userSnap) => {
          const userData = userSnap.data();
          if (
            userData &&
            userData.organization_id &&
            userData.organization_id.includes(organization_id)
          ) {
            admin.firestore().collection('users').doc(user_id).update({
              last_org: organization_id,
            });
          }
          reject('User Not found in this Organization');
        })
        .then(() => {
          logger.info('SetDefaultOrg Successfull', {
            last_org: organization_id,
          });
          resolve({ last_org: organization_id });
        })
        .catch((error) => {
          logger.error('Error in SetDefaultOrg', { message: error });
          reject(error);
        });
    });
  };
  public async ListUsersInOrganization (
    organizationId: string,
    filterQuery : string[],
  ): Promise<Users[] | null> {
    logger.info('ListUsersInOrganization Called', {
      organizationId: organizationId,
    });
    try {
      const aggregationPipeline: PipelineStage[] = [
        {
          $match: {
            _id: organizationId,
          },
        },
        {
          $unwind: {
            path: '$roles',
            preserveNullAndEmptyArrays: true,
          },
        }];
      if (filterQuery && filterQuery.length > 0) {
        aggregationPipeline.push({
          $match: {
            'roles.role': { $in: filterQuery },
          },
        });
      }
      aggregationPipeline.push(
        {
          $lookup: {
            from: 'users',
            localField: 'roles.user_id',
            foreignField: '_id',
            as: 'userDetails',
          },
        },
        {
          $unwind: {
            path: '$userDetails',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            user_id: '$roles.user_id',
            role: '$roles.role',
            email: '$roles.email',
            first_name: '$userDetails.first_name',
            last_name: '$userDetails.last_name',
            _id: 0,
          },
        },
      );
      const result = await OrganizationModel.aggregate(aggregationPipeline);
      logger.info('ListUsersInOrganization Successfull', { result: result });
      return result as Users[];
    } catch (error) {
      logger.info('Unable to fetch user roles:', { response: error });
      throw new Error('Unable to fetch user roles: ' + error);
      return null;
    }
  }
  public async migrateFromFirebase (user: UserRecord): Promise<object | null> {
    logger.info('migrateFromFirebase Called', { user: user });
    try {
      console.log('Migrating from Firebase', user.uid);

      const userData = {
        _id: user.uid,
        email: user.email,
        first_name: user.displayName,
        last_name: user.displayName,
        organization_id: [], // Initialize as needed
      };

      const userCollection = new UserModel(userData);
      await userCollection.save();

      const userDoc = userCollection;

      if (userDoc) {
        logger.info('migrateFromFirebase Successfull', { userDoc: userDoc });
        return userDoc;
      }
      logger.error('User document not found after saving.');

      return null;
    } catch (error) {
      logger.error('Error migrating from Firebase:', { message: error });

      throw error; // Re-throw the error to propagate it up the call stack if needed
    }
  }
  public async getUsername (user_id: string): Promise<string | null> {
    logger.info('getUsername Called', { user_id: user_id });
    const query = {
      _id: user_id,
    };

    const user = await this.model.findOne(query);
    if (user) {
      logger.info('getUsername Successfull', {
        user: user.first_name || user.email,
      });
      return user.first_name || user.email;
    }
    logger.error('Error in getUsername');
    return null;
  }
  public async getUserEmail (user_id: string): Promise<string | null> {
    logger.info('getUserEmail Called', { user_id: user_id });
    const query = {
      _id: user_id,
    };

    const user = await this.model.findOne(query);
    if (user) {
      logger.info('getUserEmail Successfull', { user: user.email });
      return user.email;
    }
    logger.error('Error in getUserEmail');
    return null;
  }
  public async getUserDetailsByEmail (email: string): Promise<boolean> {
    logger.info('getUserDetailsByEmail Called', { email: email });
    const query = {
      email: email,
    };
    const user = await this.model.findOne(query);
    if (user) {
      logger.info('getUserDetailsByEmail Successfull', { user: user });
      return true;
    }
    logger.error('Error in getUserDetailsByEmail');
    return false;
  }
  public async getUserIdByEmail (email: string): Promise<string | null> {
    logger.info('getUserIdByEmail Called', { email: email });
    const query = {
      email: email,
    };
    const user = await this.model.findOne(query);
    if (user) {
      logger.info('getUserIdByEmail Successfull', {
        user: user,
        userId: user._id,
      });

      return user._id.toString();
    }
    logger.error('Error in getUserIdByEmail');
    return null;
  }
  public async updateFcmToken (userId: string, fcmToken: string, action: 'add' | 'remove'): Promise<User | null> {
    logger.info('updateFcmToken Called', { userId, fcmToken, action });

    try {
      let updateOperation;

      if (action === 'add') {
        updateOperation = { $addToSet: { fcmToken: fcmToken } };
      } else {
        updateOperation = { $pull: { fcmToken: fcmToken } };
      }

      const updatedUser = await this.model.findByIdAndUpdate(
        userId,
        updateOperation,
        { new: true },
      );

      if (!updatedUser) {
        logger.error('User not found for FCM token operation', { userId });
        return null;
      }

      logger.info(`FCM token ${action === 'add' ? 'added' : 'removed'} successfully`, { userId });
      return updatedUser;
    } catch (error) {
      logger.error(`Error ${action === 'add' ? 'adding' : 'removing'} FCM token`, { userId, fcmToken, error });
      throw new Error(`Error updating FCM token: ${error}`);
    }
  }
}

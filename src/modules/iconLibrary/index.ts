import mongoose, { PipelineStage } from 'mongoose';
import { iconLibrarySchema } from '../../schema/iconLibrarySchema';
import { orgIconLibrarySchema } from '../../schema/orgIconLibrarySchema';
import { allIcons, iconLibrary, QueryParams, updateIconLibrary } from '../../types/iconLibrary';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
import { trashModule } from '../trash/index';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { trashType } from '../../types/trash';

export class IconLibraryModule{
  private model: mongoose.Model<iconLibrary>;
  public storagepath;
  constructor (organization_id?: string) {
    // If organization_id is provided, use org-specific collection, otherwise use global collection
    if (organization_id) {
      // Organization-specific collection: `${organization_id}_org_iconlibraries`
      this.model = mongoose.model<iconLibrary>(
        `${organization_id}${Models.ORG_ICONLIBRARY}`,
        orgIconLibrarySchema,
      );
      this.storagepath = `CreationtoolAssets/${organization_id}/iconlibrary/`;
    } else {
      // Global icons collection: `iconlibraries`
      this.model = mongoose.model<iconLibrary>(
        `${Models.ICONLIBRARY}`,
        iconLibrarySchema,
      );
      this.storagepath = 'CreationtoolAssets/projects/iconlibrary/';
    }
  }

  public async CreateIcon (payload: iconLibrary):Promise<iconLibrary | void>{
    logger.info('CreateIcon called', {payload: payload });
    return new Promise((resolve, reject) => {
      const iconLib = new this.model(payload);
      console.log(payload);
      iconLib
        .save()
        .then((res) => {
          logger.info('CreateIcon Successfull', {response: res});
          resolve(res);
        })
        .catch((err) => {
          logger.error('Error in CreateIcon', {message: err});
          reject(err);
        });
    });
  }

  public async UpdateIcon (icon_id : string, payload:updateIconLibrary):Promise<iconLibrary | void>{
    logger.info('UpdateIcon called', {payload: payload });
    try {
      const updatedIconLibrary = await this.model.findOneAndUpdate(
        { _id: icon_id },
        {
          $set: {
            name: payload.name,
            type: payload.type,
            category: payload.category,
          },
        },
        { new: true },
      );
      if (updatedIconLibrary) {
        logger.info('UpdateIcon Successfull', {updatedIconLibrary: updatedIconLibrary});
        return updatedIconLibrary;
      }
      logger.error('Error in updatedIconLibrary');
      throw new Error('Error in updatedIconLibrary');
    } catch (err) {
      logger.error('Error in UpdateIcon', {message: err});
      throw new Error('Error in UpdateIcon' + err);
    }
  }

  public async GetIcon (type: string, category:string):Promise<allIcons | void | string>{
    logger.info('GetIcon called', {type: type, category: category });
    try {
      const matchConditions: any = {};
      if (type) {
        matchConditions.type = type;
      }
      if (category) {
        matchConditions.category = category;
      }

      const icons = await this.model.aggregate([
        {
          $match: matchConditions,
        },
      ]);
      // Console.log('icons', icons);
      if (icons.length > 0){
        return icons.reduce((acc, icon) => {
          acc[icon._id.toString()] = icon;
          logger.info('GetIcon Successfull', {acc: acc});
          return acc;
        }, {} as allIcons);
      } else if (icons.length === 0) {
        logger.info('GetIcon Successfull', {message: 'No Icons Present in the DB' });
        return 'No Icons Present';
      }
      throw new Error('No Icons Found on type & category');

    } catch (err) {
      logger.error('Error fetching icons:', err);
      throw new Error(`Error fetching icons:${err}`);
    }
  }

  public async SearchIcon (query:QueryParams):Promise<Array<allIcons> | null>{

    logger.info('SearchIcon called', { query: query.searchText });

    if (!query.searchText) {
      return null;
    }

    const pipeline: PipelineStage[] = [];

    const matchConditions: any = {
      $or: [
        { 'name': { $regex: query.searchText, $options: 'i' } },
        { 'category': { $regex: query.searchText, $options: 'i' } },
      ],
    };

    pipeline.push({
      $match: matchConditions,
    });

    const searchResult = await this.model.aggregate(pipeline);
    if (searchResult.length > 0) {
      logger.info('SearchIcon Successfull', {result: searchResult});
      return searchResult;
    }
    return null;
  }

  public async moveToTrash (
    iconIds: Array<string>,
    organization_id: string,
    timeStamp: number,
  ): Promise<void> {
    logger.info('moveToTrash Called', {
      iconIds: iconIds,
      organization_id: organization_id,
      timeStamp: timeStamp,
    });

    if (!organization_id) {
      logger.error('Organization ID is required for moving icons to trash');
      throw new Error('Organization ID is required. This operation is only for organization icons.');
    }

    const trash = new trashModule(organization_id);

    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: iconIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });

    const documentsObj = arrayToObject(documents) as Record<string, trashType>;

    if (documents.length === 0) {
      logger.error('Icon IDs not found');
      throw new Error('Icon IDs not found');
    }

    // Collection type for organization icons (must match the format used by getAllTrash)
    const collectionType = `${organization_id.toLowerCase()}_org_iconlibraries`;

    const iconDataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: collectionType,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };

    await Promise.all([trash.addtoTrash(iconDataToInsertObj)])
      .then(async () => {
        console.log('**********Moved to Trash*********');
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: iconIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successful');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error deleting icons:', { message: err });
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', { message: err });
        throw err;
      });
  }

  public async restoreIcon (
    organization_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreIcon Called', {
      organization_id: organization_id,
      trash_Id: trash_Id,
    });

    if (!organization_id) {
      logger.error('Organization ID is required for restoring icons from trash');
      throw new Error('Organization ID is required. This operation is only for organization icons.');
    }

    const trash = new trashModule(organization_id);
    const trash_ids: Array<string> = [];
    trash_ids.push(trash_Id);

    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createIconPromises = Object.values(restoredData.data).map(async (item: any) => {
        // Create icon payload from restored data
        const iconPayload: iconLibrary = {
          _id: new mongoose.Types.ObjectId(item._id || new mongoose.Types.ObjectId()),
          name: item.name,
          type: item.type,
          category: item.category,
          iconURL: item.iconURL,
          iconHeight: item.iconHeight,
          iconWidth: item.iconWidth,
          organization_id: organization_id, // Always set organization_id for org icons
        };

        await this.CreateIcon(iconPayload);
      });

      await Promise.all(createIconPromises)
        .then(async () => {
          await trash.deleteTrash(trash_ids);
          logger.info('restoreIcon is Successful');
          return 'Icon got restored';
        })
        .catch((err) => {
          logger.error('Error in restoreIcon', { message: err });
          throw new Error('Failed to restore icon data from trash');
        });
    } else {
      logger.error('Error in restoreIcon - no data found');
      throw new Error('Failed to restore icon data from trash');
    }
  }
}

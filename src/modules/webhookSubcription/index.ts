import mongoose from 'mongoose';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
import { webhookData } from '../../types/webhooks';
import { WebhookSubcriptionSchema } from '../../schema/WebhookSubcriptionSchema';

export class WebhookSubcriptionModule{
  private model:mongoose.Model<webhookData>;
  public storagepath;
  constructor (organization_id:string){
    this.model = mongoose.model<webhookData>(
      `${organization_id}${Models._WEBHOOKSUBSCRIPTIONS}`,
      WebhookSubcriptionSchema,
    );
    this.storagepath = 'CreationtoolAssets/'+organization_id+'/webhooksubcription';
  }

  public async createWebhook (payload: Partial<webhookData>): Promise<object | void> {
    logger.info('createwebhook called');

    try {
      const webhook = new this.model(payload);
      await webhook.save();

      logger.info('webhook created successfully', { payload: payload });
      return payload;
    } catch (error) {
      logger.error('Error in creating webhook', { message: error });
      throw error;
    }
  }

  public async getWebhook (eventType?: string): Promise<object[] | null> {
    logger.info('getWebhook Called', { eventType });
    const webhooks = await this.model.find({});

    if (!webhooks || webhooks.length === 0) {
      logger.info('No webhook data found', { eventType });
      return null;
    }

    let filteredWebhooks = webhooks;
    if (eventType) {
      const normalizedEventType = eventType.trim().toLowerCase();
      filteredWebhooks = webhooks.filter((webhook: webhookData) => {
        return webhook.listOfEvents && webhook.listOfEvents.some((event: string) =>
          event.trim().toLowerCase() === normalizedEventType,
        );
      });
    }

    // Return array of webhook documents
    logger.info('getWebhook Successful', {count: filteredWebhooks.length, eventType});
    return filteredWebhooks.length > 0 ? filteredWebhooks : null;
  }

  public async setWebhook (payload: webhookData): Promise<webhookData | string> {

    try {
      const organization_id = payload.organization_id as string;
      const _id = payload._id as string;

      const existingDoc = await this.model.findOne({
        organization_id: organization_id,
        _id: _id,
      });

      if (!existingDoc){
        throw new Error('Document doesnot exist');
      }

      if (payload.name !== undefined) {
        existingDoc.name = payload.name;
      }
      if (payload.targetUrl !== undefined) {
        existingDoc.targetUrl = payload.targetUrl;
      }

      // Replace with payload values (handles add, remove, uniqueness)
      if (payload.listOfEvents && Array.isArray(payload.listOfEvents)) {
        const cleanedEvents = [...new Set(
          payload.listOfEvents
            .map((e: string) => e.trim())
            .filter((e: string) => e.length > 0),
        )];

        // Replace the entire list
        existingDoc.listOfEvents = cleanedEvents;
      }

      if (payload?.rules?.allowed_projects) {
        if (!existingDoc.rules) {
          existingDoc.rules = { allowed_projects: [] };
        }

        const cleanedProjects = [...new Set(payload.rules.allowed_projects)];

        // Replace the entire list (sync operation)
        existingDoc.rules.allowed_projects = cleanedProjects;
      }

      await existingDoc.save();
      return existingDoc;

    } catch (error) {
      logger.error('Error in setWebhooks', {message: error});
      throw new Error( `${error}`);
    }
  }

  public async deleteWebhook (_id: string): Promise<webhookData | null> {
    logger.info('deleteWebhook Called', { webhook_id: _id });

    try {
      const deletedWebhook : unknown = await this.model.findOneAndDelete({
        _id: _id,
      });

      if (!deletedWebhook) {
        logger.warn('Webhook not found for deletion', { _id });
        throw new Error( `Webhook not found for deletion ${_id}`);
      }

      logger.info('deleteWebhook Successful', { deletedWebhook });
      return deletedWebhook as webhookData;
    } catch (error) {
      logger.error('Error in deleteWebhook', { message: error, _id });
      throw error;
    }
  }
}

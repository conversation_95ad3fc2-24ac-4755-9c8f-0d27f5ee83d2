import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import { unitplanSchema } from '../../schema/unitplanSchema';
import { unitplanType, UnitplanInterface, unitType } from '../../types/unitplan';
import mongoose, { Types } from 'mongoose';
import { unitSchema } from '../../schema/UnitSchema';
import {
  Project,
} from '../../types/projects';
import { Models } from '../../types/extras';
import logger from '../../config/logger';
import { trashType } from '../../types/trash';
import { trashModule } from '../trash/index';

import { invalidateCacheByPattern } from '../cdn';

interface measurement{
  maxMeasurement:string,
  minMeasurement:string
}
interface measurementMap{
  [key:string]: measurement
}

type ObjectWithIdKey = Record<string, unitplanType>;

// Import {storageUpload} from '../../helpers/storageUpload';

export async function invalidateUnitplanAPIs (organization_id:string, project_id:string, unitplan_id?:string ): Promise<any> {
  try {

    const apiPaths = [
      `/publicapis/organization/${organization_id}/project/${project_id}/getListOfUnitplan`,

      // Units apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getListofUnits`,
      `/publicapis/organization/${organization_id}/project/${project_id}/filterunits`,

      // Unitplan apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getListOfUnitplan`,

      // Building apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getCountforBuilding`,
      `/publicapis/organization/${organization_id}/project/${project_id}/getFloorDetails`,

    ];

    if (unitplan_id) {
      apiPaths.push(`/publicapis/organization/${organization_id}/project/${project_id}/getUnitplan/${unitplan_id}`,
      );
    }

    const results = await invalidateCacheByPattern( apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

export class unitplanModule {
  private urlObject: { [key: string]: string } = {};
  private model: mongoose.Model<unitplanType>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<unitplanType>(
      `${project_id}${Models._UNITPLANS}`,
      unitplanSchema,
    );
    this.storagepath =
      'CreationtoolAssets/' +
      organization_id +
      '/projects/' +
      project_id +
      '/unitplans/';
  }
  public async createUnitplan (payload: object): Promise<object> {
    logger.info('createUnitplan Called', {payload: payload});
    return new Promise<object>((resolve, reject) => {
      const masScene = new this.model(payload);
      masScene
        .save()
        .then((res) => {
          logger.info('createUnitplan Successful', {res: res});
          resolve(res);
        })
        .catch((err) => {
          logger.error('Error in createUnitplan', {message: err});
          reject(err);
        });
    });
  }

  public async getListOfUnitplan (): Promise<object | null> {
    logger.info('getListOfUnitplan Called');
    const unitplans: Array<UnknownObject> = await this.model.find().lean();

    const unitplansobj = arrayToObject(
      unitplans.map(({ is_residential: _is_residential, ...rest }) => rest), // Remove is_residential field
    ) as Record<string, unitplanType>;
    logger.info('getListOfUnitplan Successful', {unitplansobj: unitplansobj});
    return unitplansobj;
  }

  public async getUnitplanWithSearch (project_id:string, limit:number | null, pageSize:number | null, searchText:string): Promise<object | null> {
    logger.info('getUnitplanWithSearch Called', {project_id: project_id, limit: limit, pageSize: pageSize, searchText: searchText});

    // Calculate skip count for pagination (only if pagination is enabled and values are provided)
    const skipCount = (pageSize && pageSize > 0 && limit) ? (pageSize * limit) - limit : 0;

    // Get total count of all unitplans (including villa_floor)
    const totalCount = await this.model.countDocuments({});
    let searchCount = 0;

    // Build aggregation pipeline
    const aggregationPipeline: any[] = [
      {
        $project: {
          _id: 1,
          project_id: 1,
          building_id: 1,
          type: 1,
          name: 1,
          thumbnail: 1,
          image_url: 1,
          measurement: 1,
          measurement_type: 1,
          tour_id: 1,
          bedrooms: 1,
          is_commercial: 1,
          bathrooms: 1,
          is_furnished: 1,
          unit_type: {
            $ifNull: ['$unit_type', unitType.FLAT],
          },
          floor_unitplans: 1,
          exterior_type: 1,
          scene_id: 1,
          gallery_id: 1,
          style: 1,
          balcony_measurement: 1,
          balcony_measurement_type: 1,
          suite_area: 1,
          suite_area_type: 1,
          hotspots: 1,
        },
      },
    ];

    // Add search filter if searchText is provided - search only by name within villa and flat
    if (searchText && searchText.trim() !== '') {
      aggregationPipeline.push({
        $match: {
          name: { $regex: searchText, $options: 'i' },
          unit_type: { $in: ['villa', 'flat', 'plot', 'office', 'shop'] }, // Only search in villa, flat, plot, office, shop
        },
      });

      // Count matching documents for search (only villa, flat, plot, office, shop)
      const query: any = {
        name: { $regex: searchText, $options: 'i' },
        unit_type: { $in: ['villa', 'flat', 'plot', 'office', 'shop'] },
      };
      const matchingDocuments = await this.model.find(query);
      searchCount = matchingDocuments.length;
    } else {
      // If no search text, include all unitplans (villa, flat, villa_floor, plot, office, shop)
      // No additional $match needed
    }

    // Add pagination only if pageSize > 0 and limit is provided (pagination is enabled)
    if (pageSize && pageSize > 0 && limit) {
      aggregationPipeline.push({
        $skip: skipCount,
      });
      aggregationPipeline.push({
        $limit: limit,
      });
    }

    const result = await this.model.aggregate(aggregationPipeline);

    // Convert result array to object of objects keyed by _id (like getListOfUnitplan)
    const dataObject: Record<string, any> = {};
    for (const item of result) {
      if (item._id) {
        dataObject[item._id] = item;
      }
    }

    const resultUnitplans = {
      'totalCount': totalCount,
      'searchCount': searchCount,
      'data': dataObject,
    };
    logger.info('getUnitplanWithSearch Successful', {resultUnitplans: resultUnitplans});
    return resultUnitplans;
  }

  public async getUnitPlanDataWithMeasurementRange (projectId:string):Promise<object> {
    logger.info('getUnitPlanDataWithMeasurementRange Called', {projectId: projectId});
    try {
      // Find all unitplans
      const unitPlans = await this.model.find().lean();

      // Find unitplans with measurement = 0
      const project_id=projectId as string;
      const units_model = mongoose.model<Project>(
        project_id+Models._UNITS,
        unitSchema,
      );

      const zeroMeasurementUnitPlans = await this.model.find({ measurement: 0 }).lean();
      const unitPlanIds = zeroMeasurementUnitPlans.map((unitPlan) => (unitPlan?._id));

      // Aggregate to calculate min and max measurements for units with matching unitplan_ids
      const measurementRangeMap:measurementMap = {};
      const aggregationResult = await units_model.aggregate([
        {
          $match: {
            unitplan_id: { $in: unitPlanIds },
          },
        },
        {
          $group: {
            _id: '$unitplan_id',
            minMeasurement: { $min: '$measurement' },
            maxMeasurement: { $max: '$measurement' },
          },
        },
      ]);// Process aggregation result to populate measurementRangeMap

      for (const result of aggregationResult) {
        const unitPlanId = result._id.toString();
        measurementRangeMap[unitPlanId] = {
          minMeasurement: result.minMeasurement,
          maxMeasurement: result.maxMeasurement,
        };
      }

      // Combine unitPlans data with measurement range for zero measurement unitplans
      const combinedResult = unitPlans.map((unitPlan) => {
        const unitPlanIdString = unitPlan._id.toString();
        if (unitPlanIdString in measurementRangeMap) {
          const { minMeasurement, maxMeasurement } = measurementRangeMap[unitPlanIdString];
          return {
            ...unitPlan,
            minMeasurement: minMeasurement,
            maxMeasurement: maxMeasurement,
          };
        }
        return unitPlan;

      });

      const resultantObject = combinedResult.reduce((acc, obj) => {
        acc[obj._id] = obj;
        return acc;
      }, {} as ObjectWithIdKey);
      logger.info('getUnitPlanDataWithMeasurementRange Successful', {resultantObject: resultantObject});
      return resultantObject;
    } catch (error) {
      logger.info('Error in getUnitPlanDataWithMeasurementRange', {message: error});
      throw error;
    }
  }

  public async updateUnitBedrooms (combinedResult: object): Promise<void> {
    for (const [key, unit] of Object.entries(combinedResult)) {
      console.log('key', key);
      const specialCases = ['studio', 'penthouse', 'townhouse', 'duplex', 'suite', 'podium'];
      if (
        !specialCases.includes(unit.bedrooms.toString().toLowerCase()) &&
          !unit.bedrooms.toString().includes('BHK')
      ) {
        unit.bedrooms = `${unit.bedrooms}BHK`;
        await this.model.findOneAndUpdate(
          { _id: key },
          { $set: { bedrooms: unit.bedrooms } },
          { new: true },
        );
      }
    }
  }

  public async getUnitplan (
    unitplan_id: string,
  ): Promise<unitplanType[] | null> {
    logger.info('getUnitplan Called', {unitplan_id: unitplan_id});
    const query = {
      _id: unitplan_id,
    };
    const unitplan = await this.model.findOne(query);
    logger.info('getUnitplan Successful', {unitplan: unitplan});
    return unitplan as unitplanType[] | null;
  }
  public async editUnitplan (
    unitplan_id: string,
    updatedData: UnitplanInterface,
    action:string,
  ): Promise<object> {
    logger.info('editUnitplan Called', {unitplan_id: unitplan_id, updatedData: updatedData, action: action});
    return new Promise<object>((resolve, reject) => {
      const { floor_unitplans, type, ...setData } = updatedData;
      const updateObj: any = action==='append'?{
        $set: setData,
        $push: {floor_unitplans: floor_unitplans},
      }:{
        $set: {...setData, floor_unitplans},
      };

      // If type is 'null' or null, add $unset to the update operation
      if (type === 'null' || type === null) {
        updateObj.$unset = { type: 1 };
      } else {
        // Otherwise, set the type field
        updateObj.$set.type = type;
      }

      this.model
        .findOneAndUpdate(
          { _id: unitplan_id },
          updateObj,
          { new: true },
        )
        .then((updatedUnitplan) => {
          if (!updatedUnitplan) {
            logger.info('editUnitplan is empty');
            resolve({});
          } else {
            logger.info('editUnitplan Successful', {updatedUnitplan: updatedUnitplan});
            resolve(updatedUnitplan.toObject());
          }
        })
        .catch((err) => {
          logger.error('Error in editUnitplan', {message: err});
          reject(err);
        });
    });
  }
  public async getStyles (): Promise<string[] | null>{
    logger.info('getStyles Called');
    try {
      const unitPlanItems = await this.model.distinct('style');
      logger.info('getStyles Successfull', {unitPlanItems: unitPlanItems});
      return unitPlanItems as string[] || null;
    } catch (error) {
      logger.error('Internal Server Error: in getStyles', {message: error});
      return null;
    }
  }
  public async createHotspots (unitplan_id: string, hotspots: object): Promise<object> {
    logger.info('createHotspots Called', { unitplan_id, hotspots });
    try {
      const hotspotData = {
        _id: new Types.ObjectId().toString(),
        ...hotspots,
      };
      const updatedHotspots = await this.model.findByIdAndUpdate(
        { _id: unitplan_id },
        {
          $set: {
            [`hotspots.${hotspotData._id}`]:
                  hotspotData,
          },
        },
        { new: true },
      );

      if (updatedHotspots){
        return updatedHotspots;
      }
      throw new Error('Error create hotspots');
    } catch (err) {
      throw new Error('' + err);
    }

  }

  public async editHotspots (unitplan_id: string, hotspot_id:string, hotspots: object): Promise<object> {
    logger.info('editHotspots Called', { unitplan_id, hotspot_id, hotspots });
    try {
      const previousHotspotData = <Record<string, string | number> | null> await this.model.findOne(
        {
          _id: unitplan_id,
          [`hotspots.${hotspot_id}`]: { $exists: true },
        },
      );

      logger.info('previousHotspotData', previousHotspotData);

      // Check tour data
      if (!previousHotspotData) {
        throw new Error('Document not found ');
      }

      const query = { _id: unitplan_id };

      // Create the update object
      const update: { $set: Record<string, string | number | null>, $unset?: Record<string, 1> } = {
        $set: {},
      };

      // Loop over hotspots to update existing keys dynamically
      for (const [key, value] of Object.entries(hotspots)) {
        const fieldPath = `hotspots.${hotspot_id}.${key}`;
        if (value === null) {
          if (!update.$unset) {
            update.$unset = {};
          }
          update.$unset[fieldPath] = 1;
        } else {
          update.$set[fieldPath] = value;
        }
      }

      if ('type' in hotspots){
      // Handle type-based field removal
        switch (hotspots.type) {
          case 'default':
            update.$unset = {
              ...(update.$unset || {}),
              [`hotspots.${hotspot_id}.group_id`]: 1,
              [`hotspots.${hotspot_id}.subgroup_id`]: 1,
              [`hotspots.${hotspot_id}.label_id`]: 1,
            };
            break;
          case 'label':
            update.$unset = {
              ...(update.$unset || {}),
              [`hotspots.${hotspot_id}.image_id`]: 1,
            };
            break;
          case 'group':
            update.$unset = {
              ...(update.$unset || {}),
              [`hotspots.${hotspot_id}.image_id`]: 1,
              [`hotspots.${hotspot_id}.subgroup_id`]: 1,
              [`hotspots.${hotspot_id}.label_id`]: 1,
            };
            break;

          case 'subgroup':
            update.$unset = {
              ...(update.$unset || {}),
              [`hotspots.${hotspot_id}.image_id`]: 1,
              [`hotspots.${hotspot_id}.label_id`]: 1,
            };
            break;

        }
      }

      const updatedHotspot = await this.model.findOneAndUpdate(query, update, {
        new: true,
      });
      if (updatedHotspot) {
        return updatedHotspot;
      }
      throw new Error('Error updating hotspot ');
    } catch (err) {
      throw new Error('' + err);
    }

  }

  public async deleteHotspots (unitplan_id: string, hotspot_id: string): Promise<object> {
    logger.info('deleteHotspots Called', { unitplan_id, hotspot_id });
    try {

      // Find and check existing
      const query = {
        _id: unitplan_id,
        [`hotspots.${hotspot_id}`]: { $exists: true },
      };

      // Removal key
      const update = {
        $unset: { [`hotspots.${hotspot_id}`]: '' },
      };

      const updatedHotspot = await this.model.findOneAndUpdate(query, update, {
        new: true,
      },
      );

      if (updatedHotspot) {
        return updatedHotspot;
      }

      throw new Error('Error in deleting hotspots');
    } catch (err) {
      throw new Error('Error in deleting hotspots' + err);
    }
  }

  public async getTypes (): Promise<string[] | null>{
    logger.info('getTypes Called');
    try {
      const unitPlanTypeItems = await this.model.distinct('type');
      logger.info('getTypes Successfull', {unitPlanTypeItems: unitPlanTypeItems});
      return unitPlanTypeItems as string[] || null;
    } catch (error) {
      logger.error('Internal Server Error: in getTypes', {message: error});
      return null;
    }
  }

  public async deleteVillaFloor (
    unitplan_id: string,
    parentID: string,
  ): Promise<void> {
    logger.info('deleteVillaFloor called', {unitplan_id: unitplan_id, parentID: parentID});

    try {
      // Remove the unitplan_id from parent's floor_unitplans array
      if (parentID) {
        await this.model.updateOne(
          { '_id': parentID },
          { $pull: { 'floor_unitplans': unitplan_id } },
        );
        logger.info('Removed unitplan_id from parent floor_unitplans', {unitplan_id: unitplan_id, parentID: parentID});
      }

      // Delete the villa floor unitplan document
      const deleteResult = await this.model.deleteOne({
        _id: unitplan_id,
      });

      if (deleteResult.deletedCount === 0) {
        throw new Error(`Villa floor with unitplan_id ${unitplan_id} not found`);
      }

      logger.info('Villa floor deleted successfully', {unitplan_id: unitplan_id, deletedCount: deleteResult.deletedCount});
    } catch (error) {
      logger.error('Error in deleteVillaFloor', {message: error, unitplan_id: unitplan_id, parentID: parentID});
      throw error;
    }
  }

  public async moveToTrash (
    unitplanIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<unitplanType | void> {
    logger.info('moveToTrash Successfull',
      {unitplanIds: unitplanIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: unitplanIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;

    const linkedFloorUnitplanIds: string[] = [];
    const linkedTrashIds: string[] = [];

    for (const document of documents) {
      if (document.unit_type === 'villa' && document.floor_unitplans && Array.isArray(document.floor_unitplans) && document.floor_unitplans.length > 0) {
        linkedFloorUnitplanIds.push(...document.floor_unitplans);
        logger.info('Found villa with floor_unitplans', {
          villa_id: document._id,
          floor_unitplans: document.floor_unitplans,
        });
      }
    }

    if (linkedFloorUnitplanIds.length > 0) {
      const linkedFloorDocuments: Array<UnknownObject> = await this.model.find({
        _id: { $in: linkedFloorUnitplanIds.map((id) => new mongoose.Types.ObjectId(id)) },
      });

      if (linkedFloorDocuments.length > 0) {
        const linkedFloorDocumentsObj = arrayToObject(linkedFloorDocuments) as Record<string, trashType>;

        const linkedFloorTrashObj: trashType = {
          _id: new mongoose.Types.ObjectId(),
          type: `${project_id.toLowerCase()}${Models._UNITPLANS}`,
          timeStamp: timeStamp,
          data: linkedFloorDocumentsObj,
          linked_trashes: [],
          root: false,
        };

        await trash.addtoTrash(linkedFloorTrashObj);
        linkedTrashIds.push(linkedFloorTrashObj._id.toString());

        await this.model.deleteMany({
          _id: { $in: linkedFloorUnitplanIds.map((id) => new mongoose.Types.ObjectId(id)) },
        });

        logger.info('Linked floor unitplans moved to trash', {
          linkedFloorUnitplanIds: linkedFloorUnitplanIds,
          trashId: linkedFloorTrashObj._id,
        });
      }
    }

    const UnitplandataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._UNITPLANS}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: linkedTrashIds,
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(UnitplandataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: unitplanIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }

  public async restoreUnitplan (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreUnitplan Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      if (restoredData.linked_trashes && restoredData.linked_trashes.length > 0) {
        logger.info('Found linked floor unitplans to restore', {
          mainTrashId: trash_Id,
          linkedTrashIds: restoredData.linked_trashes,
        });

        for (const linkedTrashId of restoredData.linked_trashes) {
          const linkedRestoredData = await trash.restoreData(linkedTrashId);
          if (linkedRestoredData) {
            const createLinkedUnitplansPromise = Object.values(linkedRestoredData.data).map(async (item) => {
              await this.createUnitplan(item);
            });
            await Promise.all(createLinkedUnitplansPromise);
            trash_ids.push(linkedTrashId);
            logger.info('Restored linked floor unitplans', {
              linkedTrashId: linkedTrashId,
              restoredCount: Object.keys(linkedRestoredData.data).length,
            });
          }
        }
      }

      const createMainUnitplansPromise = Object.values(restoredData.data).map(async (item) => {
        await this.createUnitplan(item);
      });

      await Promise.all(createMainUnitplansPromise).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreUnitplan Successfull');
        return 'Unitplan got restored';
      });
    } else {
      logger.error('Error in restoreUnitplan - no data found');
      throw new Error('Failed to restore unitplan data from trash');
    }
  }

  public async getUnitNamesByUnitplanId (unitplan_id: string): Promise<string[]> {
    logger.info('getUnitNamesByUnitplanId Called', {unitplan_id: unitplan_id});
    try {
      // Extract project_id from the current model name
      const modelName = this.model.modelName;
      const project_id = modelName.replace(Models._UNITPLANS, '');

      // Create units model for the same project
      const units_model = mongoose.model<Project>(
        project_id + Models._UNITS,
        unitSchema,
      );

      // Query units collection to get unit names where unitplan_id matches
      const units = await units_model.find(
        { unitplan_id: unitplan_id },
        { name: 1, _id: 0 }, // Only select name field
      ).lean();

      // Extract unit names from the result
      const unitNames = units.map((unit) => unit.name).filter((name) => name); // Filter out null/undefined names

      logger.info('getUnitNamesByUnitplanId Successful', {unitNames: unitNames});
      return unitNames;
    } catch (error) {
      logger.error('Error in getUnitNamesByUnitplanId', {message: error});
      throw error;
    }
  }
}

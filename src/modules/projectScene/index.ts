import { projectscenesSchema } from '../../schema/projectsceneSchema';
import {
  deep_zoom_status,
  projectScene,
  projectSceneType,
  transformedProjectScene,
  updateBulkSceneType,
  updateProjectSceneObj,
} from '../../types/projectScene';
import mongoose from 'mongoose';
import { Models } from '../../types/extras';
import { trashModule } from '../trash/index';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { trashType } from '../../types/trash';
import { ProjectSVGModule } from '../projectSVG';
import { projectSVG } from '../../types/projectSVG';
import logger from '../../config/logger';
import { invalidateCacheByPattern } from '../cdn';
// Import {storageUpload} from '../../helpers/storageUpload';

export async function invalidateSceneAPIs (organization_id:string, project_id:string): Promise<any> {
  try {

    const apiPaths = [
      // Project scene apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getAllScenes`,
      `/publicapis/organization/${organization_id}/project/${project_id}/getProjectSceneId`,
    ];

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

export class ProjectSceneModule {
  private urlObject: { [key: string]: string } = {};
  private model: mongoose.Model<projectScene>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<projectScene>(
      `${project_id}${Models._PROJECT_SCENES}`,
      projectscenesSchema,
    );
    this.storagepath =
      'CreationtoolAssets/' +
      organization_id +
      '/projects/' +
      project_id +
      '/projectscenes/';
  }
  // Public async UploadFiles (
  //   RequestFiles:
  //     | { [fieldname: string]: Express.Multer.File[] }
  //     | Express.Multer.File[],
  //   Scene_id:string,
  // ): Promise<{ [key: string]: string }> {
  //   Return new Promise<{ [key: string]: string }>((resolve, reject) => {
  //     Try {
  //       Const uploadPromises = Object.values(requestFiles).map(
  //         (file: Express.Multer.File[]) => {
  //           Return new Promise<void>((innerResolve, innerReject) => {
  //             Const uploadOptions = {
  //               Destination:
  //                 This.storagepath+scene_id+'/'
  //                 +file[0].originalname,
  //             };
  //             StorageUpload(uploadOptions, file[0].path).then((thumbnailUrl) => {
  //               This.urlObject[file[0].fieldname] = thumbnailUrl;
  //               Return innerResolve();
  //             })
  //               .catch((err) => {
  //                 Return innerReject(err);
  //               });
  //           });
  //         },
  //       );
  //       Promise.all(uploadPromises).then(() => {
  //         Resolve(this.urlObject);

  //       });
  //     } catch (error) {
  //       Reject(error);
  //     }
  //   });
  // }
  public async createScene (payload: object): Promise<projectScene | void> {
    logger.info('createScene Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const masScene = new this.model(payload);
      console.log(payload);
      masScene
        .save()
        .then((res) => {
          logger.info('createScene Successfull', {response: res});
          resolve(res);
        })
        .catch((err) => {
          logger.error('Error in createScene', {message: err});
          reject(err);
        });
    });
  }
  public async getAllScenes (
    type: string = '',
    parent: string = '',
  ): Promise<transformedProjectScene | null> {
    logger.info('getAllScenes Called', {type: type, parent: parent});
    const masterScenes =
      type && parent
        ? await this.model.aggregate([
          { $match: { type: type, parent: parent } },
          {
            $addFields: {
              hasOrder: {
                $cond: {
                  if: { $eq: [{ $type: '$order' }, 'missing'] },
                  then: 0,
                  else: 1,
                },
              },
            },
          },
          { $sort: { hasOrder: -1, ['order']: 1 } },
          { $unset: 'hasOrder' },
        ])
        : await this.model.find({ type: { $ne: 'rotatable_image_frame' } });
    return masterScenes.reduce((acc, scene) => {
      acc[scene._id] = { sceneData: scene, svgData: {} };
      logger.info('getAllScenes Successfull', {acc: acc});
      return acc;
    }, {} as transformedProjectScene);
  }
  public async getScene (scene_id: string): Promise<projectScene> {
    logger.info('getScene Called', {scene_id: scene_id});
    const query = {
      _id: scene_id,
    };
    const projectScene_data = await this.model.findOne(query);
    logger.info('getScene Successfull', {projectScene_data: projectScene_data});
    return projectScene_data as projectScene;
  }
  public async updateFloors (
    scene_id: string,
    floor_ids: Array<string>,
    building_id: string,
  ): Promise<projectScene | null> {
    logger.info('updateFloors Called', {scene_id: scene_id, floor_ids: floor_ids});
    const existingScene = await this.model.findOne({ _id: scene_id }); // Fetch existing session doc
    if (!existingScene) {
      logger.error('Cannot find scene');
      throw new Error('Cannot find scene');
    }
    const updatedScene = await this.model.findOneAndUpdate(
      { _id: scene_id },
      {
        $set: {
          floor_ids: floor_ids,
          building_id: building_id,
        },
      },
      { new: true },
    );
    if (updatedScene) {
      logger.info('updateFloors Successfull', {updatedScene: updatedScene});
      return updatedScene as projectScene;
    }
    logger.error('Error in updating coordinate settings');
    throw new Error('Error in updating coordinate settings');
    return null;
  }
  public async getProjectSceneId (floor_id: string): Promise<string | null> {
    logger.info('getProjectSceneId Called', {floor_id: floor_id});
    const scenes = await this.model.findOne({ floor_ids: { $in: [floor_id] } });
    if (scenes) {
      logger.info('getProjectSceneId Successfull', {scenes: scenes._id});
      return scenes._id;
    }
    return null;
  }
  public async UpdateScene (
    scene_id: string,
    updateProjectObj: updateProjectSceneObj,
  ): Promise<projectScene | void> {
    logger.info('UpdateScene Called', {scene_id: scene_id, updateProjectObj: updateProjectObj});
    try {
      const sceneData  = await this.model.findOne({_id: scene_id});

      const updatedProjectScene = await this.model.findOneAndUpdate(
        { _id: scene_id },
        {
          $set: {
            name: updateProjectObj.name,
            type: updateProjectObj.type,
            parent: updateProjectObj.parent,
            info_text: updateProjectObj.info_text,
            active: updateProjectObj.active,
            clouds: updateProjectObj.clouds,
            root: updateProjectObj.root,
            category: updateProjectObj.category,
            [`frames.${updateProjectObj.frame_id}.id`]:
              updateProjectObj.frame_id,
            position: updateProjectObj.position,
            polar_angle: updateProjectObj.polar_angle,
            distance: updateProjectObj.distance,
            auto_rotate: updateProjectObj.auto_rotate,
            'background.high_resolution': sceneData?.type === projectSceneType.DEEP_ZOOM? updateProjectObj.highRes:undefined,
            'background.high_resolution_night': sceneData?.type === projectSceneType.DEEP_ZOOM? updateProjectObj.highResNight:undefined,
            minZoomLevel: sceneData?.type === projectSceneType.DEEP_ZOOM? updateProjectObj.minZoomLevel:undefined,
            maxZoomLevel: sceneData?.type === projectSceneType.DEEP_ZOOM? updateProjectObj.maxZoomLevel:undefined,
            deep_zoom_status: updateProjectObj.deep_zoom_status,
            deep_zoom_failed_info: sceneData?.type === projectSceneType.DEEP_ZOOM?
              updateProjectObj.deep_zoom_failed_info:undefined,
            'background.high_resolution_copy': updateProjectObj.high_resolution_copy,
            'viewbox.width': updateProjectObj.width,
            'viewbox.height': updateProjectObj.height,
            direction: (() => {
              const sceneType = updateProjectObj.type || sceneData?.type;
              if (sceneType === projectSceneType.GSPLAT || sceneType === projectSceneType.ROTATABLE_IMAGE) {
                return null;
              }
              return updateProjectObj.direction;
            })(),
            north_direction: updateProjectObj.north_direction ?? null,
            preview: updateProjectObj.preview,
          },
        },
        { new: true },
      );
      if (updatedProjectScene) {
        logger.info('UpdateScene Successfull', {updatedProjectScene: updatedProjectScene});
        return updatedProjectScene;
      }
      logger.error('Error in updating project scene data');
      throw new Error('Error in updating project scene data');
    } catch (error) {
      logger.error('Error in UpdateScene', {message: error});
      throw new Error('Internal Server Error' + error);
    }
  }

  public async updateBulkSceneFrame (
    payload: updateBulkSceneType,
  ): Promise<string> {
    logger.info('updateBulkSceneFrame Called', {payload: payload});
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<projectScene | null>[] = payload.query.map(
        async (item) => {
          return this.model
            .findOneAndUpdate(
              { _id: item.id },
              {
                $set: {
                  order: item.order,
                },
              },
              { new: true }, // Return the updated document
            )
            .then((res) => {
              return res;
            })
            .catch(() => {
              return null;
            });
        },
      );

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('updateBulkSceneFrame Successfull', {results: results});
            resolve('Documents updated successfully');
          } else {

            reject('Error while updating Scenes');
          }
        })
        .catch((error) => {
          logger.error('Error in updateBulkSceneFrame', {message: error});
          reject(error);
        });
    });
  }

  public async clearPreview (scene_id: string): Promise<void> {
    await this.model.updateOne(
      { _id: scene_id, preview: { $exists: true } },
      { $unset: { preview: 1 } },
    );
  }

  public async UpdateSceneFiles (
    scene_id: string,
    updateProjectObj: updateProjectSceneObj,
    sceneType : string,
  ): Promise<projectScene | void> {
    console.log('UpdateSceneFiles Called', {scene_id: scene_id, updateProjectObj: updateProjectObj});
    logger.info('UpdateSceneFiles Called', {scene_id: scene_id, updateProjectObj: updateProjectObj});
    try {
      await this.model.updateOne(
        { _id: scene_id, preview: { $exists: true } },
        { $unset: { preview: 1 } },
      );

      const updatedProjectScene = await this.model.findOneAndUpdate(
        { _id: scene_id },
        {
          $set: {
            ...(updateProjectObj.lowRes?{'background.low_resolution': updateProjectObj.lowRes}:{}),
            ...(updateProjectObj.lowResNight?{'background.low_resolution_night': updateProjectObj.lowResNight}:{'background.low_resolution_night': null}),
            ...(sceneType === 'deep_zoom' ? updateProjectObj.file_url &&  {'background.high_resolution': ''}:{'background.high_resolution': updateProjectObj.highRes}),
            ...(sceneType === 'deep_zoom' ? (updateProjectObj.file_url_night ?  {'background.high_resolution_night': ''}:{'background.high_resolution_night': null}): (updateProjectObj.highResNight?{'background.high_resolution_night': updateProjectObj.highResNight}:{'background.high_resolution_night': null})),
            video: updateProjectObj.video as string,
            info_icon: updateProjectObj.info_icon as string,
            deep_zoom_status: sceneType === 'deep_zoom' ? deep_zoom_status.NOT_STARTED:undefined,
            deep_zoom_failed_info: undefined,
          },
        },
        { new: true },
      );
      if (updatedProjectScene) {
        logger.info('UpdateSceneFiles Successfull', {updatedProjectScene: updatedProjectScene});
        console.log('updatedProjectScene', updatedProjectScene);
        return updatedProjectScene;
      }
      logger.error('Error in updating project scene data');
      throw new Error('Error in updating project scene data');
    } catch (error) {
      logger.error('Error in updateSceneFiles', {message: error});
      throw new Error('Internal Server Error' + error);
    }
  }
  public async deleteSceneFiles (
    scene_id: string,
    deleteFileObj: updateProjectSceneObj,
  ): Promise<projectScene | void> {
    logger.info('deleteSceneFiles Called', {scene_id: scene_id, deleteFileObj: deleteFileObj});
    try {
      const updatedProjectScene = await this.model.findOneAndUpdate(
        { _id: scene_id },
        {
          $set: {
            'background.low_resolution': deleteFileObj.lowRes,
            'background.high_resolution': deleteFileObj.highRes,
            video: deleteFileObj.video,
            info_icon: deleteFileObj.info_icon,
          },
        },
        { new: true },
      );
      if (updatedProjectScene) {
        logger.info('deleteSceneFiles Successfull', {updatedProjectScene: updatedProjectScene});
        return updatedProjectScene;
      }
      logger.error('Error in deleting files from project scene data');
      throw new Error('Error in deleting files from project scene data');
    } catch (error) {
      logger.error('Error in deleteSceneFiles', {message: error});
      throw new Error('Internal Server Error' + error);
    }
  }
  public async moveToTrash (
    sceneIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<projectScene | void> {
    logger.info('moveToTrash Successfull',
      {sceneIds: sceneIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    interface linkedTrashData {
      _id: mongoose.Types.ObjectId;
      type: string;
      data: object;
      timeStamp: number;
      root:false
    }
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: sceneIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    const linked_trashes: { [key: string]: mongoose.Types.ObjectId } = {};
    const linked_trashIds: Array<string> = [];
    if (documents.length === 0) {
      logger.error('Scenes corresponding to scene IDs provided not found');
      throw 'Scenes corresponding to scene IDs provided not found';
    }
    const svg = new ProjectSVGModule(project_id, organization_id);
    const svgdocuments: Array<UnknownObject> = await svg.model.find({
      scene_id: { $in: sceneIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const svgdocumentsObj = arrayToObject(svgdocuments) as Record<
      string,
      projectSVG
    >;
    linked_trashes.svgdata = new mongoose.Types.ObjectId();
    linked_trashIds.push(linked_trashes.svgdata.toString());
    const svgdataToInsertObj: linkedTrashData = {
      _id: linked_trashes.svgdata,
      type: `${project_id.toLowerCase()}${Models._SVGS}`,
      timeStamp: timeStamp,
      data: svgdocumentsObj,
      root: false,
    };
    const scenedataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._PROJECT_SCENES}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: linked_trashIds,
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(svgdataToInsertObj),
      trash.addtoTrash(scenedataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: sceneIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
          svg.model.deleteMany({
            scene_id: {
              $in: sceneIds.map((id) => new mongoose.Types.ObjectId(id)),
            },
          }),
        ])
          .then(() => {
            logger.info('moceToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreScenes (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<projectScene | void> {
    logger.info('restoreScenes Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const svg = new ProjectSVGModule(project_id, organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const restoreLinkedTrashesPromises = restoredData.linked_trashes.map(
        async (trashId) => {
          trash_ids.push(trashId);
          const linkedData = await trash.restoreData(trashId);
          if (linkedData.data){
            Object.values(linkedData.data).forEach((obj) => {
              svg.createSVG(obj);
            });
          }
        },
      );

      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        await this.createScene(item);
      });

      await Promise.all([
        ...restoreLinkedTrashesPromises,
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreScenes is Successfull');
        return 'Scene got restored';
      });
    } else {
      logger.error('Error in restoreScenes');
      throw new Error('Failed to restore scene data from trash');
    }
  }
}

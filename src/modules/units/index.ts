import mongoose, { PipelineStage } from 'mongoose';
import { Units, unitFilterQuery, unitListResponse } from '../../types/units';
import { unitSchema } from '../../schema/UnitSchema';
import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import { ppgUnitType, webhookData, webhookResult } from '../../types/webhooks';
import { Models } from '../../types/extras';
import {
  Project,
} from '../../types/projects';
import logger from '../../config/logger';
import { trashModule } from '../trash/index';
import { trashType } from '../../types/trash';
import { invalidateCacheByPattern } from '../cdn';
import { WebhookSubcriptionModule } from '../webhookSubcription';
import { WebhookEventsModule } from '../webhooksEvents';

export async function invalidateUnitAPIs (
  organization_id:string,
  project_id:string,
): Promise<{success: boolean; message: string; data: unknown}> {
  try {

    const apiPaths = [
      `/publicapis/organization/${organization_id}/project/${project_id}/getListofUnits`,
      `/publicapis/organization/${organization_id}/project/${project_id}/getCountForUnits`,
      `/publicapis/organization/${organization_id}/project/${project_id}/filterunits`,
      `/publicapis/organization/${organization_id}/project/${project_id}/filterDataPoints`,
      `/publicapis/organization/${organization_id}/project/${project_id}/getParsedUnits`,

      // Project apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getProject`,
      `/publicapis/organization/${organization_id}/project/${project_id}/getCountforProject`,

      // Unitplan apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getListOfUnitplan`,

      // Building apis
      `/publicapis/organization/${organization_id}/project/${project_id}/getFloorDetails`,
      // Community apis

      `/publicapis/organization/${organization_id}/project/${project_id}/getCommunities`,

    ];

    const results = await invalidateCacheByPattern( apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

export class UnitModule {
  private model: mongoose.Model<Units>;
  private sessionId: string | undefined;
  constructor (project_id: string, session_id?:string) {
    if (session_id){
      this.sessionId = session_id;
    }
    const model_name = this.sessionId ?`${this.sessionId}_`+project_id + Models._UNITS:project_id + Models._UNITS;
    this.model = mongoose.model<Units>(model_name, unitSchema);
  }
  public async createUnit (payload: object, organization_id?: string): Promise<object | void> {
    logger.info('createUnit Called', {payload: payload});
    try {
      const unit = new this.model(payload);
      if (this.sessionId !== undefined) {
        unit.isNew = true;
      }
      const savedUnit = await unit.save();
      logger.info('createUnit Successful', {payload: payload});

      // Trigger webhooks if organization_id is provided
      if (organization_id) {
        const webhook = new WebhookSubcriptionModule(organization_id);
        const webhookEvent = new WebhookEventsModule(organization_id);
        const project_id = (payload as Units).project_id;

        const webhookSubscriptions = await webhook.getWebhook('unit_created') as webhookData[];
        if (webhookSubscriptions && webhookSubscriptions.length > 0) {
          const queuedWebhookTasks = webhookSubscriptions
            .filter((webhookSub: webhookData) => {
              const allowedProjects = webhookSub.rules?.allowed_projects;
              const isAllowed = allowedProjects && allowedProjects.length > 0 && allowedProjects.includes(project_id);
              return isAllowed && webhookSub.targetUrl;
            })
            .map((sub: webhookData) =>
              webhookEvent.createEvents({
                webhook_id: sub._id,
                organization_id,
                eventType: 'unit_created',
                targetUrl: sub.targetUrl,
                data: savedUnit,
                log: [],
                _id: new mongoose.Types.ObjectId().toString(),
              }),
            );

          Promise.allSettled(queuedWebhookTasks).then((results) => {
            results.forEach((result, index) => {
              const sub = webhookSubscriptions[index];
              if (result.status === 'rejected') {
                logger.error('Failed to create event during unit creation', {
                  Error: result.reason,
                  unit_id: (savedUnit as Units)?._id?.toString(),
                  webhook_id: sub._id,
                });
              } else {
                if (result.status === 'fulfilled' && !(result as webhookResult).value.success) {
                  logger.error('Failed to create event during unit creation', {
                    Error: (result as webhookResult).value,
                    eventId: (result as webhookResult).value._id,
                    webhook_id: sub._id,
                  });
                }
                logger.info('Webhook event created during unit creation', {
                  eventId: (result as webhookResult).value._id,
                  webhook_id: sub._id,
                });
              }
            });
          });
        }
      }

      return payload;
    } catch (error) {
      logger.error('Error in createUnit', {message: error});
      throw error;
    }
  }

  public async getAllUnits (): Promise<object | null> {
    logger.info('getAllUnits Called');
    const units : Array<UnknownObject> = await this.model.find();
    const unitsObj = arrayToObject(units) as Record<string, Units>;
    logger.info('getAllUnits Successful', {unitsObj: unitsObj});
    return unitsObj;
  }

  public async getUnitsByName (unitName:string):Promise<Units | null>{
    logger.info('getUnitsByName Called');
    const unit = await this.model.findOne({name: unitName});
    logger.info('getUnitsByName Successful', {unit: unit});
    return unit;

  }

  public async getAllUnitsWithPagination (project_id:string, limit:number, pageSize:number, searchText:string,
  ): Promise<unitListResponse | null> {
    logger.info('getAllUnitsWithPagination Called',
      {project_id: project_id, limit: limit, pageSize: pageSize, searchText: searchText});
    const skipCount = (pageSize * limit)-limit || 0;
    const totalCount = await this.model.countDocuments();
    let searchCount = 0;

    const units_model = mongoose.model<Project | Units>(
      project_id+Models._UNITS,
      unitSchema,
    );

    // Simpler approach - get one document to extract field structure + all metadata
    const [sampleDoc, allMetadata] = await Promise.all([
      units_model.findOne({}).lean(),
      units_model.find({ metadata: { $exists: true, $ne: null } }).select('metadata').lean(),
    ]);

    const allKeys = new Set<string>();

    // Add regular fields from sample document
    if (sampleDoc) {
      Object.keys(sampleDoc).forEach((key) => {
        if (key !== 'metadata' && key !== 'created_at') {
          allKeys.add(key);
        }
      });
    }

    // Parse all metadata to get unique keys
    allMetadata.forEach((doc) => {
      let metadata = (doc as Units).metadata;
      try {
        if (typeof metadata === 'string') {
          try {
            metadata = JSON.parse(metadata);
          } catch (parseError) {
            return;
          }
        }
        if (metadata && typeof metadata === 'object' && metadata !== null && !Array.isArray(metadata)) {
          const metadataObj = metadata as Record<string, unknown>;
          Object.keys(metadataObj).forEach((metaKey) => {
            if (metaKey && metaKey.trim() !== '' && isNaN(Number(metaKey))) {
              allKeys.add(`metadata_${metaKey}`);
            }
          });
        } else {
          console.log('Skipping invalid metadata:', metadata);
        }
      } catch (error) {
        logger.error('Error processing metadata:', error);
      }
    });
    const uniqueKeysArray = Array.from(allKeys);

    const aggregationPipeline: PipelineStage[] = [
      {
        $lookup: {
          from: project_id + Models._UNITPLANS,
          localField: 'unitplan_id',
          foreignField: '_id',
          as: 'unitplans',
        },
      },
      {
        $unwind: '$unitplans',
      },

      {
        $project: {
          _id: 1,
          unitplan_id: 1,
          name: 1,
          status: 1,
          price: 1,
          max_price: 1,
          measurement_type: 1,
          currency: 1,
          floor_id: 1,
          building_id: 1,
          tour_id: 1,
          community_id: 1,
          measurement: 1,
          balcony_measurement: 1,
          balcony_measurement_type: 1,
          suite_area: 1,
          suite_area_type: 1,
          bedroom: '$unitplans.bedrooms',
          type: '$unitplans.type',
          style: '$unitplans.style',
          project_id: 1,
          cta_link: 1,
          direction: 1,
          metadata: 1,
        },
      },
      {
        $sort: {
          _id: -1,
        },
      },
    ];

    if (searchText!==''){
      aggregationPipeline.push({
        $match: { name: { $regex: searchText, $options: 'i' } }, // Find units based on search criteria
      });

      const query:UnknownObject = {};
      query.name = { $regex: searchText, $options: 'i' };
      const matchingDocuments = await units_model.find(query);
      searchCount = matchingDocuments.length;
    }

    if (pageSize!==-1 && searchText && searchCount > limit){
      aggregationPipeline.push({
        $skip: skipCount,
      });
    } else {
      aggregationPipeline.push({
        $skip: skipCount,
      });
    }

    if (limit!==-1 && searchText && searchCount > limit){
      aggregationPipeline.push({
        $limit: limit,
      });
    } else {
      aggregationPipeline.push({
        $limit: limit,
      });
    }

    const result = await units_model.aggregate(aggregationPipeline);

    const resultUnits = {
      'totalCount': totalCount,
      'searchCount': searchCount,
      'data': result, // Return the array directly instead of using arrayToObject
      'keys': uniqueKeysArray, // Include the unique keys in the response
    };
    logger.info('getAllUnitsWithPagination Successful', {resultUnits: resultUnits});
    return resultUnits;
  }

  public async getListOfUnits (project_id:string, limit:number, pageSize:number, searchText:string,

  ): Promise<unitListResponse | null> {
    logger.info('getListOfUnits Called',
      {project_id: project_id, limit: limit, pageSize: pageSize, searchText: searchText});
    const skipCount = pageSize * limit;
    const totalCount = await this.model.countDocuments();
    let searchCount = 0;

    const units_model = mongoose.model<Project>(
      project_id+Models._UNITS,
      unitSchema,
    );

    const aggregationPipeline: PipelineStage[] = [
      {
        $lookup: {
          from: project_id + Models._UNITPLANS,
          localField: 'unitplan_id',
          foreignField: '_id',
          as: 'unitplans',
        },
      },
      {
        $unwind: '$unitplans',
      },

      {
        $project: {
          _id: 1,
          unitplan_id: 1,
          name: 1,
          status: 1,
          price: 1,
          max_price: 1,
          measurement_type: 1,
          currency: 1,
          floor_id: 1,
          building_id: 1,
          tour_id: 1,
          community_id: 1,
          measurement: 1,
          balcony_measurement: 1,
          balcony_measurement_type: 1,
          suite_area: 1,
          suite_area_type: 1,
          cta_link: 1,
          direction: 1,
          bedroom: '$unitplans.bedrooms',
          type: '$unitplans.type',
          project_id: 1,
          metadata: 1,
        },
      },
    ];

    if (searchText!==''){
      aggregationPipeline.push({
        $match: { name: { $regex: searchText, $options: 'i' } }, // Find units based on search criteria
      });

      const query:UnknownObject = {};
      query.name = { $regex: searchText, $options: 'i' };
      const matchingDocuments = await units_model.find(query);
      searchCount = matchingDocuments.length;
    }

    if (pageSize!==-1){
      aggregationPipeline.push(
        {
          $skip: skipCount,
        });
    }

    if (limit!==-1){
      aggregationPipeline.push({
        $limit: limit,
      },
      );
    }

    const result = await units_model.aggregate(aggregationPipeline);
    const convertedUnits = await arrayToObject(result);

    const resultUnits = {
      'totalCount': totalCount,
      'searchCount': searchCount,
      'data': convertedUnits,
    };
    logger.info('getListOfUnits Successful', {resultUnits: resultUnits});
    return resultUnits;
  }

  public async updateUnit (
    payload: {[key:string]:string},
    unit_id: string,
    organization_id?: string,
  ): Promise<Units | null> {
    try {
      logger.info('updateUnit Called', {payload: payload, unit_id: unit_id});

      // Build the $set object
      const updateSet: {[key:string]: any} = {
        unitplan_id: payload.unitplan_id,
        name: payload.name,
        status: payload.status,
        metadata: payload.metadata,
        floor_id: payload.floor_id,
        building_id: payload.building_id,
        currency: payload.currency,
        price: payload.price,
        max_price: payload.max_price,
        tour_id: payload.tour_id,
        community_id: payload.community_id,
        measurement: payload.measurement,
        measurement_type: payload.measurement_type,
        balcony_measurement: payload.balcony_measurement,
        balcony_measurement_type: payload.balcony_measurement_type,
        suite_area: payload.suite_area,
        suite_area_type: payload.suite_area_type,
        cta_link: payload.cta_link,
      };

      // Handle direction: if "" or null, set to null; otherwise set to the value
      if ('direction' in payload) {
        const directionValue = payload.direction;
        if (directionValue === '' || directionValue === null || directionValue === undefined || directionValue === 'null') {
          updateSet.direction = null;
        } else {
          updateSet.direction = directionValue;
        }
      }

      const updatedUnit = await this.model.findOneAndUpdate({_id: unit_id}, {
        $set: updateSet,
      }, {
        new: true,
      });

      logger.info('updateUnit Successful', {updatedUnit: updatedUnit});

      // Trigger webhooks if organization_id is provided
      if (organization_id && updatedUnit) {
        const webhook = new WebhookSubcriptionModule(organization_id);
        const webhookEvent = new WebhookEventsModule(organization_id);
        const project_id = payload.project_id;

        const webhookSubscriptions = await webhook.getWebhook('unit_updated') as webhookData[] | null;
        if (webhookSubscriptions && Array.isArray(webhookSubscriptions) && webhookSubscriptions.length > 0) {
          const queuedWebhookTasks = webhookSubscriptions
            .filter((webhookSub: webhookData) => {
              const allowedProjects = webhookSub.rules?.allowed_projects;
              const isAllowed = allowedProjects && allowedProjects.length > 0 && allowedProjects.includes(project_id);
              return isAllowed && webhookSub.targetUrl;
            })
            .map((sub: webhookData) =>
              webhookEvent.createEvents({
                webhook_id: sub._id,
                organization_id,
                eventType: 'unit_updated',
                targetUrl: sub.targetUrl,
                data: updatedUnit,
                log: [],
                _id: new mongoose.Types.ObjectId().toString(),
              }),
            );

          Promise.allSettled(queuedWebhookTasks).then((results) => {
            results.forEach((result, index) => {
              const sub = webhookSubscriptions[index];
              if (result.status === 'rejected') {
                logger.error('Failed to create event during unit updation', {
                  Error: result.reason,
                  unit_id: updatedUnit._id.toString(),
                  webhook_id: sub._id,
                });
              } else {
                if (result.status === 'fulfilled' && !(result as webhookResult).value.success) {
                  logger.error('Failed to create event during unit updation', {
                    Error: (result as webhookResult).value,
                    eventId: (result as webhookResult).value._id,
                    webhook_id: sub._id,
                  });
                }
                logger.info('Webhook event created during unit updation', {
                  eventId: (result as webhookResult).value._id,
                  webhook_id: sub._id,
                });
              }
            });
          });
        }
      }

      return updatedUnit;
    } catch (err) {
      logger.error('Error in updateUnit', {message: err});
      throw err;
    }
  }

  public async deleteUnit
  (unit_id:string,
  ): Promise<Units | null> {
    logger.info('deleteUnit Called', {unit_id: unit_id});
    return new Promise((resolve, reject) => {
      this.model.findOneAndDelete({_id: unit_id}).then((deletedUnit: unknown) => {
        logger.info('deleteUnit Successful', {deletedUnit: deletedUnit});
        resolve(deletedUnit as Units);
      }).catch((err) => {
        logger.error('Error in deleteUnit', {message: err});
        reject(err);
      });
    });
  }

  public async getFilteredUnits (query: unitFilterQuery): Promise<object | null> {
    logger.info('getFilteredUnits Called', {query: query});
    let pipeline: PipelineStage[] = [];
    const mongoQuery: unitFilterQuery = {
      ...(query.unit_id && { _id: query.unit_id }),
      ...(query.unitplan_id && { unitplan_id: query.unitplan_id }),
      ...(query.building_id && { building_id: query.building_id }),
      ...(query.floor && { floor: query.floor }),
      ...(query.is_available && { is_available: query.is_available }),
      ...(query.status && { status: query.status }),
      ...(query.min_price && query.max_price && {
        price: { $gte: parseInt(query.min_price), $lte: parseInt(query.max_price) },
      }),
    };

    if (Object.keys(mongoQuery).length > 0) {
      pipeline = [
        {
          $match: {
            $and: [
              mongoQuery,
            ],
          },
        },
        {
          $group: {
            _id: null,
            num_units: { $sum: 1 },
            units: { $push: '$$ROOT' },
          },
        },
        {
          $project: {
            _id: 0,
            num_units: '$num_units',
            units: '$units',
          },
        },
      ];
    }

    // Check if the pipeline is not empty before executing aggregate
    if (pipeline.length > 0) {
      const result = await this.model.aggregate(pipeline);
      if (result.length > 0) {
        // Check if there are actually units in the result
        if (result[0].num_units > 0) {
          logger.info('getFilteredUnits Successful', {result: result[0]});
          return result[0];
        }
        // If no units match the criteria, return an empty result
        return null;

      }
    }
    return null;
  }
  public async getUnitsCount (community_id:string):Promise <number | null>{
    logger.info('getUnitsCount Called', {community_id: community_id});
    try {
      const unitsCount = await this.model.countDocuments({ community_id: community_id });
      logger.info('getUnitsCount Successfull', {unitsCount: unitsCount});
      return unitsCount;
    } catch {
      logger.error('error in getUnitsCount');
      return null;
    }
  }

  public async updateUnitByMeta (unit_meta:{[key:string]:string}, payload:ppgUnitType):Promise<Units|null>{
    logger.info('updateUnitByMeta Called', {unit_meta: unit_meta, payload: payload});
    return new Promise((resolve, reject) => {
      Object.keys((unit_meta)).map(async (data:string) => {

        const unitData =  await this.model.find({
          ['metadata.'+data]: unit_meta[data],
        });

        // Check unit data
        if (unitData.length===0){
          logger.error('Error in updateUnitByMeta', {message: 'Document not found'});
          reject('Document not found');
        }

        // Update object which stores data in <key,value> pairs
        const update: { $set: Record<string, string> } = { $set: {} };

        // Dynamic updatation of Unit irrespective of the fields
        for (const [key, value] of Object.entries(payload)) {
          if (key!=='metadata'){
            update.$set[key] = value as string;
          }
        }

        this.model.findOneAndUpdate(
          {['metadata.'+data]: unit_meta[data]},
          update,
          {new: true},
        ).then((updatedUnit) => {
          logger.info('updateUnitByMeta Successfull', {updatedUnit: updatedUnit});
          resolve(updatedUnit);
        });
      });

    });

  }

  public async moveToTrash (
    unitIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<Units | void> {
    logger.info('moveToTrash Successfull',
      {unitIds: unitIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: unitIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('unitIds corresponding to Unit IDs provided not found');
      throw 'unitIds corresponding to Unit IDs  provided not found';
    }
    const unitdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._UNITS}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(unitdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: unitIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(async () => {
            logger.info('moveToTrash Successful');

            // Trigger webhooks for unit deletion
            const webhook = new WebhookSubcriptionModule(organization_id);
            const webhookEvent = new WebhookEventsModule(organization_id);
            const resultObj = {
              _id: Array.isArray(unitIds) ? unitIds[0] : unitIds,
              result: 'unit deleted',
            };

            const webhookSubscriptions = await webhook.getWebhook('unit_deleted') as webhookData[];
            if (webhookSubscriptions && Array.isArray(webhookSubscriptions) && webhookSubscriptions.length > 0) {
              const queuedTasks = webhookSubscriptions
                .filter((webhookSub: webhookData) => {
                  const allowedProjects = webhookSub.rules?.allowed_projects;
                  const isAllowed = allowedProjects
                    && allowedProjects.length > 0
                    && allowedProjects.includes(project_id);
                  return isAllowed && webhookSub.targetUrl;
                })
                .map((sub: webhookData) =>
                  webhookEvent.createEvents({
                    webhook_id: sub._id,
                    organization_id,
                    eventType: 'unit_deleted',
                    targetUrl: sub.targetUrl,
                    data: resultObj,
                    log: [],
                    _id: new mongoose.Types.ObjectId().toString(),
                  }),
                );

              Promise.allSettled(queuedTasks).then((results) => {
                for (let i = 0; i < results.length; i++) {
                  const result = results[i];
                  const sub = webhookSubscriptions[i];
                  if (result.status === 'rejected') {
                    logger.error('Failed to create event during unit deletion', {
                      Error: result.reason,
                      unit_id: resultObj._id,
                      webhook_id: sub._id,
                    });
                  } else {
                    if (result.status === 'fulfilled' && !(result as webhookResult).value.success) {
                      logger.error('Failed to create event during unit deletion', {
                        Error: (result as webhookResult).value,
                        eventId: (result as webhookResult).value._id,
                        webhook_id: sub._id,
                      });
                    }
                    logger.info('Webhook event created during unit deletion', {
                      eventId: (result as webhookResult).value._id,
                      webhook_id: sub._id,
                    });
                  }
                }
              });
            }

            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreUnit (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreUnit Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        await this.createUnit(item);
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreUnit is Successfull');
        return 'Unit got restored';
      });
    } else {
      logger.error('Error in restoreSVG');
      throw new Error('Failed to restore unit data from trash');
    }
  }

  public async dropUnitsCollection ():Promise<boolean> {
    logger.info('Drop Units Collection Called');
    const result = await this.model.collection.drop();
    logger.info('Drop Units Collection Successfull');
    return result;
  }
}

import {
  CreateOrganizationInput,
  migratedOrganizationRecords,
  Organization,
  organizationSettingsInput,
  Role,
  share_scenes_type,
  updateOrganizationDetails,
  User,
} from '../../types/organization';
import { generateOrganizationId, isOrganization, transformUpdatePayloadForOldSchema  } from '../../helpers/organization';
import { getUserByEmail } from '../../helpers/authUser';
import { assignRoleObj } from '../../types/user';
import { getUser } from '../../helpers/authUser';
import { organizationSchema } from '../../schema/organizationsSchema';
import { userSchema } from '../../schema/userSchema';
import mongoose from 'mongoose';
import { UserModule } from '../user';
import { sendMessageToChannel } from '../../helpers/slackMessenger';
import logger from '../../config/logger';
import { invalidateCacheByPattern } from '../cdn';
import { logoType } from '../../types/projects';

export async function invalidateOrganizationAPIs (organization_id?: string): Promise<any> {
  try {
    const apiPaths = [
      '/publicapis/getAllOrganizations',
    ];

    if (organization_id) {
      apiPaths.push(
        `/publicapis/organization/${organization_id}/getOrganization`,
      );
    }

    const results = await invalidateCacheByPattern( apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

// Import { admin } from '../../config/firebase';
const OrganizationModel = mongoose.model('Organization', organizationSchema);
const UserModel = mongoose.model('User', userSchema);
export class OrganizationModule {
  public async GenerateOrganizationId (): Promise<string> {
    logger.info('GenerateOrganizationId Called');
    const organizationId = await generateOrganizationId();
    const isExists = await this.IsOrganizationIdExists(organizationId);
    if (isExists) {
      // If the ID already exists, recursively call the function again
      return this.GenerateOrganizationId();
    }
    logger.info('GenerateOrganizationId Successfull');
    return organizationId;
  }

  public async CreateOrganization (
    new_org: CreateOrganizationInput,
  ): Promise<Organization> {
    logger.info('CreateOrganization Called');
    const {
      name,
      founding_date,
      contact_email,
      phone_number,
      address,
      website,
      max_users,
      organizationId,
      thumbnail,
      unique_org_id,
      organizationSettings,
    } = new_org;

    const organization: Organization = {
      _id: organizationId,
      name,
      founding_date,
      contact_email,
      phone_number,
      address,
      website,
      max_users,
      thumbnail,
      unique_org_id,
      roles: [],
      organizationSettings,
    };

    const orga = new OrganizationModel(organization);
    const org = await orga.save();
    return org as Organization;
  }

  public async IsOrganizationIdExists (
    organizationId: string,
  ): Promise<boolean> {
    logger.info('IsOrganizationIdExists Called');
    const organization = await OrganizationModel.findById(organizationId);
    return organization !== null && organization !== undefined;
  }

  public async GetOrganization (organizationId: string): Promise<Organization> {
    logger.info('GetOrganization Called');
    const org = await OrganizationModel.findById(organizationId).lean();
    if (org) {
      logger.info('GetOrganization Successfull');
      return org as unknown as Organization;
    }
    logger.error(`Organization with ID ${organizationId} not found`);
    throw new Error(`Organization with ID ${organizationId} not found`);
  }
  public async FindOrganizationById (
    organizationId: string,
  ): Promise<Organization | migratedOrganizationRecords | null> {
    console.info('FindOrganizationById Called');
    try {
      const organization = await OrganizationModel.findById(organizationId);
      if (!organization) {
        console.warn(`Organization with ID ${organizationId} not found`);
        return null;
      }
      console.info('FindOrganizationById Successful');
      return organization as unknown as Organization;
    } catch (error) {
      console.error(`Error in FindOrganizationById: ${error}`);
      throw new Error('An error occurred while finding the organization');
    }
  }
  public async FindOrganizationByunique_org_id (
    unique_org_id: string,
  ): Promise<Organization | migratedOrganizationRecords | null> {
    console.info('FindOrganizationByunique_org_id Called', unique_org_id);
    try {
      const organization = await OrganizationModel.aggregate([
        {
          $match: {
            unique_org_id: unique_org_id,
          },
        },
        {
          $project: {
            roles: 0,
          },
        },
      ]);

      if (!organization) {
        console.warn(
          `Organization with unique_org_id ${unique_org_id} not found`,
        );
        return null;
      }
      console.info('FindOrganizationByunique_org_id Successful');
      return organization[0] as Organization;
    } catch (error) {
      console.error(`Error in FindOrganizationByunique_org_id: ${error}`);
      throw new Error('An error occurred while finding the organization');
    }
  }

  public async UpdateOrganization (
    organizationId: string,
    updatedData: updateOrganizationDetails,
  ): Promise<Organization | migratedOrganizationRecords> {
    logger.info('UpdateOrganization Called');
    try {
      // Update object which stores data in <key,value> pairs
      const update: { $set: Record<string, string | string[]  | boolean | number | Date | share_scenes_type> }
      = { $set: {} };

      // Dynamic updatation of Tour irrespective of the fields
      for (const [key, value] of Object.entries(updatedData)) {
        update.$set[key] = value;
      }
      const updatedOrganization = await OrganizationModel.findOneAndUpdate(
        { _id: organizationId },
        update,
        { new: true },
      );
      if (updatedOrganization) {
        logger.info('UpdateOrganization Successfull');
        return updatedOrganization as Organization;
      }
      logger.error('Organization missing after update');
      throw new Error('Organization missing after update');
    } catch (error) {
      logger.error('Error updating organization', { message: error });
      throw new Error('Error updating organization');
    }
  }

  public async UpdateOrganizationSettings (organizationId:string, query:organizationSettingsInput)
  :Promise<Organization> {
    logger.info('UpdateOrganization Called', {organizationId: organizationId, query: query});
    try {
      let updateOrganizationObject  = {};

      const existingOrganization =
      await OrganizationModel.findById(organizationId).lean() as Organization | migratedOrganizationRecords;
      if (!existingOrganization) {
        logger.error('Unable to find existing Organization');
        throw new Error('Unable to find existing Organization');
      }
      console.log(isOrganization(existingOrganization), 'Organization Classification');

      // True Case
      if (isOrganization(existingOrganization)) {
        for (const key of Object.keys(query)){
          switch (key) {
            case 'salestool':
              updateOrganizationObject = {
                ...updateOrganizationObject,
                [`organizationSettings.${key}.slots`]: query[key].slots,
                [`organizationSettings.${key}.timezone`]: query[key].timezone,
              };
              break;
            case 'pixelstreaming':
              updateOrganizationObject = {
                ...updateOrganizationObject,
                [`organizationSettings.${key}.max_concurrent_sessions`]: query[key].max_concurrent_sessions,
                [`organizationSettings.${key}.duration`]: query[key].duration,
                [`organizationSettings.${key}.lark_groupid`]: query[key].lark_groupid,
              };
              break;
            case 'theme':
              updateOrganizationObject = {
                ...updateOrganizationObject,
                [`organizationSettings.${key}.theme`]: query[key].theme,
                [`organizationSettings.${key}.primary`]: query[key].primary,
                [`organizationSettings.${key}.primary_text`]: query[key].primary_text,
                [`organizationSettings.${key}.secondary`]: query[key].secondary,
                [`organizationSettings.${key}.secondary_text`]: query[key].secondary_text,
                [`organizationSettings.${key}.font_type`]: query[key].font_type,
                [`organizationSettings.${key}.font_url`]: query[key].font_url,
              };
              break;
            case 'currency':
              updateOrganizationObject = {
                ...updateOrganizationObject,
                [`organizationSettings.${key}.baseCurrency`]: query[key].baseCurrency,
                [`organizationSettings.${key}.exchangeRatio`]: query[key].exchangeRatio,
                [`organizationSettings.${key}.currency_provider`]: query[key].currency_provider,
                [`organizationSettings.${key}.currency_provider_name`]: query[key].currency_provider_name,
              };
              break;
            case 'weblite': {
              let sharescene_object;

              if (typeof existingOrganization.organizationSettings.weblite.share_masterscenes === 'boolean') {
                sharescene_object = {
                  whatsapp: existingOrganization.organizationSettings.weblite.share_masterscenes,
                  email: existingOrganization.organizationSettings.weblite.share_masterscenes,
                  twitter: existingOrganization.organizationSettings.weblite.share_masterscenes,
                };
              }

              if (query[key]?.share_masterscenes && typeof query[key]?.share_masterscenes === 'object') {
                sharescene_object = {
                  ...(sharescene_object ?? existingOrganization.organizationSettings.weblite.share_masterscenes ?? {}),
                  ...query[key]?.share_masterscenes,
                };
              }

              if (sharescene_object) {
                updateOrganizationObject = {
                  ...updateOrganizationObject,
                  [`organizationSettings.${key}.share_masterscenes`]: sharescene_object,
                };
              }
              updateOrganizationObject = {
                ...updateOrganizationObject,
                [`organizationSettings.${key}.mastersvg_visibility`]: query[key].mastersvg_visibility,
                [`organizationSettings.${key}.is_broadcast_enabled`]: query[key].is_broadcast_enabled,
                [`organizationSettings.${key}.org_logo_click_type`]: query[key].org_logo_click_type,
                [`organizationSettings.${key}.org_logo_click_link`]:
                query[key].org_logo_click_type === logoType.CUSTOM ? query[key].org_logo_click_link : '',
              };
              break;
            }
            default:
              logger.error('Invalid Org settings type');
              throw new Error('Invalid Org settings type');
          }
        }
        const updatedOrganizationDetails = await OrganizationModel.findOneAndUpdate(
          { _id: organizationId },
          {
            $set: updateOrganizationObject,
          },
          { new: true },
        );
        logger.info('updateProjectSettings Successfull', {updatedOrg: updatedOrganizationDetails});
        return updatedOrganizationDetails as unknown as Organization;
      }
      // False Case
      updateOrganizationObject = transformUpdatePayloadForOldSchema(query);
      // Apply flattened update directly
      const updatedOrganization = await OrganizationModel.findOneAndUpdate(
        { _id: organizationId },
        {
          $set: updateOrganizationObject,
        },
        { new: true, strict: false }, // Restricted only for the Old Org Documents
      );
      logger.info('updateProjectSettings Successfull', {updatedOrg: updatedOrganization});
      return updatedOrganization as Organization;

    } catch (error) {
      logger.error('Error in updating Organization Settings', {message: error});
      throw new Error(`Error in updating Organization Settings: ${error}`);
    }
  }

  public async Isunique_org_idExists (unique_org_id: string): Promise<boolean> {
    try {
      logger.info('Isunique_org_idExists Called for:', unique_org_id);

      const user = await OrganizationModel.findOne({
        unique_org_id: {
          $exists: true,
          $regex: `^${unique_org_id}$`,
          $options: 'i',
        },
      });

      const exists = !!user;
      logger.info(
        exists
          ? `unique_org_id "${unique_org_id}" exists`
          : `unique_org_id "${unique_org_id}" does not exist`,
      );
      console.log('Query Result:', user, exists);
      return exists;
    } catch (error) {
      logger.error('Error checking unique_org_id', { message: error });
      throw new Error('Database error while checking unique_org_id');
    }
  }

  public async AddUserToOrganization (
    organizationId: string,
    user: User,
  ): Promise<object | null> {
    logger.info('AddUserToOrganization Called');
    try {
      const IsInOrganization = await this.checkAndCreateUser(user.user_id);
      console.log(user.user_id, IsInOrganization);
      if (
        IsInOrganization === null ||
        IsInOrganization.organization_id === undefined ||
        !IsInOrganization.organization_id.includes(organizationId)
      ) {
        await OrganizationModel.updateOne(
          { _id: organizationId },
          { $push: { roles: user } },
        );
        const updatedUser = await UserModel.findOneAndUpdate(
          { _id: user.user_id },
          {
            $push: { organization_id: organizationId },
          },
          {
            new: true,
          },
        );
        logger.info('AddUserToOrganization Successfull');
        return updatedUser;
      }
      if (
        IsInOrganization &&
        IsInOrganization !== null &&
        IsInOrganization.organization_id !== undefined &&
        IsInOrganization.organization_id.includes(organizationId)
      ) {
        logger.error('User already in this organisation');
        throw new Error('User already in this organisation');
      }
      return null;
    } catch (error) {
      logger.error('Error adding user to organization', { message: error });
      throw new Error('Error adding user to organization' + error);
    }
  }

  public async RemoveUserFromOrganization (
    organizationId: string,
    email: string,
  ): Promise<boolean> {
    logger.info('RemoveUserFromOrganization Called');
    try {
      const userRecords = await getUserByEmail(email);
      if (!userRecords) {
        logger.error('User Record not found');
        throw new Error('User Record not found');
      }

      const IsInOrganization = await this.checkAndCreateUser(userRecords.uid);
      const webhookUrl = process.env.SLACK_WEBHOOKURL as string;
      sendMessageToChannel(
        webhookUrl,
        'removed user',
        JSON.stringify(IsInOrganization),
      );
      if (
        IsInOrganization !== null &&
        IsInOrganization.organization_id !== undefined &&
        IsInOrganization.organization_id.includes(organizationId)
      ) {
        await UserModel.updateOne(
          { _id: userRecords.uid },
          { $pull: { organization_id: organizationId } },
        );
        await OrganizationModel.updateOne(
          { _id: organizationId },
          { $pull: { roles: { user_id: userRecords.uid } } },
        );

        // Console.log('User removed from organization successfully');
        logger.info('RemoveUserFromOrganization Successfull');
        return true;
      }
      logger.error('User not in this organization');
      throw new Error('User not in this organization');
    } catch (error) {
      logger.error('Error removing user from organization:', {
        message: error,
      });
      throw new Error('Error removing user from organization:' + error);
    }
  }
  public async CountUsersWithRole (organizationId: string): Promise<number> {
    logger.info('CountUsersWithRole Called');
    try {
      const count = await OrganizationModel.aggregate([
        {
          $match: {
            _id: organizationId,
          },
        },
        {
          $project: {
            adminCount: {
              $size: {
                $filter: {
                  input: '$roles',
                  as: 'role',
                  cond: { $eq: ['$$role.role', 'admin'] },
                },
              },
            },
          },
        },
      ]);

      const adminCount = count.length > 0 ? count[0].adminCount : 0;
      logger.info('CountUsersWithRole Successfull', { adminCount: adminCount });
      return adminCount;
    } catch (error) {
      logger.error('Error counting users with role:', { message: error });
      throw new Error('Error counting users with role: ' + error);
    }
  }

  public async AssignRoleToUser (userRoleObj: assignRoleObj): Promise<boolean> {
    logger.info('AssignRoleToUser Called');
    try {
      const previousRole = await this.GetUserRole(
        userRoleObj.user_id,
        userRoleObj.organizationId,
      );
      const adminUsersCount = await this.CountUsersWithRole(
        userRoleObj.organizationId,
      );

      if (previousRole && previousRole.role === userRoleObj.roleId) {
        logger.error('User already has the requested access');
        throw new Error('User already has the requested access');
      }

      if (previousRole?.role === 'admin' && adminUsersCount === 1) {
        logger.error(
          'At least one admin should be present in the organization',
        );
        throw new Error(
          'At least one admin should be present in the organization',
        );
      }
      this.checkAndCreateUser(userRoleObj.user_id).then((IsInOrganization) => {
        if (
          !IsInOrganization ||
          IsInOrganization.organization_id === undefined
        ) {
          logger.error('User is not associated with this organization');
          throw new Error('User is not associated with this organization');
          return false;
        }
        if (
          !IsInOrganization.organization_id.includes(userRoleObj.organizationId)
        ) {
          logger.error('User is not associated with this organization');
          throw new Error('User is not associated with this organization');
          return false;
        }

        const time = new Date().toISOString();
        const role: Role = {
          role: userRoleObj.roleId,
          user_id: userRoleObj.user_id,
          created_time: time,
          email: userRoleObj.email,
        };
        OrganizationModel.updateOne(
          {
            _id: userRoleObj.organizationId,
            'roles.user_id': userRoleObj.user_id,
          },
          // Match organization and user_id in roles array
          { $set: { 'roles.$': role } }, // Set the entire matched object in the roles array
        ).then(() => {
          logger.info('AssignRoleToUser Successfull');
          return true;
        });
        return false;
      });
      return false;
    } catch (error) {
      logger.error('Error Assigning Role to User', { message: error });
      throw new Error('Error Assigning Role to User' + error);
    }
  }

  public async GetUserRole (
    user_id: string,
    organizationId: string,
  ): Promise<Role | null> {
    logger.info('GetUserRole Called');
    try {
      const organization = await OrganizationModel.find(
        { _id: organizationId },
        { roles: { $elemMatch: { user_id: user_id } } },
      ).lean();
      console.log(user_id);
      if (organization) {
        const rolesArray = organization[0].roles;

        if (rolesArray && rolesArray.length > 0) {
          const userRole = rolesArray[0];
          if (userRole) {
            logger.info('GetUserRole Successfull', { userRole: userRole });
            return userRole as Role;
          }
        } else {
          return null;
          logger.error('User not in this organization');
          throw new Error('User not in this organization');
        }
      }
      return null;
    } catch (error) {
      return null;
      logger.error('User does not exist in organization', { message: error });
      throw new Error('User does not exist in organization: ' + error);
    }
  }

  public async checkAndCreateUser (userId: string): Promise<User | null> {
    logger.info('checkAndCreateUser Called');
    const userData: User | null = await UserModel.findOne({
      _id: userId,
    }).lean();
    if (userData && userData.organization_id) {
      logger.info('checkAndCreateUser Successfull');
      return userData;
    }
    const authUser = await getUser(userId);
    if (authUser) {
      const userModule = new UserModule();
      userModule.migrateFromFirebase(authUser);
      const migratedUserData: User | null = await UserModel.findOne({
        _id: userId,
      }).lean();
      return migratedUserData || null;
    }
    return null; // Return null if no organization found
  }

  public async uploadThumbnail (
    organizationId: string,
    thumbnail: string,
  ): Promise<void> {
    logger.info('uploadThumbnail Called');
    if (!thumbnail) {
      logger.error('Thumbnail image is missing', { thumbnail: thumbnail });
      throw new Error('Thumbnail image is missing');
    }

    try {
      const organization = await OrganizationModel.findOneAndUpdate(
        { _id: organizationId },
        { $set: { thumbnail: thumbnail } },
        { new: true },
      );
      logger.info('uploadThumbnail Successfull');

      if (!organization) {
        logger.error('Organization not found');
        throw new Error('Organization not found');
      }
    } catch (error) {
      logger.error('Organization not found', { message: error });
      throw new Error('Error uploading thumbnail');
    }
  }
}

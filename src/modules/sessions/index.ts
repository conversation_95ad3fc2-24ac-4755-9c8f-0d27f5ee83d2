import { sessionSchema } from '../../schema/sessionSchema';
import {
  Anonymous_Session, ExportSessionInput, Session, SessionStatus,
  SessionType, UpdateSessionInput, bookSessionInput, createSessionInput,
  getMonthlySlotsInput,
  getSlotsInput, sessionAnalyticsQuery,
} from '../../types/session';
import mongoose, { PipelineStage } from 'mongoose';
import { generateCode } from '../../helpers/session';
import { admin } from '../../config/firebase';
import { leadsSchema } from '../../schema/leadsSchema';
import { projectSchema } from '../../schema/projectSchema';
import { ProjectModule } from '../../modules/projects';
import { OrganizationModule } from '../../modules/organization';
import moment from 'moment-timezone';
import * as Moment from 'moment';
import { Models } from '../../types/extras';
import { ObjectId } from 'mongodb';
import logger from '../../config/logger';
import axios from 'axios';
import https from 'https';
import { invalidateCacheByPattern } from '../cdn';
import { WebhookSubcriptionModule } from '../webhookSubcription';
import { WebhookEventsModule } from '../webhooksEvents';
import { webhookData, webhookResult } from '../../types/webhooks';
import { isOrganization, migrateOrgSchema } from '../../helpers/organization';
import { migratedOrganizationRecords } from '../../types/organization';

export async function invalidateSessionAPIs (): Promise<any> {
  try {
    const apiPaths = [
      '/publicapis/getSession',
    ];

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

type AvalilabilityConfig = {
  organization_id: string,
  project_id?: string,
  start_time: Date | Moment.Moment,
  end_time: Date | Moment.Moment,
  maxConcurrentSessions: number
}
type Avalilability = {
  slotStart: string,
  slotEnd?:string,
  activeSessions: number,
  availableSessions: number,
  available: boolean;
  conflictingSessionIds: string[];  // ← Add this
}

interface AvalilabilityInstanceConfig {
  organization_id: string;
  start_time: Date;
  end_time?: Date;
  maxConcurrentSessions?: number;
  exclude_session_id?: string; // Add this optional parameter
}

interface SessionSlotResult {
  user_ids: string[];
  schedule_time: string;
  end_time: string;
  availableSession: number;
  activeSessions: number;
}

// Interface InstanceAvailabilityQuery {
//   Organization_id: string;
//   Is_reserved: boolean;
//   Status: {
//     $in: SessionStatus[];
//     $nin: SessionStatus[];
//   };
//   $or: Array<{
//     Is_scheduled?: boolean;
//     Instance_start_time?: { $lt: string };
//     Instance_end_time?: { $gt: string };
//     Status?: { $in: SessionStatus[] };
//   }>;
//   _id?: { $ne: string };
// }

export class SessionModule {
  private model: mongoose.Model<Session>;

  constructor () {
    this.model = mongoose.model<Session>(
      `${Models.SESSION}`,
      sessionSchema,
    );
  }
  public async CreateSession (createSessionData: createSessionInput,
  ): Promise<Session | void> {
    logger.info('CreateSession Called', { createSessionData: createSessionData });
    let duration_minutes = 30;
    const code = await generateCode();
    const session_id = new mongoose.Types.ObjectId();
    const invitelink = (createSessionData.referrer || process.env.BASE_URL) + 'salestool/joinroom/' + session_id;
    let starttime = new Date().toISOString();
    let endtime = new Date();
    let migratedOrg;

    // Get organization-level session settings
    const organizationModule = new OrganizationModule();
    const organization = await organizationModule.GetOrganization(createSessionData.organization_id);
    if (!organization) {
      throw new Error('Organization not found');
    }
    if (!isOrganization(organization)) {
      // Old schema - migrate it first, then send
      migratedOrg = migrateOrgSchema(organization as migratedOrganizationRecords);
    } else {
      migratedOrg = organization;
    }

    // Use organization-level duration if available, otherwise fallback to project or default
    if (migratedOrg.organizationSettings.pixelstreaming.duration) {
      duration_minutes = migratedOrg.organizationSettings.pixelstreaming.duration;
    } else if (createSessionData.project_id) {
      const ProjectModel = mongoose.model(createSessionData.organization_id + '_projects', projectSchema);
      const project = await ProjectModel.findById(createSessionData.project_id);
      if (!project) {
        throw new Error('Project not found in organization');
      }
      const session_duration = project?.projectSettings?.pixelstreaming?.session_duration;
      duration_minutes = typeof session_duration === 'number' ? session_duration : 30;
    }

    // Default session_duration is 30 minutes
    if (createSessionData.is_scheduled) {
      // Scheduled session - use the provided schedule_time
      if (!createSessionData.schedule_time) {
        logger.error('Scheduled time not available for scheduled meeting');
        throw new Error('Scheduled time not available for scheduled meeting');
      }
      starttime = createSessionData.schedule_time;
      endtime = new Date(starttime);
      endtime.setSeconds(endtime.getSeconds() + (createSessionData.duration * 60));
    } else {
      // Instant session - always start immediately, ignore schedule_time if provided
      starttime = new Date().toISOString();
      endtime = new Date();
      endtime.setSeconds(endtime.getSeconds() + (duration_minutes * 60));
    }

    const newSession: Session = {
      _id: session_id,
      status: createSessionData.is_scheduled ? SessionStatus.SCHEDULED : SessionStatus.ACTIVE,
      code: code,
      duration_minutes: createSessionData.project_id ? duration_minutes : createSessionData.duration,
      pixel_duration_minutes: 0,
      organization_id: createSessionData.organization_id,
      user_id: createSessionData.user_id,
      project_id: createSessionData.project_id,
      source: createSessionData.source,
      is_scheduled: createSessionData.is_scheduled,
      schedule_time: createSessionData.is_scheduled ? createSessionData.schedule_time : null,
      tag: createSessionData.tag,
      description: createSessionData.description,
      start: starttime,
      last_interaction_time: new Date().toISOString(),
      end_time: endtime.toISOString(),
      scheduled_end_time: endtime.toISOString(),
      instance_start_time: starttime,
      instance_end_time: endtime.toISOString(),
      invite_link: invitelink,
      participants: [],
      is_pixelstreaming_active: false,
      is_reserved: createSessionData.is_reserved || false,
      type: createSessionData.type || SessionType.DEFAULT,
    };
    const sessions = new this.model(newSession);
    const session = await sessions.save();
    await this.updateFirestore(session._id);
    logger.info('CreateSession Successfull', { session: session });

    // Trigger webhooks
    const organization_id = session.organization_id;
    const webhook = new WebhookSubcriptionModule(organization_id);
    const webhookEvent = new WebhookEventsModule(organization_id);

    const webhookSubscriptions = await webhook.getWebhook('session_created') as webhookData[];
    if (webhookSubscriptions && webhookSubscriptions?.length) {
      const queuedEvents = webhookSubscriptions
        .filter((sub: webhookData) => sub.targetUrl)
        .map((sub: webhookData) =>
          webhookEvent.createEvents({
            webhook_id: sub._id,
            organization_id,
            eventType: 'session_created',
            targetUrl: sub.targetUrl,
            data: session,
            log: [],
            _id: new mongoose.Types.ObjectId().toString(),
          }),
        );

      Promise.allSettled(queuedEvents).then((results) => {
        results.forEach((result, index) => {
          const sub = webhookSubscriptions[index];
          if (result.status === 'rejected') {
            logger.error('Failed to create event during session creation', {
              Error: result.reason,
              _id: session._id.toString(),
              webhook_id: sub._id,
            });
          } else {
            if (result.status === 'fulfilled' && !(result as webhookResult).value.success) {
              logger.error('Failed to create event during session creation', {
                Error: (result as webhookResult).value,
                eventId: (result as webhookResult).value._id,
                webhook_id: sub._id,
              });
            }
            logger.info('Webhook event created during session creation', {
              eventId: (result as webhookResult).value._id,
              webhook_id: sub._id,
            });
          }
        });
      });
    }

    return session;
  }

  public async getSessionById (sessionId: string): Promise<Session> {
    logger.info('getSessionById Called', { sessionId: sessionId });
    const session = await this.model.findById(sessionId);
    if (session) {
      logger.info('getSessionById Successfull', { session: session });
      return session as Session;
    }
    logger.error(`Session with ID ${sessionId} not found`);
    throw new Error(`Session with ID ${sessionId} not found`);

  }
  public async getSessions (organizationId: string, query: sessionAnalyticsQuery): Promise<object | void> {
    logger.info('getSessions Successfull', { organizationId: organizationId, query: query });
    try {
      console.log(query.user_id);
      let pipeline: PipelineStage[] = [];
      const mongoQuery: sessionAnalyticsQuery = {
        organization_id: organizationId,
        ...(query.project_id && { project_id: query.project_id }),
        ...(query.user_id && { user_id: query.user_id }),
        ...(query.type === 'scheduled' && { is_scheduled: true }),
        ...(query.type === 'previous' && {
          scheduled_end_time: { $lt: new Date().toISOString() }, // Sessions scheduled in the past
          // { status: SessionStatus.ENDED }, // OR sessions that are ended
        }),
        ...(query.type === 'upcoming' && {
          scheduled_end_time: { $gt: new Date().toISOString() }, // Sessions scheduled in the past
          // { status: { $ne: SessionStatus.ENDED } }, // OR sessions that are ended
        }),
        ...(query.tag && { tag: query.tag }),
      };
      if (Object.keys(query).length === 0) {
        // Execute MongoDB query for all session (without filters)
        pipeline = [
          {
            $match: {
              organization_id: organizationId,
            },
          },
          {
            $sort: { schedule_time: 1 },
          },
          {
            $lookup: {
              from: organizationId.toLowerCase() + Models._LEADS,
              localField: '_id',
              foreignField: 'session_id',
              as: 'leads',
            },
          },
          {
            $addFields: {
              leads: {
                $map: {
                  input: '$leads',
                  as: 'lead',
                  in: {
                    _id: '$$lead._id',
                    name: '$$lead.name',
                    email: '$$lead.email',
                    phone_number: '$$lead.phone_number',
                  },
                },
              },
            },
          },
          {
            $group: {
              _id: null,
              sessions: { $push: { k: '$_id', v: '$$ROOT' } },
            },
          },
          {
            $replaceRoot: { newRoot: { $arrayToObject: '$sessions' } },
          },
        ];
      } else {
        pipeline = [
          {
            $match: {
              $and: [
                mongoQuery,
              ].filter(Boolean),
            },
          },
          {
            $sort: { schedule_time: 1 }, // Sort the sessions
          },
          {
            $lookup: {
              from: organizationId.toLowerCase() + Models._LEADS,
              localField: '_id',
              foreignField: 'session_id',
              as: 'leads',
            },
          },
          {
            $group: {
              _id: null,
              sessions: { $push: { k: '$_id', v: '$$ROOT' } },
            },
          },
          {
            $replaceRoot: { newRoot: { $arrayToObject: '$sessions' } },
          },
        ];
      }

      const result = await this.model.aggregate(pipeline);

      if (result.length > 0) {
        logger.info('getSessions Successfull', { result: result });
        return result[0];
      }
      return {};
    } catch (error) {
      logger.error(`Unable to fetch sesisons for this organization - ${organizationId}`);
      throw new Error(`Unable to fetch sesisons for this organization - ${error}`);
      return { error: error };
    }
  }
  public async ExtendSession (updateSession: ExportSessionInput): Promise<Session | void> {
    logger.info('UpdateSessionDuration Called', { updateSession: updateSession });
    const existingSession = await this.model.findOne({ _id: updateSession.session_id }); // Fetch existing session doc
    if (existingSession) {
      // Calculate the new last_interaction_time
      const newLastInteractionTime = new Date(new Date().getTime()
        + (updateSession.duration * 60000));
      const session = await this.model.findOneAndUpdate(
        { _id: updateSession.session_id },
        {
          $set: {
            status: 'on-going',
            end_time: newLastInteractionTime.toISOString(),
            ispixel_using: true,
          },
        },
        { new: true }, // Return the updated document
      );
      const firestoreUpdated = await this.updateFirestore(updateSession.session_id);
      if (session && firestoreUpdated) {
        logger.info('UpdateSessionDuration Successfull', { session: session });
        return session;
      }
      logger.error('Error in updating session on Firestore');
      throw new Error('Error in updating session on Firestore');

    } else {
      logger.error('Error in UpdateSessionDuration');
      throw new Error('Unable to find existing session');
    }
  }
  public async UpdateSessionDuration (updateSession: UpdateSessionInput): Promise<Session | void> {
    // Logger.info('UpdateSessionDuration Called', { updateSession });

    const existingSession = await this.model.findOne({ _id: updateSession.session_id });

    if (!existingSession) {
      logger.error('Error in UpdateSessionDuration');
      throw new Error('Unable to find existing session');
    }

    const newLastInteractionTime = new Date().toISOString();
    const currentDuration = existingSession.duration_minutes || 0;
    const updateDuration = updateSession.duration_minutes || 0;
    const newDuration = Number(currentDuration) + Number(updateDuration);

    // Validate that newDuration is a valid number
    if (isNaN(newDuration)) {
      logger.error('Invalid duration calculation', {
        currentDuration,
        updateDuration,
        newDuration,
      });
      throw new Error('Invalid duration value');
    }

    let newPixelDuration = existingSession.pixel_duration_minutes || 0;
    if (existingSession.is_pixelstreaming_active && updateSession.duration_minutes) {
      newPixelDuration = Number(newPixelDuration) + Number(updateSession.duration_minutes);
    }

    const updateObj: Partial<Pick<
      Session,
      'last_interaction_time' | 'duration_minutes' | 'pixel_duration_minutes' | 'is_pixelstreaming_active' | 'project_id' | 'type' | 'is_reserved' | 'instance_start_time' | 'instance_end_time'
    >> = {
      last_interaction_time: newLastInteractionTime,
      duration_minutes: newDuration,
      pixel_duration_minutes: newPixelDuration,
      is_pixelstreaming_active: updateSession.is_pixelstreaming_active,
    };

    if (updateSession.project_id) {
      updateObj.project_id = updateSession.project_id;
    }

    if (updateSession.type) {
      updateObj.type = updateSession.type;
    }

    if (typeof updateSession.is_reserved === 'boolean') {
      updateObj.is_reserved = updateSession.is_reserved;
    }

    console.log('@@@', updateSession),
    console.log('______', updateSession.instance_start_time);

    // Replace the instance_start_time condition with:
    if (updateSession.instance_start_time && typeof updateSession.instance_start_time === 'string' && updateSession.instance_start_time.length > 0) {
      updateObj.instance_start_time = updateSession.instance_start_time;
      logger.info('Updating instance_start_time', {
        old: existingSession.instance_start_time,
        new: updateSession.instance_start_time,
      });
    }

    if (updateSession.instance_end_time) {
      updateObj.instance_end_time = updateSession.instance_end_time;
    }

    const session = await this.model.findOneAndUpdate(
      { _id: updateSession.session_id },
      { $set: updateObj },
      { new: true },
    );

    const firestoreUpdated = await this.updateFirestore(updateSession.session_id);

    if (session && firestoreUpdated) {
      // Trigger webhooks
      const organization_id = session.organization_id;
      const webhook = new WebhookSubcriptionModule(organization_id);
      const webhookEvent = new WebhookEventsModule(organization_id);

      const webhookSubscriptions = await webhook.getWebhook('session_updated') as webhookData[];
      if (webhookSubscriptions && webhookSubscriptions?.length) {
        const queuedEvents = webhookSubscriptions
          .filter((sub: webhookData) => sub.targetUrl)
          .map((sub: webhookData) =>
            webhookEvent.createEvents({
              webhook_id: sub._id,
              organization_id,
              eventType: 'session_updated',
              targetUrl: sub.targetUrl,
              data: session,
              log: [],
              _id: new mongoose.Types.ObjectId().toString(),
            }),
          );

        Promise.allSettled(queuedEvents).then((results) => {
          results.forEach((result, index) => {
            const sub = webhookSubscriptions[index];
            if (result.status === 'rejected') {
              logger.error('Failed to create event during session updation', {
                Error: result.reason,
                _id: session._id.toString(),
                webhook_id: sub._id,
              });
            } else {
              if (result.status === 'fulfilled' && !(result as webhookResult).value.success) {
                logger.error('Failed to create event during session updation', {
                  Error: (result as webhookResult).value,
                  eventId: (result as webhookResult).value._id,
                  webhook_id: sub._id,
                });
              }
              logger.info('Webhook event created during session updation', {
                eventId: (result as webhookResult).value._id,
                webhook_id: sub._id,
              });
            }
          });
        });
      }

      return session;
    }

    logger.error('Error in updating session on Firestore');
    throw new Error('Error in updating session on Firestore');
  }

  // Start Session
  public async StartSession (session_id: string): Promise<Session | void> {
    logger.info('StartSession Called', { session_id: session_id });
    const sessionData = await this.model.findById(session_id);

    if (!sessionData) {
      logger.error('Session not found', { session_id: session_id });
      throw new Error('Session not found');
    }

    const session = await this.model.findOneAndUpdate(
      { _id: session_id },
      {
        $set: {
          status: 'on-going',
          start: new Date().toISOString(),
          is_pixelstreaming_active: sessionData.type === SessionType.PIXELSTREAMING ? true : false,
        },
      },
      { new: true },
    );
    try {
      const firestoreUpdate = await this.updateFirestore(session_id);
      if (session && firestoreUpdate) {
        logger.info('StartSession Successfull', { session: session });
        return session as Session;
      }
      logger.error('Error in updating session on Firestore');
      throw new Error('Error in updating session on Firestore');

    } catch (error) {
      logger.error('Error in StartSession', { message: error });
      throw new Error('An unexpected error occurred during Firestore update');
    }
  }
  // End Session API for Host
  public async EndSession (session_id: string): Promise<Session | void> {
    logger.info('EndSession Called', { session_id: session_id });
    const orgId = await this.getOrganizationId(session_id);
    const LeadsModel = mongoose.model(orgId + '_leads', leadsSchema);
    const sessionLeads = await LeadsModel.find({ session_id: session_id });
    const session = await this.model.findOneAndUpdate(
      { _id: session_id },
      {
        $set: {
          status: SessionStatus.ENDED,
          end_time: new Date().toISOString(),
          participants: sessionLeads,
        },
      },
      { new: true },
    );
    try {
      const firestoreUpdate = await this.updateFirestore(session_id);
      if (session && firestoreUpdate) {
        logger.info('EndSession Successfull', { session: session });
        return session as Session;
      }
      logger.error('Error in updating session on Firestore');
      throw new Error('Error in updating session on Firestore');

    } catch (error) {
      logger.error('Error in EndSession', { message: error });
      throw new Error('An unexpected error occurred during Firestore update');
    }
  }
  public async getOrganizationId (sessionId: string): Promise<string> {
    logger.info('getOrganizationId Called', { sessionId: sessionId });
    const session = await this.model.findById(sessionId);
    if (session) {
      logger.info('getOrganizationId Successfull', { session: session });
      return session.organization_id;
    }
    logger.error(`Session with ID ${sessionId} not found`);
    throw new Error(`Cannot fetch Organization ID: Session with ID ${sessionId} not found`);

  }
  public async updateFirestore (sessionId: string | ObjectId): Promise<boolean> {
    logger.info('updateFirestore Called', { sessionId: sessionId });
    try {
      const session = await this.model.findById(sessionId);

      if (session) {

        const sessionRef = admin.firestore().collection('sessions').doc(sessionId as string);
        console.log(session);
        // Check if the document exists
        const sessionSnapshot = await sessionRef.get();
        const data = JSON.parse(JSON.stringify(session));
        if (sessionSnapshot.exists) {
          await sessionRef.update(data);
          return true;
        }
        await sessionRef.set(data);
        await admin.firestore().collection('sessions').doc(sessionId as string)
          .collection('config').doc('data').set({ roomId: sessionId as string });
        logger.info('updateFirestore Successfull');
        return true;

      }
      logger.error('Error updating firestore - session not found');
      throw new Error('Error updating firestore - session not found');

    } catch (error) {
      logger.error('Error in updateFirestore', { message: error });
      return false;
    }
  }

  public async checkAvailability (config: AvalilabilityConfig): Promise<Avalilability> {
    const { organization_id, project_id, start_time, end_time, maxConcurrentSessions } = config;
    const scheduledSessions = await this.model.find({
      is_scheduled: true,
      organization_id: organization_id,
      project_id: project_id || undefined,
      status: { $ne: SessionStatus.CANCELLED },
      $and: [
        {
          // Is_pixelstreaming_active: true,
          type: SessionType.PIXELSTREAMING,
        },
      ],
      $or: [
        { schedule_time: { $gte: start_time.toISOString(), $lt: end_time.toISOString() } },
        { end_time: { $gte: start_time.toISOString(), $lt: end_time.toISOString() } },
        {
          $and: [{ schedule_time: { $lt: start_time.toISOString() } },
            { end_time: { $gt: end_time.toISOString() } }],
        },
      ],
    });
    const availableSessions = maxConcurrentSessions - scheduledSessions.length;
    const conflictingSessionIds = scheduledSessions.map((session) => session._id.toString());
    return {
      slotStart: start_time.toISOString(),
      slotEnd: end_time.toISOString(),
      activeSessions: scheduledSessions.length,
      availableSessions,
      available: availableSessions > 0,
      conflictingSessionIds: conflictingSessionIds,
    };
  }
  // Available slots
  public async getAvailableSlots (getSlotsObj: getSlotsInput): Promise<object[] | null> {
    logger.info('getAvailableSlots Called', { getSlotsObj: getSlotsObj });
    const organizationModule = new OrganizationModule();
    const organization = await organizationModule.GetOrganization(getSlotsObj.organization_id);
    const slotAvailability = [];
    let migratedOrg;
    if (isOrganization(organization)) {
      // Old schema - migrate it first, then send
      migratedOrg = organization;

    } else {
      migratedOrg = migrateOrgSchema(organization as migratedOrganizationRecords);
    }
    if (migratedOrg && migratedOrg.organizationSettings.salestool.slots &&
      migratedOrg.organizationSettings.pixelstreaming.max_concurrent_sessions !== undefined) {
      const maxConcurrentSessions = migratedOrg.organizationSettings.pixelstreaming.max_concurrent_sessions;

      if (maxConcurrentSessions <= 0) {
        // Handle case where max_concurrent_slots is 0
        logger.error('Invalid max_concurrent_slots value. It must be greater than 0.');
        throw new Error('Invalid max_concurrent_slots value. It must be greater than 0.');
      }
      for (const slot of migratedOrg.organizationSettings.salestool.slots) {
        // Const slotParts = slot.split(':');
        // Const slotHour = parseInt(slotParts[0], 10);
        const userSelectedDate = getSlotsObj.date.slice(0, 10); // For example
        const userTimeZone = migratedOrg.organizationSettings.salestool.timezone || getSlotsObj.zone;
        const slotUTC = slot; // Example: 6:30 PM UTC, date part is ignored
        const slotTime = moment.tz(slotUTC, userTimeZone).format('HH:mm');
        const startThreshold = moment.tz(`${userSelectedDate} ${slotTime}`, 'YYYY-MM-DD HH:mm', userTimeZone);
        // Const endThreshold = startThreshold.clone()
        //   .add(((organization.duration || 30)), 'minutes');
        // StartThreshold.add(1, 'minutes');
        // Console.log(startThreshold.toISOString(), endThreshold.toISOString());
        try {
          const scheduledSessions = await this.CheckInstanceAvailability({
            organization_id: getSlotsObj.organization_id,
            start_time: new Date(startThreshold.utc().toISOString()),
            // End_time: new Date(endThreshold.utc().toISOString()),
            maxConcurrentSessions,
          }); // Combine the requested date with the slot timing
          // Sessions starting/ending exactly at the start time / end time - active
          // Sessions completely encompassing the time range - active
          // Adjacent sessions - active
          // Sessions that end before the requested start time - not active
          // Sessions that start after the requested start time + session duration ends - not active
          slotAvailability.push(scheduledSessions);
        } catch (error) {
          logger.error(`Error processing slot ${slot}: ${error}`);
          console.error(`Error processing slot ${slot}: ${error}`);
          // Handle the error appropriately, e.g., log it or return a meaningful error response
        }

      }
      logger.info('getAvailableSlots Successfull', { slotAvailability: slotAvailability });
      return slotAvailability;
    }
    logger.error('Error in getAvailableSlots');
    throw new Error('Unable to fetch sessions. Project or slots or max concurrent sessions not found.');
    return null;
  }

  public async getAnalytics (
    query: sessionAnalyticsQuery,
    organizationId: string,
    user_id: string,
    page: number,
    limit: number,
  ): Promise<object | void> {
    logger.info('getAnalytics Called', { query, organizationId, user_id });

    const mongoQuery = this.buildMongoQuery(query, organizationId, user_id);
    const skip = (page - 1) * limit;

    if (Object.keys(mongoQuery).length === 0) {
      return {
        analytics: {
          total_duration: 0,
          num_sessions: 0,
          unique_participants: 0,
        },
        sessions: [],
        pagination: {
          page,
          limit,
          hasMore: false,
        },
      };
    }

    // 1. Analytics pipeline
    const analyticsPipeline: PipelineStage[] = [
      { $match: mongoQuery },
      {
        $group: {
          _id: null,
          total_duration: { $sum: '$duration_minutes' },
          num_sessions: { $sum: 1 },
          unique_participants: { $addToSet: '$user_id' },
        },
      },
      {
        $project: {
          _id: 0,
          total_duration: 1,
          num_sessions: 1,
          unique_participants: { $size: '$unique_participants' },
        },
      },
    ];

    // 2. Paginated sessions pipeline
    const sessionsPipeline: PipelineStage[] = [
      { $match: mongoQuery },
      { $sort: { createdAt: -1 } }, // Adjust sorting if needed
      { $skip: skip },
      { $limit: limit },
    ];

    const [analyticsResult, paginatedSessions] = await Promise.all([
      this.model.aggregate(analyticsPipeline),
      this.model.aggregate(sessionsPipeline),
    ]);

    const analytics = analyticsResult[0] || {
      total_duration: 0,
      num_sessions: 0,
      unique_participants: 0,
    };

    const result = {
      analytics,
      sessions: paginatedSessions,
      pagination: {
        page,
        limit,
        hasMore: paginatedSessions.length === limit,
      },
    };

    logger.info('getAnalytics Successful', { result });
    return result;
  }

  private buildMongoQuery (query: sessionAnalyticsQuery, organizationId: string,
    user_id: string) {
    logger.info('buildMongoQuery Called', { query: query, organizationId: organizationId, user_id: user_id });
    return {
      ...(query.project_id && { project_id: query.project_id }),
      ...(organizationId && { organization_id: organizationId }),
      ...(user_id && { user_id: user_id }),
      ...(query.status && { status: query.status }),
      ...(query.type && { type: query.type }),
      ...(query.tag && { tag: query.tag }),
      ...(query.is_scheduled && { is_scheduled: Boolean(query.is_scheduled) }),
      ...(query.start_date && query.end_date && {
        $and: [
          { start: { $gte: new Date(query.start_date) } },
          { end_time: { $lte: new Date(query.end_date) } },
        ],
      }),
      ...(query.min_duration && query.max_duration && {
        duration_minutes: { $gte: parseInt(query.min_duration), $lte: parseInt(query.max_duration) },
      }),
    };
  }
  public async UpdateThreadId (session_id: string, threadId: string): Promise<Session | void> {
    logger.info('UpdateThreadId Called', { session_id: session_id, threadId: threadId });
    const existingSession = await this.model.findOne({ _id: session_id }); // Fetch existing session doc
    if (existingSession) {
      const session = await this.model.findOneAndUpdate(
        { _id: session_id },
        {
          $set: {
            thread_id: threadId,
          },
        },
        { new: true }, // Return the updated document
      );
      if (session) {
        logger.info('UpdateThreadId Successfull', { session: session });
        return session;
      }
      logger.error('Error in updating threadId on session');
      throw new Error('Error in updating threadId on session');

    } else {
      logger.error('Error in UpdateThreadId');
      throw new Error('Unable to find existing session');
    }
  }
  public async bookSession (bookSessionData: bookSessionInput,
  ): Promise<Anonymous_Session | void> {
    logger.info('bookSession Called', { bookSessionData: bookSessionData });
    const duration_minutes = 0;
    const code = await generateCode();
    const session_id = new mongoose.Types.ObjectId();
    const invitelink = (bookSessionData.referrer || process.env.BASE_URL) + 'salestool/joinroom/' + session_id;
    let starttime = new Date().toISOString();
    const ProjectModel = mongoose.model(bookSessionData.organization_id + '_projects', projectSchema);
    const project = await ProjectModel.findById(bookSessionData.project_id);
    const session_duration = project?.projectSettings?.pixelstreaming?.session_duration;
    const project_session_duration: number = typeof session_duration === 'number' ? session_duration : 30;
    // Default session_duration is 30 minutes
    if (bookSessionData.is_scheduled) {
      if (!bookSessionData.schedule_time) {
        logger.error('Scheduled time not available for scheduled meeting');
        throw new Error('Scheduled time not available for scheduled meeting');
      }
      starttime = bookSessionData.schedule_time;
    }
    const endtime = new Date(starttime);
    endtime.setSeconds(endtime.getSeconds() + (project_session_duration * 60));

    const newSession: Anonymous_Session = {
      _id: session_id,
      project_id: bookSessionData.project_id,
      status: bookSessionData.is_scheduled ? SessionStatus.SCHEDULED : SessionStatus.ACTIVE,
      code: code,
      duration_minutes: duration_minutes,
      organization_id: bookSessionData.organization_id,
      type: bookSessionData.type,
      source: bookSessionData.source,
      is_scheduled: bookSessionData.is_scheduled,
      schedule_time: bookSessionData.is_scheduled ? bookSessionData.schedule_time : null,
      description: bookSessionData.description,
      start: starttime,
      last_interaction_time: new Date().toISOString(),
      end_time: endtime.toISOString(),
      config: bookSessionData.config,
      invite_link: invitelink,
      participants: [],
    };
    const sessions = new this.model(newSession);
    const session = await sessions.save();
    await this.updateFirestore(session._id);
    logger.info('BookSession Successfull', { session: session });
    return session;
  }
  public async getAnonymousSessions (project_id: string): Promise<object | void> {
    logger.info('getAnonymousSessions Called', { project_id: project_id });
    try {
      const result = await this.model.find({ user_id: null, project_id: project_id });
      logger.info('getAnonymousSessions Successfull', { result: result });
      return result;
    } catch (error) {
      logger.error('Error in getAnonymousSessions ', { message: error });
      throw new Error('Unable to fetch sesisons for this organization');
    }
  }
  public async AssignSession (user_id: string, session_id: string): Promise<Session | void> {
    logger.info('AssignSession Called', { user_id: user_id, session_id: session_id });
    try {
      const existingSessions = await this.model.find({ user_id: user_id });
      const sessionToAssign = await this.model.findOne({ _id: session_id }); // Fetch existing session doc
      if (!sessionToAssign) {
        logger.error('Unable to find session');
        throw new Error('Unable to find session');
      }
      for (const existingSession of existingSessions) {
        const newSessionStart = new Date(sessionToAssign.start);
        const newSessionEnd = new Date(sessionToAssign.end_time);
        const existingSessionStart = new Date(existingSession.start);
        const existingSessionEnd = new Date(existingSession.end_time);

        // Check if the sessions overlap
        if (
          (newSessionStart >= existingSessionStart && newSessionStart < existingSessionEnd) ||
          (newSessionEnd > existingSessionStart && newSessionEnd <= existingSessionEnd) ||
          (existingSessionStart >= newSessionStart && existingSessionStart < newSessionEnd) ||
          (existingSessionEnd > newSessionStart && existingSessionEnd <= newSessionEnd)
        ) {
          logger.error('User has another conflicting session');
          throw new Error('User has another conflicting session');
        }
      }
      sessionToAssign.user_id = user_id;
      const session = await sessionToAssign.save();
      logger.info('AssignSession Successfull', { session: session });
      return session;
    } catch (error) {
      logger.error('Error in AssignSession', { message: error });
      throw new Error('Unable to assign session to user' + error);
    }

  }
  public async CancelSession (session_id: string, organization_id?: string): Promise<Session | void> {
    const orgId = organization_id || await this.getOrganizationId(session_id);
    const LeadsModel = mongoose.model(orgId + '_leads', leadsSchema);
    const sessionLeads = await LeadsModel.find({ session_id: session_id });
    const session = await this.model.findOneAndUpdate(
      { _id: session_id },
      {
        $set: {
          status: SessionStatus.CANCELLED,
          end_time: null,
          participants: sessionLeads,
        },
      },
      { new: true },
    );
    try {
      const firestoreUpdate = await this.updateFirestore(session_id);
      if (session && firestoreUpdate) {
        // Trigger webhooks
        const webhook = new WebhookSubcriptionModule(orgId);
        const webhookEvent = new WebhookEventsModule(orgId);

        const webhookSubscriptions = await webhook.getWebhook('session_deleted') as webhookData[];
        if (webhookSubscriptions && webhookSubscriptions?.length) {
          const queuedEvents = webhookSubscriptions
            .filter((sub: webhookData) => sub.targetUrl)
            .map((sub: webhookData) =>
              webhookEvent.createEvents({
                webhook_id: sub._id,
                organization_id: orgId,
                eventType: 'session_deleted',
                targetUrl: sub.targetUrl,
                data: session,
                log: [],
                _id: new mongoose.Types.ObjectId().toString(),
              }),
            );

          Promise.allSettled(queuedEvents).then((results) => {
            results.forEach((result, index) => {
              const sub = webhookSubscriptions[index];
              if (result.status === 'rejected') {
                logger.error('Failed to create event during session deletion', {
                  Error: result.reason,
                  _id: session._id.toString(),
                  webhook_id: sub._id,
                });
              } else {
                if (result.status === 'fulfilled' && !(result as webhookResult).value.success) {
                  logger.error('Failed to create event during session deletion', {
                    Error: (result as webhookResult).value,
                    eventId: (result as webhookResult).value._id,
                    webhook_id: sub._id,
                  });
                }
                logger.info('Webhook event created during session deletion', {
                  eventId: (result as webhookResult).value._id,
                  webhook_id: sub._id,
                });
              }
            });
          });
        }

        return session as Session;
      }
      throw new Error('Error in updating session on Firestore');

    } catch (error) {
      throw new Error('An unexpected error occurred while cancelling session');
    }
  }

  public async AssignInstance (project_id: string, session_id: string):
    Promise<{ status: number; data?: object; error?: string }> {
    logger.info('AssignInstance Called', { project_id, session_id });
    try {
      const session = await this.model.findOne({ _id: session_id });
      if (!session) {
        logger.error('Session not found');
        return { status: 404, error: 'Session not found' };
      }

      const projectModule = new ProjectModule(session.organization_id);
      const project = await projectModule.getProjectById(project_id);
      if (!project || !project.projectSettings.pixelstreaming.pixel_streaming_endpoint) {
        logger.error('Project or pixel streaming endpoint not found');
        return { status: 404, error: 'Project or pixel streaming endpoint not found' };
      }

      const pixelStreamingEndpoint = project.projectSettings.pixelstreaming.pixel_streaming_endpoint;
      const agent = new https.Agent({
        rejectUnauthorized: false,
      });
      try {
        const response = await axios.get(`${pixelStreamingEndpoint}/geturl`, { httpsAgent: agent });
        // Console.log("Pixel streaming response-----------", response.data);

        if (response.data === 'in queue') {
          logger.info('AssignInstance In Queue');
          return { status: 202, data: { message: 'in queue' } };
        }

        session.pixel_streaming_link = response.data;
        // Console.log("Setting session pixel_streaming_link:@@@@@@@@@@@@@@@@@@@@@", session.pixel_streaming_link);
        session.is_pixelstreaming_active = true;
        session.is_reserved = true;
        session.instance_start_time = new Date().toISOString();

        const updatedSession = await session.save();
        logger.info('AssignInstance Successful', { updatedSession });
        return { status: 200, data: updatedSession };
      } catch (axiosError) {
        if (axios.isAxiosError(axiosError)) {
          logger.error('Axios error in AssignInstance', {
            status: axiosError.response?.status,
            data: axiosError.response?.data,
            message: axiosError.message,
            url: `${pixelStreamingEndpoint}/geturl`,
          });
          return {
            status: axiosError.response?.status || 500,
            error: `Error fetching pixel streaming URL: ${axiosError.message}`,
          };
        }
        throw axiosError; // Re-throw if it's not an Axios error
      }
    } catch (error) {
      logger.error('Error in AssignInstance', { error });
      return { status: 500, error: `Unable to assign instance to session: ${error}` };
    }
  }
  // For rejoining the session-----
  public async RejoinSessionStatus (session_id: string): Promise<Session | void> {
    logger.info('RejoinSessionStatus Called', { session_id: session_id });
    const session = await this.model.findOneAndUpdate(
      { _id: session_id },
      { $set: { status: 'on-going' } },
      { new: true },
    );

    if (session) {
      await this.updateFirestore(session_id);
      logger.info('RejoinSessionStatus Successful', { session });
      return session;
    }

    logger.error('Error in RejoinSessionStatus');
    throw new Error('Unable to update session status');
  }
  public async getAvailableSlotsForDateRange (getSlotsObj: getMonthlySlotsInput): Promise<object[]> {
    const projectModule = new ProjectModule(getSlotsObj.organization_id as string);
    const project = await projectModule.getProjectById(getSlotsObj.project_id);

    if (project && project.projectSettings && project.projectSettings.general.slots &&
      project.projectSettings.pixelstreaming.max_concurrent_sessions !== undefined) {

      const maxConcurrentSessions = project.projectSettings.pixelstreaming.max_concurrent_sessions;
      const totalSlots = project.projectSettings.general.slots.length;
      if (maxConcurrentSessions <= 0) {
        logger.error('Invalid max_concurrent_sessions value. It must be greater than 0.');
        throw new Error('Invalid max_concurrent_sessions value. It must be greater than 0.');
      }
      const timezone = getSlotsObj.zone || 'UTC';
      // Fetch all sessions within the date range
      const scheduledSessions = await this.model.find({
        is_scheduled: true,
        status: { $ne: SessionStatus.CANCELLED },
        organization_id: getSlotsObj.organization_id,
        project_id: getSlotsObj.project_id,
        schedule_time: {
          $gte: moment.tz(getSlotsObj.from_date, timezone).startOf('day').toISOString(),
          $lte: moment.tz(getSlotsObj.to_date, timezone).endOf('day').toISOString(),
        },
      });
      // Sort the scheduled sessions by date
      const sessionsByDate: { [key: string]: number } = {};
      scheduledSessions.forEach((session) => {
        const dateKey = moment.tz(session.schedule_time, timezone).format('YYYY-MM-DD');
        if (!sessionsByDate[dateKey]) {
          sessionsByDate[dateKey] = 0;
        }
        sessionsByDate[dateKey] += 1;
      });

      // Iterate over each date in the range and calculate available slots
      const currentDate = moment(getSlotsObj.from_date).startOf('day');
      const endDate = moment(getSlotsObj.to_date).endOf('day');
      const allSlotAvailability: { date: string, availableSlots: number }[] = [];

      while (currentDate.isSameOrBefore(endDate, 'day')) {
        const currentDay = currentDate.format('YYYY-MM-DD');
        const scheduledCount = sessionsByDate[currentDay] || 0;
        const availableSlots = (maxConcurrentSessions * totalSlots) - scheduledCount;

        allSlotAvailability.push({
          date: currentDay,
          availableSlots: Math.max(0, availableSlots), // Avoid negative
        });

        // Move to the next day
        currentDate.add(1, 'day');
      }

      return allSlotAvailability;
    }

    logger.error('Error in getAvailableSlotsForDateRange');
    throw new Error('Unable to fetch sessions. Project or slots or max concurrent sessions not found.');
  }

  public async CheckAvailableSessionSlots (
    organization_id: string,
    startTime: Date,
    endTime: Date,
  ): Promise<object> {
    const organizationSessionLimit = 2;
    const scheduledSessions = await this.model.find({
      organization_id: organization_id,
      is_scheduled: true,
      status: { $ne: SessionStatus.CANCELLED },
      $or: [
        {
          schedule_time: { $lt: endTime.toISOString() },
          end_time: { $gt: startTime.toISOString() },
        },
      ],
    });

    const sessionMinutes: number[] = [];
    let isLimitExhausted = false;
    let count = 0;
    if (scheduledSessions.length > 0) {
      // Track the number of active sessions per minute
      scheduledSessions.forEach((session) => {
        console.log('loop all sessions', session);
        const start = new Date(session.schedule_time as string).getTime();
        const end = new Date(session.end_time as string).getTime();

        for (let t = start; t < end; t += 60 * 1000) { // Iterate minute by minute
          console.log('firstloop', t);
          sessionMinutes.push(t);
        }
      });

      // Check if adding this new session violates the limit
      for (let t = startTime.getTime(); t < endTime.getTime(); t += 60 * 1000) {
        console.log('second t', t);
        count = sessionMinutes.filter((time) => time === t).length;
        console.log('count', count);
        if (count >= organizationSessionLimit) {
          isLimitExhausted = true;
          break;
        }
      }
    }

    const obj = {
      activeSessions: scheduledSessions.length,
      availableSessions: isLimitExhausted ? 0 : Math.max(organizationSessionLimit - scheduledSessions.length, 0), // Ensure it doesn't go negative
    };

    return obj;
  }
  public async getSessionSlots (organization_id: string, date: Date): Promise<SessionSlotResult[]> {
    const organizationSessionLimit = 2;
    const givenDate = new Date(date);
    const startOfDay = new Date(givenDate.setUTCHours(0, 0, 0, 0));
    const endOfDay = new Date(givenDate.setUTCHours(23, 59, 59, 999));

    // Fetch all sessions for the day
    const sessions = await this.model.find({
      organization_id,
      is_scheduled: true,
      status: { $ne: SessionStatus.CANCELLED },
      schedule_time: { $gte: startOfDay.toISOString(), $lte: endOfDay.toISOString() },
    }).lean();

    // Collect all unique start and end times
    let times: number[] = [];
    sessions.forEach((s) => {
      times.push(new Date(s.schedule_time as string).getTime());
      times.push(new Date(s.end_time as string).getTime());
    });
    console.log('times before', times);
    times = Array.from(new Set(times)).sort((start, end) => start - end);
    console.log('times after', times);
    // For each interval, count active sessions and available slots
    const intervals: SessionSlotResult[] = [];
    for (let i = 0; i < times.length - 1; i++) {
      const intervalStart = new Date(times[i]);
      const intervalEnd = new Date(times[i + 1]);
      const activeSessionsList = sessions.filter((s) =>
        new Date(s.schedule_time as string) < intervalEnd && new Date(s.end_time as string) > intervalStart,
      );
      const activeSessions = activeSessionsList.length;
      const user_ids = activeSessionsList.map((s) => s.user_id);
      intervals.push({
        user_ids,
        schedule_time: intervalStart.toISOString(),
        end_time: intervalEnd.toISOString(),
        activeSessions,
        availableSession: Math.max(0, organizationSessionLimit - activeSessions),
      });
    }
    return intervals;
  }

  public async CheckInstanceAvailability (config: AvalilabilityInstanceConfig): Promise<Avalilability> {
    const { organization_id, start_time, maxConcurrentSessions, exclude_session_id } = config;
    const { end_time } = config;

    // Validation: Ensure start_time is valid
    if (!start_time) {
      throw new Error('start_time is required');
    }

    const startMoment = moment(start_time);
    if (!startMoment.isValid()) {
      throw new Error('Invalid start_time provided');
    }

    // Fetch org settings
    const orgModule = new OrganizationModule();
    const organization = await orgModule.GetOrganization(organization_id);
    const sessionLimit = maxConcurrentSessions || organization?.organizationSettings?.pixelstreaming?.max_concurrent_sessions || 1;

    // Compute end_time if missing or invalid
    let endMoment: moment.Moment;
    const isEndTimeMissing = !end_time ||
      end_time === null ||
      end_time === undefined ||
      (typeof end_time === 'object' && end_time.toString() === 'Invalid Date') ||
      !moment(end_time).isValid();

    if (isEndTimeMissing) {
      const defaultDuration = organization?.organizationSettings?.pixelstreaming?.duration || 30;
      endMoment = startMoment.clone().add(defaultDuration, 'minutes');
      if (!endMoment.isValid()) {
        throw new Error('Failed to compute valid end_time');
      }
    } else {
      endMoment = moment(end_time);
      if (!endMoment.isValid()) {
        throw new Error('Invalid end_time provided');
      }
    }

    // Validate end_time is after start_time
    if (endMoment.isSameOrBefore(startMoment)) {
      throw new Error('end_time must be after start_time');
    }

    // Build query for overlapping reserved sessions
    // Sessions overlap if: session_start < request_end AND session_end > request_start
    const baseQuery: any = {
      organization_id,
      is_reserved: true,
      status: {
        $in: [SessionStatus.SCHEDULED, SessionStatus.ACTIVE, SessionStatus.ONGOING],
      },
      // Overlap condition: session overlaps with requested time window
      instance_start_time: { $lt: endMoment.toISOString() },
      instance_end_time: { $gt: startMoment.toISOString() },
      ...(exclude_session_id && { _id: { $ne: exclude_session_id } }),
    };

    const conflictingSessions = await this.model.find(baseQuery);

    // If exclude_session_id is provided, we're checking availability for that session
    // The excluded session is already filtered out in the query above
    // We should NOT count it as a conflict - only count OTHER sessions that overlap

    // Calculate availability (excluded session is NOT counted as conflict)
    const activeSessionsCount = conflictingSessions.length;
    const availableCount = sessionLimit - activeSessionsCount;

    // Extract conflicting session IDs (excluded session is NOT included)
    const conflictingSessionIds = conflictingSessions.map((session) => session._id.toString());

    return {
      slotStart: startMoment.toISOString(),
      slotEnd: endMoment.toISOString(),
      activeSessions: activeSessionsCount,
      availableSessions: Math.max(0, availableCount),
      available: availableCount > 0,
      conflictingSessionIds: conflictingSessionIds,
    };
  }

}

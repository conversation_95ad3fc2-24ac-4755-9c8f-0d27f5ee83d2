import mongoose from 'mongoose';
import { Models } from '../../types/extras';
import { statusSchema } from '../../schema/statusSchema';
import { statusCategoryType } from '../../types/projects';
import logger from '../../config/logger';

export class StatusModule{
  private model : mongoose.Model<statusCategoryType>;
  private project_id: string;
  constructor (project_id:string){
    this.project_id = project_id;
    this.model = mongoose.model<statusCategoryType>(`${project_id}${Models._STATUS}`, statusSchema);
  }

  public async createDefaultStatusesBulk (): Promise<statusCategoryType[]> {
    const defaultStatuses = [
      { name: 'available', order: 1 },
      { name: 'onhold', order: 2 },
      { name: 'not available', order: 3 },
    ];

    const bulkOps = defaultStatuses.map((d) => {
      const substatusArray: { name: string }[] = [{ name: d.name }];

      // Add "sold" and "reserved" to "not available" category
      if (d.name === 'not available') {
        substatusArray.push({ name: 'sold' });
        substatusArray.push({ name: 'reserved' });
      }

      return {
        updateOne: {
          filter: { name: d.name },
          update: {
            $setOnInsert: {
              name: d.name,
              order: d.order,
              substatus: substatusArray,
              created_at: new Date().toISOString(),
              __v: 0,
            },
          },
          upsert: true,
        },
      };
    });

    await this.model.bulkWrite(bulkOps);

    const statuses = await this.model.find({}).lean();
    return statuses as statusCategoryType[];
  }

  public async createSubstatus (
    category: string,
    substatus: string,
  ): Promise<statusCategoryType | null> {
    logger.info('createSubstatus called', { category, substatus });
    try {
      // Find the status category by name
      const existingStatus = await this.model.findOne({ name: category });

      if (!existingStatus) {
        throw new Error(`Category "${category}" not found in status collection`);
      }

      // Get existing substatus array
      const existingSubstatusArray: { name: string }[] = Array.isArray(existingStatus.substatus)
        ? existingStatus.substatus
        : [];

      // Check if substatus already exists
      if (existingSubstatusArray.some((s) => s.name === substatus)) {
        return existingStatus as statusCategoryType | null;
      }

      // Add new substatus to array
      const updatedSubstatus = [
        ...existingSubstatusArray,
        { name: substatus },
      ];

      // Update using the name field since we found it by name
      const updateResult = await this.model.updateOne(
        { name: category },
        { $set: { substatus: updatedSubstatus } },
      );

      if (updateResult.matchedCount === 0) {
        throw new Error(`Failed to create substatus for category "${category}"`);
      }

      // Fetch the updated document
      const finalStatus = await this.model.findOne({ name: category });

      if (!finalStatus) {
        throw new Error(`Failed to fetch created substatus for category "${category}"`);
      }

      logger.info('Substatus added successfully', { category, substatus });
      return finalStatus as statusCategoryType;

    } catch (error) {
      logger.error('Error creating substatus', { category, substatus, error });
      throw error;
    }
  }

  public async getStatus (): Promise<statusCategoryType[]> {
    logger.info('getStatus called', { collection: this.model.modelName });

    try {
      // Use .lean() to get plain objects and check count
      const statuses = await this.model.find({}).lean();

      if (!statuses || statuses.length === 0){
        const status = await this.createDefaultStatusesBulk();

        // Verify statuses were actually created
        if (!status || status.length === 0) {
          logger.error('createDefaultStatusesBulk returned empty array', { collection: this.model.modelName });
          throw new Error(`Failed to create default statuses for collection: ${this.model.modelName}`);
        }

        return status;
      }

      return statuses as statusCategoryType[];
    } catch (error) {
      logger.error('Error getting statuses', { error, collection: this.model.modelName });
      throw error;
    }
  }

  public async getAllSubstatus (): Promise<string[]> {
    logger.info('getAllSubstatus called');

    try {
      const statuses = await this.model.find({}).lean();

      if (!statuses || statuses.length === 0){
        throw new Error('No Substatuses found');
      }

      const substatusSet = new Set<string>();

      // Loop through each status and extract unique substatus names
      for (const statusData of statuses) {
        if (statusData?.substatus && Array.isArray(statusData.substatus)) {
          for (const sub of statusData.substatus) {
            if (sub?.name && typeof sub.name === 'string') {
              substatusSet.add(sub.name);
            }
          }
        }
      }

      return Array.from(substatusSet);
    } catch (error) {
      logger.error('Error getting all substatuses', { error });
      throw error;
    }
  }
}

import axios from 'axios';
import logger from '../../config/logger';
import { FormattedRate } from '../../types/organization';

type currencyObject = {
  baseCurrency: string,
  exchangeRatio: FormattedRate[]
}

type CurrencyRates = {
  [key: string]: number | string;
};

function formattedArray (ratesObject: CurrencyRates): FormattedRate[] {
  const filteredRates = { ...ratesObject };

  // Deleting 'conversion_date'
  delete filteredRates.conversion_date;

  const formattedRates: FormattedRate[] = Object.entries(filteredRates).map(([currency, rate]) => ({
    currency,
    rate: typeof rate === 'number' ? rate : null,
  }));

  return formattedRates;
}

export class CurrencyFetchModule {
  private readonly authApi: string;
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly apiHost: string;
  private readonly projectKey: string;
  private readonly conversionKey: string;
  private readonly containerValue: string;

  constructor () {
    this.authApi = process.env.CURRENCY_AUTH_URL || '';
    this.clientId = process.env.CURRENCY_CLIENT_ID || '';
    this.clientSecret = process.env.CURRENCY_CLIENT_SECRET || '';
    this.apiHost = process.env.API_HOST_URL || '';
    this.projectKey = process.env.PROJECT_KEY || '';
    this.containerValue = process.env.CURRENCY_CONTAINER_VALUE || '';
    this.conversionKey = process.env.CURRENCY_CONVERSION_KEY || '';
  }

  private async getInitialBearerToken (): Promise<string> {
    try {

      const response = await axios.post(this.authApi + '/oauth/token?grant_type=client_credentials', {}, {
        auth: {
          username: this.clientId,
          password: this.clientSecret,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      logger.info('Initial token obtained successfully');

      return response.data.access_token;
    } catch (error) {
      logger.error('Error getting initial token', { error });
      throw new Error('Failed to get initial token');
    }
  }

  public async fetchCurrencyRates (): Promise<currencyObject> {
    try {
      // Step 1: Get initial token
      const initialToken = await this.getInitialBearerToken();

      // Step 2: Get Exchange Rates
      const response = await axios.get(
        `${this.apiHost}/${this.projectKey}/custom-objects/${this.containerValue}/${this.conversionKey}`,
        {
          headers: {
            Authorization: `Bearer ${initialToken}`,
          },
        },
      );

      const resultantObject: currencyObject = {
        baseCurrency: response.data.key,
        exchangeRatio: formattedArray(response.data.value),
      };

      logger.info('Resultant Object obtained successfully');

      return resultantObject;
    } catch (error) {
      logger.error('Error in getUnitsList flow', { error });
      throw error;
    }
  }
}

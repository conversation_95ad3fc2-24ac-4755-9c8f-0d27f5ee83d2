import { leadsSchema } from '../../schema/leadsSchema';
import { Leads, updateLeadDurationObj, updateLeadInput, createLeadInput,
  leadAnalyticsQuery, LeadSource,
  UpdateLeadInput} from '../../types/leads';
import mongoose, { PipelineStage, Types } from 'mongoose';
import { sessionSchema } from '../../schema/sessionSchema';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
import { invalidateCacheByPattern } from '../cdn';
import { WebhookSubcriptionModule } from '../webhookSubcription';
import { WebhookEventsModule } from '../webhooksEvents';
import { webhookData, webhookResult } from '../../types/webhooks';

export async function invalidateLeadAPIs (): Promise<any> {
  try {
    const apiPaths = [
      '/publicapis/CreateLead',
      '/publicapis/GetLead',
    ];

    const results = await invalidateCacheByPattern(apiPaths);

    return {
      success: true,
      message: 'CDN cache invalidation for multiple APIs initiated successfully',
      data: results,
    };
  } catch (error) {
    console.error('CDN cache invalidation failed:', error);
    throw {
      success: false,
      message: 'Failed to invalidate CDN cache',
      error: error instanceof Error ? error.message : 'Unknown error',
    };

  }
}

export class LeadsModule {
  private model: mongoose.Model<Leads>;

  constructor (organization_id: string) {
    console.log(organization_id);
    this.model = mongoose.model<Leads>(
      `${organization_id}${Models._LEADS}`,
      leadsSchema,
    );
  }
  public async CreateLead (createLeadData: createLeadInput):Promise<Leads | void> {
    logger.info('CreateLead Called', {createLeadData: createLeadData});
    try {
      if (!createLeadData.lead_source) {
        createLeadData.lead_source=LeadSource.SALES_SESSION;
      }

      const newLead = {
        _id: new Types.ObjectId(),
        session_id: createLeadData.session_id,
        organization_id: createLeadData.organization_id,
        name: createLeadData.name,
        phone_number: createLeadData.phone_number,
        email: createLeadData.email,
        duration_minutes: createLeadData.duration_minutes,
        joining_time: createLeadData.joining_time,
        interested_in: {
          type: createLeadData.type,
          project_id: createLeadData.project_id || undefined,
          unit_id: createLeadData.type !== 'project' ? createLeadData.unit_id : undefined,
        },
        lead_source: createLeadData.lead_source,
        lead_product_interest: createLeadData.lead_product_interest,
        lead_industry_type: createLeadData.lead_industry_type,
        start_time: createLeadData.start_time,
        user_id: createLeadData.user_id,
        lead_status: createLeadData.lead_status,
        next_interaction_time: new Date().toISOString(),
        last_interaction_time: new Date().toISOString(),
        end_time: createLeadData.end_time,
        lead_creation_time: new Date().toISOString(),
      };

      const leads = new this.model(newLead);
      const newlead = await leads.save();
      const organization_id  = newlead.organization_id;
      const webhook = new WebhookSubcriptionModule(organization_id);
      const webhookEvent = new WebhookEventsModule(organization_id);
      logger.info('CreateLead Successfully', {newlead: newlead});

      const webhookSubscriptions = await webhook.getWebhook('lead_created')  as webhookData[];
      if (webhookSubscriptions && webhookSubscriptions?.length>0) {
        const queuedEvents = webhookSubscriptions
          .filter((sub: webhookData) => sub.targetUrl)
          .map((sub: webhookData) =>
            webhookEvent.createEvents({
              webhook_id: sub._id,
              organization_id,
              eventType: 'lead_created',
              targetUrl: sub.targetUrl,
              data: newlead,
              log: [],
              _id: new mongoose.Types.ObjectId().toString(),
            }),
          );

        // Non-blocking webhook creation
        Promise.allSettled(queuedEvents).then((results) => {
          results.forEach((result, index) => {
            const sub = webhookSubscriptions[index];
            if (result.status === 'rejected'){
              logger.error('Failed to create event  during lead creation', {
                Error: result.reason,
                _id: newlead?._id.toString(),
                webhook_id: sub._id,
              });
            } else {
              if (result.status === 'fulfilled' && !(result as webhookResult).value.success){
                logger.error('Failed to create event  during lead creation', {
                  Error: (result as webhookResult).value,
                  eventId: (result as webhookResult).value._id,
                  webhook_id: sub._id,
                });
              }
              logger.info('Webhook event created during lead creation', {
                eventId: (result as webhookResult).value._id,
                webhook_id: sub._id,
              });
            }
          });
        });
      }

      return newlead as Leads;
    } catch (error) {
      logger.error('Unable to create lead for this session', {message: error});
      throw new Error('Unable to create lead for this session'+error);
    }

  }

  public async UpdateLead (
    lead_id: string,
    updatedData: UpdateLeadInput,
  ): Promise<Leads | null> {
    const updatedLead = await this.model.findOneAndUpdate(
      { _id: lead_id },
      { $set: updatedData },
      { new: true },
    );

    if (updatedLead) {
      const organization_id = updatedLead.organization_id;
      const webhook = new WebhookSubcriptionModule(organization_id);
      const webhookEvent = new WebhookEventsModule(organization_id);

      const webhookSubscriptions = await webhook.getWebhook('lead_updated') as webhookData[];
      if (webhookSubscriptions && webhookSubscriptions?.length > 0) {
        const queuedEvents = webhookSubscriptions
          .filter((sub: webhookData) => sub.targetUrl)
          .map((sub: webhookData) =>
            webhookEvent.createEvents({
              webhook_id: sub._id,
              organization_id,
              eventType: 'lead_updated',
              targetUrl: sub.targetUrl,
              data: updatedLead,
              log: [],
              _id: new mongoose.Types.ObjectId().toString(),
            }),
          );

        Promise.allSettled(queuedEvents).then((results) => {
          results.forEach((result, index) => {
            const sub = webhookSubscriptions[index];
            if (result.status === 'rejected') {
              logger.error('Failed to create event during lead updation', {
                Error: result.reason,
                _id: updatedLead._id.toString(),
                webhook_id: sub._id,
              });
            } else {
              if (result.status === 'fulfilled' && !(result as webhookResult).value.success) {
                logger.error('Failed to create event during lead updation', {
                  Error: (result as webhookResult).value,
                  eventId: (result as webhookResult).value._id,
                  webhook_id: sub._id,
                });
              }
              logger.info('Webhook event created during lead updation', {
                eventId: (result as webhookResult).value._id,
                webhook_id: sub._id,
              });
            }
          });
        });
      }
    }

    return updatedLead;
  }

  public async GetLeads (
    organization_id: string,
    query: leadAnalyticsQuery,
  ): Promise<object | void> {
    logger.info('GetLeads Called', { organization_id, query });

    try {
      const mongoQuery: Record<string, unknown> = {};

      const projectIds = query.project_id;
      const statuses = query.status ;
      const userIds = typeof query.user_id === 'string'
        ? [query.user_id]
        : Array.isArray(query.user_id)
          ? query.user_id
          : undefined;
      if (projectIds) {
        mongoQuery['interested_in.project_id'] = { $in: projectIds };
      }

      if (statuses) {
        mongoQuery.lead_status = { $in: statuses };
      }

      if (userIds) {
        mongoQuery.user_id = { $in: userIds };
      }

      const pipeline: PipelineStage[] = [];

      pipeline.push(
        ...(Object.keys(mongoQuery).length ? [{ $match: mongoQuery }] : []),
        { $sort: { lead_creation_time: -1 } },
        {
          $group: {
            _id: null,
            leads: { $push: { k: '$_id', v: '$$ROOT' } },
          },
        },
        { $replaceRoot: { newRoot: { $arrayToObject: '$leads' } } },
      );

      const result = await this.model.aggregate(pipeline);

      if (result.length > 0) {
        logger.info('GetLeads Successfully', { result: result[0] });
        return result[0];
      }

      logger.warn('No leads found');
      return {};
    } catch (error) {
      logger.error('Unable to fetch leads for this organization', { message: error });
      throw new Error(`Unable to fetch leads for this organization - ${organization_id}`);
    }
  }

  public async DeleteLead (lead_id: string, organization_id :string): Promise<Leads | null> {
    const deletedLead = await this.model.findOneAndDelete({ _id: lead_id }).lean();
    if (deletedLead) {
      const webhook = new WebhookSubcriptionModule(organization_id);
      const webhookEvent = new WebhookEventsModule(organization_id);

      const webhookSubscriptions = await webhook.getWebhook('lead_deleted') as webhookData[];
      if (webhookSubscriptions && webhookSubscriptions?.length > 0) {
        const queuedEvents = webhookSubscriptions
          .filter((sub: webhookData) => sub.targetUrl)
          .map((sub: webhookData) =>
            webhookEvent.createEvents({
              webhook_id: sub._id,
              organization_id,
              eventType: 'lead_deleted',
              targetUrl: sub.targetUrl,
              data: deletedLead,
              log: [],
              _id: new mongoose.Types.ObjectId().toString(),
            }),
          );

        Promise.allSettled(queuedEvents).then((results) => {
          results.forEach((result, index) => {
            const sub = webhookSubscriptions[index];
            if (result.status === 'rejected') {
              logger.error('Failed to create event during lead deletion', {
                Error: result.reason,
                _id: lead_id.toString(),
                webhook_id: sub._id,
              });
            } else {
              if (result.status === 'fulfilled' && !(result as webhookResult).value.success) {
                logger.error('Failed to create event during lead deletion', {
                  Error: (result as webhookResult).value,
                  eventId: (result as webhookResult).value._id,
                  webhook_id: sub._id,
                });
              }
              logger.info('Webhook event created during lead deletion', {
                eventId: (result as webhookResult).value._id,
                webhook_id: sub._id,
              });
            }
          });
        });
      }
    }

    return deletedLead as Leads;
  }

  public async UpdateDuration (updateLeadData:updateLeadDurationObj):Promise<Leads|void> {
    logger.info('UpdateDuration Called', {updateLeadData: updateLeadData});
    try {
      const existingLead = await this.model.findById(updateLeadData.lead_id);

      if (!existingLead) {
        logger.error('Unable to find existing lead');
        throw new Error('Unable to find existing lead');
      }
      const newLastInteractionTime = new Date().toISOString();
      const newDuration = existingLead.duration_minutes + updateLeadData.duration_minutes;
      const lead = await this.model.findOneAndUpdate(
        { _id: updateLeadData.lead_id, session_id: updateLeadData.session_id },
        {
          $set: {
            last_interaction_time: newLastInteractionTime,
            duration_minutes: newDuration,
          },
        },
        { new: true }, // Return the updated document
      );

      if (lead) {
        logger.info('UpdateDuration Successfully', {lead: lead});
        return lead;
      }
      logger.error('Lead with this session ID does not exist');
      throw new Error('Lead with this session ID does not exist');
      logger.error('Error in updating lead');
      throw new Error('Error in updating lead');

    } catch (error){
      logger.error('Unexpected error occured while updating lead duration', {message: error});
      throw new Error('Unexpected error occured while updating lead duration');
    }

  }
  // Join Session for client
  public async JoinSession ( joinSessionObj: updateLeadInput ): Promise<Leads | void> {
    logger.info('JoinSession Called', {joinSessionObj: joinSessionObj});
    try {
      const SessionModel = mongoose.model('Session', sessionSchema);
      const session = await SessionModel.findById(joinSessionObj.session_id);
      if (!session) {
        logger.error('Session not found');
        throw new Error('Session not found');
      }
      const lead = await this.model.findOneAndUpdate(
        { _id: joinSessionObj.lead_id },
        { $set: {
          joining_time: new Date().toISOString(),
          start: session.start,
        },
        },
      );
      logger.info('JoinSession Successfully', {lead: lead});
      return lead as Leads;
    } catch (error) {
      logger.error('An unexpected error occurred while joining the session', {message: error});
      throw new Error('An unexpected error occurred while joining the session'+error);
    }
  }
  // Leave Session API for client
  public async LeaveSession (joinSessionObj: updateLeadInput):Promise<Leads|void> {
    logger.info('LeaveSession Called', {joinSessionObj: joinSessionObj});
    try {
      const SessionModel = mongoose.model('Session', sessionSchema);
      const session = await SessionModel.findById(joinSessionObj.session_id);
      if (!session) {
        logger.error('Session not found');
        throw new Error('Session not found');
      }
      const lead = await this.model.findOneAndUpdate(
        { _id: joinSessionObj.lead_id },
        { $set: {
          last_interaction_time: new Date().toISOString(),
          end_time: new Date().toISOString(),
        },
        },
      );
      logger.info('LeaveSession Successfully', {lead: lead});
      return lead as Leads;
    } catch (error) {
      logger.error('An unexpected error occurred while leaving the session', {message: error});
      throw new Error('An unexpected error occurred while leaving the session');
    }
  }
  public async getAnalytics (query: leadAnalyticsQuery, organizationId: string): Promise<object | null> {
    logger.info('getAnalytics Called', {query: query, organizationId: organizationId});
    let pipeline: PipelineStage[] = [];
    const mongoQuery : leadAnalyticsQuery = {
      ...(query.project_id && { 'interested_in.project_id': query.project_id }),
      ...(query.user_id && { user_id: query.user_id }),
      ...(query.status && { status: query.status }),
      ...(query.lead_source && { lead_source: query.lead_source }),
      ...(query.start_time && {
        start: { $gte: new Date(query.start_time) },
      }),
    };
    if (Object.keys(query).length === 0) {
      // Execute MongoDB query for all leads (without filters)
      pipeline = [
        {
          $match: {
            organization_id: organizationId,
          },
          $group: {
            _id: null,
            total_duration_all: { $sum: '$duration_minutes' },
            num_leads_all: { $sum: 1 },
            leads: { $push: '$$ROOT' },
          },
        },
        {
          $project: {
            _id: 0,
            analytics: {
              total_duration_all: '$total_duration_all',
              num_leads_all: '$num_leads_all',
            },
            leads: '$leads',
          },
        },
      ];
    } else {
      pipeline = [
        {
          $match: {
            $and: [
              mongoQuery,
            ].filter(Boolean),
          },
        },
        {
          $group: {
            _id: null,
            total_duration: { $sum: '$duration_minutes' },
            num_leads: { $sum: 1 },
            leads: { $push: '$$ROOT' },
          },
        },
        {
          $project: {
            _id: 0,
            analytics: {
              total_duration: '$total_duration',
              num_leads: '$num_leads',
            },
            leads: '$leads',
          },
        },
      ];
    }

    const result = await this.model.aggregate(pipeline);

    if (result.length > 0) {
      logger.info('getAnalytics Successfully', {result: result[0]});
      return result[0];
    }
    logger.error('An unexpected error in getAnalytics');
    return null;
  }
  public async GetLeadById (leadId: string): Promise<Leads |null>{
    logger.info('GetLeadById Called', {leadId: leadId});
    try {
      const lead = await this.model.findOne({ _id: leadId });
      logger.info('GetLeadById Successfully', {lead: lead});
      return lead as Leads;
    } catch (error) {
      logger.error('An unexpected error in getAnalytics', {message: error});
      throw new Error('Error in GetLeadById');
    }
  }
  public async GetLeadsBySessionId (session_id: string): Promise<Leads[] |null>{
    logger.info('GetLeadsBySessionId Called', {session_id: session_id});
    try {
      const leadsArray = await this.model.find({ session_id: session_id });
      logger.info('GetLeadsBySessionId Successfully', {leadsArray: leadsArray});
      return leadsArray as Leads[];
    } catch (error) {
      logger.error('An unexpected error in GetLeadsBySessionId', {message: error});
      throw new Error('An unexpected error occurred while leaving the session');
    }
  }
  public async UpdateThreadId (lead_id: string, threadId:string):Promise<Leads|void> {
    const existingSession = await this.model.findOne({ _id: lead_id }); // Fetch existing session doc
    if (existingSession) {
      const session = await this.model.findOneAndUpdate(
        { _id: lead_id },
        {
          $set: {
            thread_id: threadId,
          },
        },
        { new: true }, // Return the updated document
      );
      if (session) {
        return session;
      }
      throw new Error('Error in updating threadId on lead');

    } else {
      throw new Error('Unable to find existing lead');
    }
  }
}

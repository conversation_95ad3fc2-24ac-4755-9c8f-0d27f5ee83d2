import { DamacUnits, UpdateUnit, resultObject } from '../../types/damacUnitsSyncUp';
import { Units } from '../../types/units';
import { UnitModule } from '../units/';
import { ExternalUnitModule } from '../damac/index';
import logger from '../../config/logger';
import { ProjectModule } from '../projects/';
import { sendMessageToChannel } from '../../helpers/slackMessenger';
import { Project, projectMetaData } from '../../types/projects';
import { StatusModule } from '../status';

export class DamacUnitSyncUpModule {
  public async syncUnits (org:string) : Promise<resultObject | null> {
    if (!org){
      console.log('Organization not found');
      logger.error('DAMAC_ORG is not set, unable to sync units');
      return null;
    }
    const projectsModule = new ProjectModule(org || '');

    function chunkArray (array: UpdateUnit[], size: number): UpdateUnit[][] {
      const chunks: UpdateUnit[][] = [];
      for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
      }
      return chunks;
    }

    const Unitsnotpresent:any = {};
    async function syncData (project:Project, drupalID:string): Promise<UpdateUnit[] | null> {
      try {
        const projectID = project._id;
        const unit = new UnitModule(projectID.toString());
        const damacModule = new ExternalUnitModule();
        const data = await unit.getListOfUnits(projectID.toString(), -1, -1, '');

        const unitsList = await damacModule.getUnitsList(drupalID);
        let propvrdata = JSON.parse(JSON.stringify(data));
        propvrdata = propvrdata.data;
        const damac = JSON.parse(JSON.stringify(unitsList));
        console.log('Total units:-', damac.length);

        if (!project) {
          logger.info('No project found');
          return null;
        }

        // Check if propvrdata is an empty object or null/undefined
        if (!propvrdata || Object.keys(propvrdata).length === 0) {
          logger.info(`No units found for project ${projectID}`);
          return null;
        }

        const propvrLookup: { [key: string]: Units } = {};

        // Helper function to get unit name from metadata.name or fallback to name
        const getUnitName = (unitobject: Units): string => {
          if (unitobject?.metadata && typeof unitobject?.metadata === 'object' && unitobject?.metadata !== null) {
            const metadata = unitobject.metadata as { [key: string]: unknown };
            if (metadata?.name && typeof metadata?.name === 'string') {
              console.log('------metadata------', unitobject.metadata);
              return metadata.name;
            }
          }
          return unitobject.name;
        };

        if (damac.length > 0) {
          // Get valid substatuses
          const statusModule = new StatusModule(projectID.toString());
          let validSubstatuses: string[] = [];
          try {
            validSubstatuses = await statusModule.getAllSubstatus();
          } catch (error) {
            logger.error('Error getting valid substatuses for status mapping', { error, projectID });
          }

          const STATUS_MAP: Record<string, string> = {};

          if (validSubstatuses.includes('sold')) {
            STATUS_MAP.Sold = 'sold';
            STATUS_MAP['Not Available'] = 'sold';
          }

          if (validSubstatuses.includes('available')) {
            STATUS_MAP.Available = 'available';
          }

          for (const key in propvrdata) {
            const unitName = getUnitName(propvrdata[key]);
            propvrLookup[unitName] = propvrdata[key];
          }

          const result = damac.reduce((differences: UpdateUnit[], damacUnit: DamacUnits) => {
            const matchingPropvrUnit = propvrLookup[damacUnit.unitNumber];

            if (!Unitsnotpresent[project?.name]){
              Unitsnotpresent[project?.name]=[];
            }

            if (!matchingPropvrUnit){
              if (damacUnit.status === 'Available'){
                Unitsnotpresent[project?.name].push(damacUnit);
              }
            }

            if (matchingPropvrUnit) {
              const damacStatus = STATUS_MAP[damacUnit.status];

              // Convert values for comparison
              const damacPriceString = damacUnit.price !== null &&
              damacUnit.price !== undefined ? String(damacUnit.price) : '';

              const propvrMeasurement = matchingPropvrUnit.measurement !== undefined ?
                String(matchingPropvrUnit.measurement) : '';

              const damacAreaString = damacUnit.area !== null &&
              damacUnit.area !== undefined ? String(damacUnit.area) : '';

              // Check what needs to be updated
              const statusNeedsUpdate = matchingPropvrUnit.status !== damacStatus;
              const priceNeedsUpdate = matchingPropvrUnit.price !== damacPriceString;
              const measurementNeedsUpdate = propvrMeasurement !== damacAreaString;

              // If any field needs updating, create an update object
              if (statusNeedsUpdate || priceNeedsUpdate || measurementNeedsUpdate) {
                // Create the base update object with required fields
                const updateObj: UpdateUnit = {
                  _id: matchingPropvrUnit._id,
                  name: damacUnit.unitNumber,
                };

                // For Unit A: status is mismatching - include only status
                if (statusNeedsUpdate) {
                  updateObj.status = damacStatus;
                }

                // For Unit B: price is mismatching - include only price
                if (priceNeedsUpdate) {
                  updateObj.price = damacPriceString;
                }

                // For Unit C: measurement is mismatching - include only measurement
                if (measurementNeedsUpdate) {
                  // Convert area to number for the UpdateUnit object
                  updateObj.measurement = damacUnit.area !== null && damacUnit.area !== undefined ?
                    Number(damacUnit.area) : 0;
                }

                // The update object will contain only the fields that need updating for this specific unit
                differences.push(updateObj);
              }
            }

            return differences;
          }, []);

          console.log('Units requiring updates:', result.length);

          for (const key in propvrdata) {
            const unitName = getUnitName(propvrdata[key]);
            const existsInDamac = damac.some((d: DamacUnits) => d.unitNumber === unitName);
            // Only use 'sold' status if it exists in getAllSubstatus
            if (!existsInDamac && propvrdata[key].status !== 'sold') {
              if (validSubstatuses.includes('sold')) {
                result.push({
                  _id: propvrdata[key]._id,
                  status: 'sold',
                  name: propvrdata[key].name,
                });
              }
            }
          }

          if (Object.values(Unitsnotpresent).length) {
            if (Unitsnotpresent[project?.name].length) {
              const header = `Units not present in project ${project?.name}`;
              let msg = '';
              Unitsnotpresent[project?.name].forEach((element:DamacUnits) => {
                msg += element.unitNumber + ', ';
              });

              const webhookUrl = process.env.SLACK_WEBHOOKURL as string;
              await sendMessageToChannel(webhookUrl, header, msg);
            }
          }

          if (result.length > 0) {
            const header = 'Units updated for project: ' + (project?.name ?? '');
            let msg = '';
            const resultChunks = chunkArray(result, 150);

            for (const chunk of resultChunks) {
              console.log(chunk);
              await Promise.all(chunk.map(async (obj: UpdateUnit) => {
                const updateData: { [key: string]: string } = {
                  _id: String(obj._id),
                };

                if (obj.status !== undefined) {
                  updateData.status = String(obj.status);
                }

                if (obj.price !== undefined) {
                  updateData.price = obj.price;
                }

                if (obj.measurement !== undefined) {
                  updateData.measurement = String(obj.measurement);
                }

                const updatedUnit = await unit.updateUnit(updateData, String(obj._id));
                if (updatedUnit) {
                  msg += updatedUnit.name + ', ';
                }
              }));
              console.log('-------------');
              const webhookUrl = process.env.SLACK_WEBHOOKURL as string;
              await sendMessageToChannel(webhookUrl, header, msg);
            }
            return result;
          }
        }
        return null;
      } catch (e) {
        logger.error('Sync Data Error:', e);
        return null;
      }
    }

    async function processTable (): Promise<resultObject | null> {
      const listOfAllProjects = await projectsModule.GetListOfAllProjects();
      const projectslist = Object.values(listOfAllProjects);

      const promises = projectslist.map((entry: Project) => {
        if (entry?.projectSettings?.metadata) {
          const metadata = entry.projectSettings.metadata as projectMetaData;
          // Find the key that matches 'drupalid' case-insensitively and get its value
          const drupalIdKey = Object.keys(metadata)
            .find((key) => key.toLowerCase() === 'drupalid');

          if (drupalIdKey) {
            const drupalIdValue = metadata[drupalIdKey];
            console.log(drupalIdValue);
            return syncData(entry, drupalIdValue);
          }
        }
        return null;
      });

      try {
        const results = await Promise.all(promises);
        const updatedProjects = results.filter((result): result is UpdateUnit[] => result !== null);
        const response:resultObject = {
          updatedUnits: updatedProjects.flat(),
          unitsnotpresent: Unitsnotpresent,
        };
        return response;
      } catch (error) {
        logger.error('Error in table processing:', error);
        return null;
      }
    }

    return processTable();
  }
}

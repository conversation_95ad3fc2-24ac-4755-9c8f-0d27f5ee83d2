options:
 logging: CLOUD_LOGGING_ONLY  # Use Cloud Logging for logs
 machineType: 'E2_HIGHCPU_8'
 
steps:
# Pull the cached image from Container Registry (with fallback for first build)
- name: 'gcr.io/cloud-builders/docker'
  entrypoint: 'bash'
  args: ['-c', 'docker pull us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api:latest || exit 0']
  id: Pulling cached image

# Build the container image with cache optimization
- name: gcr.io/cloud-builders/docker
  args: [
    'build', 
    '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api/$BRANCH_NAME:${COMMIT_SHA}',
    '--cache-from', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api:latest',
    '-f', 'Dockerfile', 
    '.'
  ]
  id: Building the container image

# Tag the image as latest for future caching
- name: 'gcr.io/cloud-builders/docker'
  args: ['tag', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api/$BRANCH_NAME:${COMMIT_SHA}', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api:latest']
  id: Tagging as latest

# Push both the tagged image and latest
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api/$BRANCH_NAME:${COMMIT_SHA}']
  id: Pushing the image to registry

- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api:latest']
  id: Pushing latest tag

# Deploy container image to Cloud Run
- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', '${_SERVICE_NAME}', '--image', 'us-central1-docker.pkg.dev/$PROJECT_ID/propvrtwt/dashboard-api/$BRANCH_NAME:${COMMIT_SHA}', '--region', 'us-central1', '--platform', 'managed', "--allow-unauthenticated"]

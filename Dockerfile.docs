# Multi-stage build for PropVR API Documentation
# Stage 1: Build documentation
FROM node:20.18.0-alpine AS builder

WORKDIR /app

COPY package*.json ./

RUN npm install && npm cache clean --force

COPY redocly.yaml ./

# Copy all documentation files and controller redoc files
# Note: Use .dockerignore.docs to control what gets copied
COPY docs/ ./docs/
COPY src/controllers/ ./src/controllers/

# Build unified documentation
RUN npx redocly bundle docs/propvr-api-main.yaml --output docs/propvr-api-bundled.yaml
RUN npx redocly build-docs docs/propvr-api-bundled.yaml --output docs/dist/index.html

# Copy images to dist directory for serving
RUN mkdir -p docs/dist/images && cp docs/images/*.png docs/dist/images/

# Stage 2: Production runtime (minimal)
FROM node:20.18.0-alpine AS runtime

WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Copy only the built documentation from builder stage
COPY --from=builder /app/docs/dist ./docs/dist

# Copy images from builder stage
COPY --from=builder /app/docs/images ./images

# Expose single port for Cloud Run
EXPOSE 4001

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Copy the server file from builder stage
COPY --from=builder /app/docs/server.js ./server.js

# Documentation is served directly from dist directory

# Install only the required dependency directly
RUN npm install express

# Start the Express server
CMD ["node", "server.js"]

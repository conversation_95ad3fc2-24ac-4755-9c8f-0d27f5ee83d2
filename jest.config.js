module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    setupFilesAfterEnv: ['<rootDir>/jest-mongodb-setup.ts'],
    collectCoverage: true, // Enable coverage collection
    collectCoverageFrom: [
        '**/*.{ts,tsx}', // Specify the file extensions to collect coverage from
        '!**/node_modules/**',
        '!**/vendor/**'
    ],
    coverageDirectory: './coverage', // Directory where coverage reports will be saved
    coverageReporters: ['text', 'lcov'],
  };